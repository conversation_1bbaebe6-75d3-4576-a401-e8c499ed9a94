{"_from": "css-line-break@^2.1.0", "_id": "css-line-break@2.1.0", "_inBundle": false, "_integrity": "sha512-FHcKFCZcAha3LwfVBhCQbW2nCNbkZXn7KVUJcsT5/P8YmfsVja0FMPJr0B903j/E69HUphKiV9iQArX8SDYA4w==", "_location": "/swt-tool-box/css-line-break", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "css-line-break@^2.1.0", "name": "css-line-break", "escapedName": "css-line-break", "rawSpec": "^2.1.0", "saveSpec": null, "fetchSpec": "^2.1.0"}, "_requiredBy": ["/swt-tool-box/html2canvas"], "_resolved": "https://registry.npmjs.org/css-line-break/-/css-line-break-2.1.0.tgz", "_shasum": "bfef660dfa6f5397ea54116bb3cb4873edbc4fa0", "_spec": "css-line-break@^2.1.0", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace\\node_modules\\swt-tool-box\\node_modules\\html2canvas", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://hertzen.com"}, "bugs": {"url": "https://github.com/niklasvh/css-line-break/issues"}, "bundleDependencies": false, "dependencies": {"utrie": "^1.0.2"}, "deprecated": false, "description": "css-line-break ==============", "devDependencies": {"@rollup/plugin-commonjs": "^19.0.0", "@rollup/plugin-node-resolve": "^13.0.0", "@rollup/plugin-typescript": "^8.2.1", "@types/mocha": "^8.2.2", "@types/node": "^16.0.0", "mocha": "9.0.2", "prettier": "^2.3.2", "rimraf": "3.0.2", "rollup": "^2.52.7", "rollup-plugin-json": "^4.0.0", "rollup-plugin-sourcemaps": "^0.6.3", "standard-version": "^9.3.0", "ts-node": "^10.0.0", "tslint": "^6.1.3", "tslint-config-prettier": "^1.18.0", "typescript": "^4.3.5"}, "homepage": "https://github.com/niklasvh/css-line-break#readme", "keywords": ["white-space", "line-break", "word-break", "word-wrap", "overflow-wrap"], "license": "MIT", "main": "dist/css-line-break.umd.js", "module": "dist/css-line-break.es5.js", "name": "css-line-break", "repository": {"type": "git", "url": "git+ssh://**************/niklasvh/css-line-break.git"}, "scripts": {"build": "tsc --module commonjs && rollup -c rollup.config.ts", "format": "prettier --write \"{src,scripts}/**/*.ts\"", "generate-tests": "ts-node scripts/generate_line_break_tests.ts", "generate-trie": "ts-node scripts/generate_line_break_trie.ts", "lint": "tslint -c tslint.json --project tsconfig.json -t codeFrame src/**/*.ts tests/**/*.ts scripts/**/*.ts", "mocha": "mocha --require ts-node/register tests/*.ts", "prebuild": "rimraf dist/", "release": "standard-version", "test": "npm run lint && npm run mocha"}, "typings": "dist/types/index.d.ts", "version": "2.1.0"}