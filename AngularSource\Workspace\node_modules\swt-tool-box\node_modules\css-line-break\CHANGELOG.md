# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

# [2.1.0](https://github.com/niklasvh/css-line-break/compare/v2.0.1...v2.1.0) (2022-01-22)


### feat

* update to use utrie dep (#20) ([18adab4](https://github.com/niklasvh/css-line-break/commit/18adab4010b54bb73add4f23c3325b27c2c13d91)), closes [#20](https://github.com/niklasvh/css-line-break/issues/20)

### fix

* source maps (#19) ([60cdede](https://github.com/niklasvh/css-line-break/commit/60cdedeaa025f685fc7002653f390233becce128)), closes [#19](https://github.com/niklasvh/css-line-break/issues/19)



## [2.0.1](https://github.com/niklasvh/css-line-break/compare/v2.0.0...v2.0.1) (2021-08-04)


### fix

* wordBreak break-word (#17) ([d615f1f](https://github.com/niklasvh/css-line-break/commit/d615f1f731c9074035d0dab843a17a64080ba7ba)), closes [#17](https://github.com/niklasvh/css-line-break/issues/17)



# [2.0.0](https://github.com/niklasvh/css-line-break/compare/v1.1.3-0...v2.0.0) (2021-08-02)


### fix

* zwj emojis #2 (#16) ([a314ea3](https://github.com/niklasvh/css-line-break/commit/a314ea33768cde9dab4e673d3339d6b4f9c32196)), closes [#2](https://github.com/niklasvh/css-line-break/issues/2) [#16](https://github.com/niklasvh/css-line-break/issues/16)



## [1.1.3-0](https://github.com/niklasvh/css-line-break/compare/v1.1.2-0...v1.1.3-0) (2021-07-15)


### deps

* update deps (#14) ([330cb73](https://github.com/niklasvh/css-line-break/commit/330cb734f635d4d5e0d61ea991651d6d49b03054)), closes [#14](https://github.com/niklasvh/css-line-break/issues/14)

### docs

* fix readme (#13) ([1f4a330](https://github.com/niklasvh/css-line-break/commit/1f4a3300752c8bbf5a0138c7924b231161f1e4ac)), closes [#13](https://github.com/niklasvh/css-line-break/issues/13) [#10](https://github.com/niklasvh/css-line-break/issues/10)

### feat

* implement line-break.txt v13 (#15) ([bc95c80](https://github.com/niklasvh/css-line-break/commit/bc95c809e12613a9531b7985450c6bc96717e8de)), closes [#15](https://github.com/niklasvh/css-line-break/issues/15)



## [1.1.2-0](https://github.com/niklasvh/css-line-break/compare/v1.1.1...v1.1.2-0) (2021-07-04)


### ci

* update to use github actions (#12) ([7aed118](https://github.com/niklasvh/css-line-break/commit/7aed11880975b6faf6e46caed93b6d225babd943)), closes [#12](https://github.com/niklasvh/css-line-break/issues/12)
