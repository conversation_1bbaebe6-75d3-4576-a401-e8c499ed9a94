# Matching Process Flowcharts

This folder contains comprehensive flowcharts for the PK_MATCHING_PROCESS Oracle package.

## Files

### 📋 `index.html` - Main Navigation
- Landing page with overview of all flowcharts
- Click on any card to view the specific flowchart

### 🔄 `01_main_matching_process.html` - Main Process Flow
- Complete overview of the matching process
- 11-position iteration cycle
- Source and target processing logic
- Major decision points and branching

### ⚙️ `02_detailed_conditions_decisions.html` - Detailed Conditions
- Configuration parameters with defaults
- Position level determination logic (CNTR1 = 1 to 11)
- Target check strategy selection
- Cross-reference validation rules

### 🎯 `03_quality_calculation_matching.html` - Quality & Matching Logic
- Quality scoring system (A=5, B=4, C=3, D=2, E=1)
- Multi-factor quality assessment
- Match action processing (A,B,C,D,E,N)
- Amount total processing logic

## Features

✅ **Fixed Issues:**
- Updated to Mermaid v10.9.0 for better compatibility
- Added zoom controls (Zoom In, Zoom Out, Reset)
- Fixed syntax errors in flowcharts
- Improved responsive design
- Added scrollable containers for large diagrams

✅ **Zoom Controls:**
- **Zoom In**: Increase diagram size by 20%
- **Zoom Out**: Decrease diagram size by 20%
- **Reset**: Return to original size
- **Scroll**: Use scrollbars to navigate large diagrams

## How to Use

1. Open `index.html` in your browser
2. Click on any flowchart card to view that diagram
3. Use the zoom controls in the top-right corner of each diagram
4. Scroll within the diagram container to see different parts

## Browser Compatibility

- Chrome (recommended)
- Firefox
- Safari
- Edge

## Key Process Information

### Position Processing Order
1. **Positions 1-8**: Standard internal positions (highest to lowest)
2. **Position 9**: Second-stage processing for highest internal position
3. **Position 10**: Pre-advice position processing
4. **Position 11**: Other stage processing for outstanding movements

### Quality Scoring
- **A = 5**: Perfect match
- **B = 4**: Good match
- **C = 3**: Fair match
- **D = 2**: Poor match
- **E = 1**: Bad match

### Match Actions
- **A**: Auto Match
- **B**: Offer Match
- **C**: Confirm Match
- **D**: Decline Match
- **E**: Exception Match
- **N**: No Action

### Configuration Parameters (with defaults)
- `POS_LEVEL_THRESHOLD`: 6
- `DEBUG`: N
- `PREADVICE_SEARCH_ALWAYS`: N
- `PREADVICE_PREDICT_STRATEGY`: 1
- `ACC_LINKING_EXEMPTION_LEVEL`: 7
- `ENFORCE_REFERENCE_SELECTION`: N
- `PREADVICE_SEARCH_POSITIONS`: 6,4
- `EUR_DAYS_AHEAD`: 0
- `OTHER_CURRENCIES_DAYS_AHEAD`: 7
