{"_from": "file:swt-tool-box-1.0.0.tgz", "_id": "swt-tool-box@1.0.0", "_inBundle": false, "_integrity": "sha512-s+vwRRk3sDT5s/mDsA1/9sPXa9dw46i4aU5WoNwsgKWjFSIe8vrKgvlT5d6GHIMidczRPuep0fxyTFz0d4wlmw==", "_location": "/swt-tool-box", "_phantomChildren": {"esprima": "1.2.2", "esutils": "2.0.3", "optionator": "0.8.3", "text-segmentation": "1.0.3", "utrie": "1.0.2"}, "_requested": {"type": "file", "where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace\\bin", "raw": "swt-tool-box-1.0.0.tgz", "rawSpec": "swt-tool-box-1.0.0.tgz", "saveSpec": "file:swt-tool-box-1.0.0.tgz", "fetchSpec": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace\\bin\\swt-tool-box-1.0.0.tgz"}, "_requiredBy": ["#USER"], "_resolved": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace\\bin\\swt-tool-box-1.0.0.tgz", "_shasum": "f14062f3813017c38b27dde3b88f3e2737780fed", "_spec": "swt-tool-box-1.0.0.tgz", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace\\bin", "author": {"name": "Swallow Tech Tunisia"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dependencies": {"@angular/animations": "^7.2.4", "@angular/cdk": "^7.3.2", "@angular/common": "^7.2.4", "@angular/compiler": "^7.2.4", "@angular/core": "^7.2.4", "@angular/flex-layout": "^7.0.0-beta.23", "@angular/forms": "^7.2.4", "@angular/material": "^7.3.0", "@angular/platform-browser": "^7.2.0", "@angular/platform-browser-dynamic": "^7.2.0", "@angular/router": "^7.2.0", "@ctrl/ngx-codemirror": "^1.3.9", "@ngx-translate/core": "^11.0.1", "@ngx-translate/http-loader": "^4.0.0", "@tinymce/tinymce-angular": "^3.3.1", "@types/jquery": "^3.3.4", "@types/jqueryui": "^1.12.7", "aes-js": "^3.1.2", "angular-resize-event": "^1.1.1", "angular-slickgrid": "2.24.0", "angular-tippy": "^0.0.3", "bootstrap": "^3.3.7", "chart.js": "^2.8.0", "chartjs-plugin-annotation": "^0.5.7", "classlist.js": "^1.1.20150312", "codemirror": "^5.42.2", "copyfiles": "^2.1.0", "core-js": "^2.5.4", "cross-env": "^5.2.0", "crypto-js": "^3.1.9-1", "custom-event-polyfill": "^1.0.6", "dompurify": "^1.0.8", "file-saver": "^1.3.8", "git": "^0.1.5", "highcharts": "^10.2.1", "html2canvas": "^1.4.1", "inputmask": "^4.0.6", "jquery": "^3.3.1", "jquery-ui-dist": "^1.12.1", "jquery.fancytree": "^2.31.0", "jsonpath": "^1.0.1", "moment": "^2.24.0", "moment-mini": "^2.22.1", "ng-multiselect-dropdown-angular7": "^0.1.5", "ng-select": "^1.0.1", "ng2-charts": "^2.2.3", "ng2-dnd": "^5.0.2", "ng2-file-upload": "^1.3.0", "ng5-slider": "^1.1.14", "ngx-bootstrap": "^2.0.2", "ngx-json-viewer-scrolling": "^2.3.1", "ngx-popper": "^7.0.0", "ngx-quill": "^4.6.3", "ngx-simple-modal": "^1.3.13", "pbkdf2": "^3.0.17", "popper.js": "^1.16.1", "pretty-data": "^0.40.0", "protip": "^1.4.21", "quill": "1.3.6", "resize-observer-polyfill": "^1.5.1", "rxjs": "^6.4.0", "rxjs-compat": "^6.4.0", "sass": "^1.60.0", "select2": "^4.0.6-rc.1", "slickgrid": "2.4.17", "stream": "0.0.2", "tinymce": "^5.1.5", "ts-md5": "^1.2.4", "tsickle": "^0.34.3", "tslib": "^1.9.0", "uglify-js": "^3.3.18", "web-animations-js": "^2.3.1", "xlsx": "^0.16.8", "xml-js": "^1.6.11", "xmldom": "^0.1.27", "xpath": "0.0.27", "zone.js": "~0.8.26"}, "deprecated": false, "description": "SwtToolBox: Angular library that is used by NewSmart and Smart Predict products", "devDependencies": {"@angular-devkit/build-angular": "~0.13.0", "@angular/cli": "^7.3.1", "@angular/compiler-cli": "^7.2.4", "@angular/http": "^7.2.4", "@angular/language-service": "^7.2.4", "@types/jasmine": "^3.3.8", "@types/jasminewd2": "~2.0.2", "@types/node": "^11.11.3", "codelyzer": "~4.5.0", "jasmine-core": "~2.99.1", "jasmine-spec-reporter": "^4.2.1", "karma": "^4.0.0", "karma-chrome-launcher": "^2.2.0", "karma-coverage-istanbul-reporter": "^2.0.1", "karma-jasmine": "^2.0.1", "karma-jasmine-html-reporter": "^1.4.0", "ng-packagr": "^4.4.5", "npm-run-all": "^4.1.1", "protractor": "^5.4.2", "rxjs-tslint": "^0.1.6", "ts-node": "~7.0.0", "tslint": "^5.12.1", "typescript": "3.2.4"}, "es2015": "fesm2015/swt-tool-box.js", "esm2015": "esm2015/swt-tool-box.js", "esm5": "esm5/swt-tool-box.js", "fesm2015": "fesm2015/swt-tool-box.js", "fesm5": "fesm5/swt-tool-box.js", "keywords": ["swttoolbox", "swallowtech", "newsmart", "predict", "stl"], "license": "SwallowTech STL", "main": "bundles/swt-tool-box.umd.js", "metadata": "swt-tool-box.metadata.json", "module": "fesm5/swt-tool-box.js", "name": "swt-tool-box", "private": true, "sideEffects": false, "typings": "swt-tool-box.d.ts", "version": "1.0.0"}