{"version": 3, "file": "purify.min.js", "sources": ["../src/utils.js", "../src/purify.js", "../src/tags.js", "../src/attrs.js", "../src/regexp.js"], "sourcesContent": ["const { hasOwnProperty, setPrototypeOf } = Object;\nlet { apply } = typeof Reflect !== 'undefined' && Reflect;\n\nif (!apply) {\n  apply = function(fun, thisValue, args) {\n    return fun.apply(thisValue, args);\n  };\n}\n\n/* Add properties to a lookup table */\nexport function addToSet(set, array) {\n  if (setPrototypeOf) {\n    // Make 'in' and truthy checks like <PERSON><PERSON><PERSON>(set.constructor)\n    // independent of any properties defined on Object.prototype.\n    // Prevent prototype setters from intercepting set as a this value.\n    setPrototypeOf(set, null);\n  }\n\n  let l = array.length;\n  while (l--) {\n    let element = array[l];\n    if (typeof element === 'string') {\n      const lcElement = element.toLowerCase();\n      if (lcElement !== element) {\n        // Config presets (e.g. tags.js, attrs.js) are immutable.\n        if (!Object.isFrozen(array)) {\n          array[l] = lcElement;\n        }\n\n        element = lcElement;\n      }\n    }\n\n    set[element] = true;\n  }\n\n  return set;\n}\n\n/* Shallow clone an object */\nexport function clone(object) {\n  const newObject = {};\n\n  let property;\n  for (property in object) {\n    if (apply(hasOwnProperty, object, [property])) {\n      newObject[property] = object[property];\n    }\n  }\n\n  return newObject;\n}\n", "import * as TAGS from './tags';\nimport * as ATTRS from './attrs';\nimport { addToSet, clone } from './utils';\nimport * as EXPRESSIONS from './regexp';\n\nlet { apply } = typeof Reflect !== 'undefined' && Reflect;\nconst { slice: arraySlice } = Array.prototype;\nconst { freeze } = Object;\nconst getGlobal = () => (typeof window === 'undefined' ? null : window);\n\nif (!apply) {\n  apply = function(fun, thisValue, args) {\n    return fun.apply(thisValue, args);\n  };\n}\n\n/**\n * Creates a no-op policy for internal use only.\n * Don't export this function outside this module!\n * @param {?TrustedTypePolicyFactory} trustedTypes The policy factory.\n * @param {Document} document The document object (to determine policy name suffix)\n * @return {?TrustedTypePolicy} The policy created (or null, if Trusted Types\n * are not supported).\n */\nconst _createTrustedTypesPolicy = function(trustedTypes, document) {\n  if (\n    typeof trustedTypes !== 'object' ||\n    typeof trustedTypes.createPolicy !== 'function'\n  ) {\n    return null;\n  }\n\n  // Allow the callers to control the unique policy name\n  // by adding a data-tt-policy-suffix to the script element with the DOMPurify.\n  // Policy creation with duplicate names throws in Trusted Types.\n  let suffix = null;\n  const ATTR_NAME = 'data-tt-policy-suffix';\n  if (\n    document.currentScript &&\n    document.currentScript.hasAttribute(ATTR_NAME)\n  ) {\n    suffix = document.currentScript.getAttribute(ATTR_NAME);\n  }\n\n  const policyName = 'dompurify' + (suffix ? '#' + suffix : '');\n\n  try {\n    return trustedTypes.createPolicy(policyName, {\n      createHTML(html) {\n        return html;\n      },\n    });\n  } catch (error) {\n    // Policy creation failed (most likely another DOMPurify script has\n    // already run). Skip creating the policy, as this will only cause errors\n    // if TT are enforced.\n    console.warn(\n      'TrustedTypes policy ' + policyName + ' could not be created.'\n    );\n    return null;\n  }\n};\n\nfunction createDOMPurify(window = getGlobal()) {\n  const DOMPurify = root => createDOMPurify(root);\n\n  /**\n   * Version label, exposed for easier checks\n   * if DOMPurify is up to date or not\n   */\n  DOMPurify.version = VERSION;\n\n  /**\n   * Array of elements that DOMPurify removed during sanitation.\n   * Empty if nothing was removed.\n   */\n  DOMPurify.removed = [];\n\n  if (!window || !window.document || window.document.nodeType !== 9) {\n    // Not running in a browser, provide a factory function\n    // so that you can pass your own Window\n    DOMPurify.isSupported = false;\n\n    return DOMPurify;\n  }\n\n  const originalDocument = window.document;\n  let useDOMParser = false;\n  let removeTitle = false;\n\n  let { document } = window;\n  const {\n    DocumentFragment,\n    HTMLTemplateElement,\n    Node,\n    NodeFilter,\n    NamedNodeMap = window.NamedNodeMap || window.MozNamedAttrMap,\n    Text,\n    Comment,\n    DOMParser,\n    TrustedTypes,\n  } = window;\n\n  // As per issue #47, the web-components registry is inherited by a\n  // new document created via createHTMLDocument. As per the spec\n  // (http://w3c.github.io/webcomponents/spec/custom/#creating-and-passing-registries)\n  // a new empty registry is used when creating a template contents owner\n  // document, so we use that as our parent document to ensure nothing\n  // is inherited.\n  if (typeof HTMLTemplateElement === 'function') {\n    const template = document.createElement('template');\n    if (template.content && template.content.ownerDocument) {\n      document = template.content.ownerDocument;\n    }\n  }\n\n  const trustedTypesPolicy = _createTrustedTypesPolicy(\n    TrustedTypes,\n    originalDocument\n  );\n  const emptyHTML = trustedTypesPolicy ? trustedTypesPolicy.createHTML('') : '';\n\n  const {\n    implementation,\n    createNodeIterator,\n    getElementsByTagName,\n    createDocumentFragment,\n  } = document;\n  const { importNode } = originalDocument;\n\n  let hooks = {};\n\n  /**\n   * Expose whether this browser supports running the full DOMPurify.\n   */\n  DOMPurify.isSupported =\n    implementation &&\n    typeof implementation.createHTMLDocument !== 'undefined' &&\n    document.documentMode !== 9;\n\n  const {\n    MUSTACHE_EXPR,\n    ERB_EXPR,\n    DATA_ATTR,\n    ARIA_ATTR,\n    IS_SCRIPT_OR_DATA,\n    ATTR_WHITESPACE,\n  } = EXPRESSIONS;\n\n  let { IS_ALLOWED_URI } = EXPRESSIONS;\n  /**\n   * We consider the elements and attributes below to be safe. Ideally\n   * don't add any new ones but feel free to remove unwanted ones.\n   */\n\n  /* allowed element names */\n  let ALLOWED_TAGS = null;\n  const DEFAULT_ALLOWED_TAGS = addToSet({}, [\n    ...TAGS.html,\n    ...TAGS.svg,\n    ...TAGS.svgFilters,\n    ...TAGS.mathMl,\n    ...TAGS.text,\n  ]);\n\n  /* Allowed attribute names */\n  let ALLOWED_ATTR = null;\n  const DEFAULT_ALLOWED_ATTR = addToSet({}, [\n    ...ATTRS.html,\n    ...ATTRS.svg,\n    ...ATTRS.mathMl,\n    ...ATTRS.xml,\n  ]);\n\n  /* Explicitly forbidden tags (overrides ALLOWED_TAGS/ADD_TAGS) */\n  let FORBID_TAGS = null;\n\n  /* Explicitly forbidden attributes (overrides ALLOWED_ATTR/ADD_ATTR) */\n  let FORBID_ATTR = null;\n\n  /* Decide if ARIA attributes are okay */\n  let ALLOW_ARIA_ATTR = true;\n\n  /* Decide if custom data attributes are okay */\n  let ALLOW_DATA_ATTR = true;\n\n  /* Decide if unknown protocols are okay */\n  let ALLOW_UNKNOWN_PROTOCOLS = false;\n\n  /* Output should be safe for jQuery's $() factory? */\n  let SAFE_FOR_JQUERY = false;\n\n  /* Output should be safe for common template engines.\n   * This means, DOMPurify removes data attributes, mustaches and ERB\n   */\n  let SAFE_FOR_TEMPLATES = false;\n\n  /* Decide if document with <html>... should be returned */\n  let WHOLE_DOCUMENT = false;\n\n  /* Track whether config is already set on this instance of DOMPurify. */\n  let SET_CONFIG = false;\n\n  /* Decide if all elements (e.g. style, script) must be children of\n   * document.body. By default, browsers might move them to document.head */\n  let FORCE_BODY = false;\n\n  /* Decide if a DOM `HTMLBodyElement` should be returned, instead of a html\n   * string (or a TrustedHTML object if Trusted Types are supported).\n   * If `WHOLE_DOCUMENT` is enabled a `HTMLHtmlElement` will be returned instead\n   */\n  let RETURN_DOM = false;\n\n  /* Decide if a DOM `DocumentFragment` should be returned, instead of a html\n   * string  (or a TrustedHTML object if Trusted Types are supported) */\n  let RETURN_DOM_FRAGMENT = false;\n\n  /* If `RETURN_DOM` or `RETURN_DOM_FRAGMENT` is enabled, decide if the returned DOM\n   * `Node` is imported into the current `Document`. If this flag is not enabled the\n   * `Node` will belong (its ownerDocument) to a fresh `HTMLDocument`, created by\n   * DOMPurify. */\n  let RETURN_DOM_IMPORT = false;\n\n  /* Output should be free from DOM clobbering attacks? */\n  let SANITIZE_DOM = true;\n\n  /* Keep element content when removing element? */\n  let KEEP_CONTENT = true;\n\n  /* If a `Node` is passed to sanitize(), then performs sanitization in-place instead\n   * of importing it into a new Document and returning a sanitized copy */\n  let IN_PLACE = false;\n\n  /* Allow usage of profiles like html, svg and mathMl */\n  let USE_PROFILES = {};\n\n  /* Tags to ignore content of when KEEP_CONTENT is true */\n  const FORBID_CONTENTS = addToSet({}, [\n    'audio',\n    'head',\n    'math',\n    'script',\n    'style',\n    'template',\n    'svg',\n    'video',\n  ]);\n\n  /* Tags that are safe for data: URIs */\n  const DATA_URI_TAGS = addToSet({}, [\n    'audio',\n    'video',\n    'img',\n    'source',\n    'image',\n  ]);\n\n  /* Attributes safe for values like \"javascript:\" */\n  let URI_SAFE_ATTRIBUTES = null;\n  const DEFAULT_URI_SAFE_ATTRIBUTES = addToSet({}, [\n    'alt',\n    'class',\n    'for',\n    'id',\n    'label',\n    'name',\n    'pattern',\n    'placeholder',\n    'summary',\n    'title',\n    'value',\n    'style',\n    'xmlns',\n  ]);\n\n  /* Keep a reference to config to pass to hooks */\n  let CONFIG = null;\n\n  /* Ideally, do not touch anything below this line */\n  /* ______________________________________________ */\n\n  const formElement = document.createElement('form');\n\n  /**\n   * _parseConfig\n   *\n   * @param  {Object} cfg optional config literal\n   */\n  // eslint-disable-next-line complexity\n  const _parseConfig = function(cfg) {\n    if (CONFIG && CONFIG === cfg) {\n      return;\n    }\n\n    /* Shield configuration object from tampering */\n    if (!cfg || typeof cfg !== 'object') {\n      cfg = {};\n    }\n\n    /* Set configuration parameters */\n    ALLOWED_TAGS =\n      'ALLOWED_TAGS' in cfg\n        ? addToSet({}, cfg.ALLOWED_TAGS)\n        : DEFAULT_ALLOWED_TAGS;\n    ALLOWED_ATTR =\n      'ALLOWED_ATTR' in cfg\n        ? addToSet({}, cfg.ALLOWED_ATTR)\n        : DEFAULT_ALLOWED_ATTR;\n    URI_SAFE_ATTRIBUTES =\n      'ADD_URI_SAFE_ATTR' in cfg\n        ? addToSet({}, cfg.ADD_URI_SAFE_ATTR)\n        : DEFAULT_URI_SAFE_ATTRIBUTES;\n    FORBID_TAGS = 'FORBID_TAGS' in cfg ? addToSet({}, cfg.FORBID_TAGS) : {};\n    FORBID_ATTR = 'FORBID_ATTR' in cfg ? addToSet({}, cfg.FORBID_ATTR) : {};\n    USE_PROFILES = 'USE_PROFILES' in cfg ? cfg.USE_PROFILES : false;\n    ALLOW_ARIA_ATTR = cfg.ALLOW_ARIA_ATTR !== false; // Default true\n    ALLOW_DATA_ATTR = cfg.ALLOW_DATA_ATTR !== false; // Default true\n    ALLOW_UNKNOWN_PROTOCOLS = cfg.ALLOW_UNKNOWN_PROTOCOLS || false; // Default false\n    SAFE_FOR_JQUERY = cfg.SAFE_FOR_JQUERY || false; // Default false\n    SAFE_FOR_TEMPLATES = cfg.SAFE_FOR_TEMPLATES || false; // Default false\n    WHOLE_DOCUMENT = cfg.WHOLE_DOCUMENT || false; // Default false\n    RETURN_DOM = cfg.RETURN_DOM || false; // Default false\n    RETURN_DOM_FRAGMENT = cfg.RETURN_DOM_FRAGMENT || false; // Default false\n    RETURN_DOM_IMPORT = cfg.RETURN_DOM_IMPORT || false; // Default false\n    FORCE_BODY = cfg.FORCE_BODY || false; // Default false\n    SANITIZE_DOM = cfg.SANITIZE_DOM !== false; // Default true\n    KEEP_CONTENT = cfg.KEEP_CONTENT !== false; // Default true\n    IN_PLACE = cfg.IN_PLACE || false; // Default false\n\n    IS_ALLOWED_URI = cfg.ALLOWED_URI_REGEXP || IS_ALLOWED_URI;\n\n    if (SAFE_FOR_TEMPLATES) {\n      ALLOW_DATA_ATTR = false;\n    }\n\n    if (RETURN_DOM_FRAGMENT) {\n      RETURN_DOM = true;\n    }\n\n    /* Parse profile info */\n    if (USE_PROFILES) {\n      ALLOWED_TAGS = addToSet({}, [...TAGS.text]);\n      ALLOWED_ATTR = [];\n      if (USE_PROFILES.html === true) {\n        addToSet(ALLOWED_TAGS, TAGS.html);\n        addToSet(ALLOWED_ATTR, ATTRS.html);\n      }\n\n      if (USE_PROFILES.svg === true) {\n        addToSet(ALLOWED_TAGS, TAGS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n\n      if (USE_PROFILES.svgFilters === true) {\n        addToSet(ALLOWED_TAGS, TAGS.svgFilters);\n        addToSet(ALLOWED_ATTR, ATTRS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n\n      if (USE_PROFILES.mathMl === true) {\n        addToSet(ALLOWED_TAGS, TAGS.mathMl);\n        addToSet(ALLOWED_ATTR, ATTRS.mathMl);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n    }\n\n    /* Merge configuration parameters */\n    if (cfg.ADD_TAGS) {\n      if (ALLOWED_TAGS === DEFAULT_ALLOWED_TAGS) {\n        ALLOWED_TAGS = clone(ALLOWED_TAGS);\n      }\n\n      addToSet(ALLOWED_TAGS, cfg.ADD_TAGS);\n    }\n\n    if (cfg.ADD_ATTR) {\n      if (ALLOWED_ATTR === DEFAULT_ALLOWED_ATTR) {\n        ALLOWED_ATTR = clone(ALLOWED_ATTR);\n      }\n\n      addToSet(ALLOWED_ATTR, cfg.ADD_ATTR);\n    }\n\n    if (cfg.ADD_URI_SAFE_ATTR) {\n      addToSet(URI_SAFE_ATTRIBUTES, cfg.ADD_URI_SAFE_ATTR);\n    }\n\n    /* Add #text in case KEEP_CONTENT is set to true */\n    if (KEEP_CONTENT) {\n      ALLOWED_TAGS['#text'] = true;\n    }\n\n    /* Add html, head and body to ALLOWED_TAGS in case WHOLE_DOCUMENT is true */\n    if (WHOLE_DOCUMENT) {\n      addToSet(ALLOWED_TAGS, ['html', 'head', 'body']);\n    }\n\n    /* Add tbody to ALLOWED_TAGS in case tables are permitted, see #286 */\n    if (ALLOWED_TAGS.table) {\n      addToSet(ALLOWED_TAGS, ['tbody']);\n    }\n\n    // Prevent further manipulation of configuration.\n    // Not available in IE8, Safari 5, etc.\n    if (freeze) {\n      freeze(cfg);\n    }\n\n    CONFIG = cfg;\n  };\n\n  /**\n   * _forceRemove\n   *\n   * @param  {Node} node a DOM node\n   */\n  const _forceRemove = function(node) {\n    DOMPurify.removed.push({ element: node });\n    try {\n      node.parentNode.removeChild(node);\n    } catch (error) {\n      node.outerHTML = emptyHTML;\n    }\n  };\n\n  /**\n   * _removeAttribute\n   *\n   * @param  {String} name an Attribute name\n   * @param  {Node} node a DOM node\n   */\n  const _removeAttribute = function(name, node) {\n    try {\n      DOMPurify.removed.push({\n        attribute: node.getAttributeNode(name),\n        from: node,\n      });\n    } catch (error) {\n      DOMPurify.removed.push({\n        attribute: null,\n        from: node,\n      });\n    }\n\n    node.removeAttribute(name);\n  };\n\n  /**\n   * _initDocument\n   *\n   * @param  {String} dirty a string of dirty markup\n   * @return {Document} a DOM, filled with the dirty markup\n   */\n  const _initDocument = function(dirty) {\n    /* Create a HTML document */\n    let doc;\n    let leadingWhitespace;\n\n    if (FORCE_BODY) {\n      dirty = '<remove></remove>' + dirty;\n    } else {\n      /* If FORCE_BODY isn't used, leading whitespace needs to be preserved manually */\n      const matches = dirty.match(/^[\\s]+/);\n      leadingWhitespace = matches && matches[0];\n      if (leadingWhitespace) {\n        dirty = dirty.slice(leadingWhitespace.length);\n      }\n    }\n\n    /* Use DOMParser to workaround Firefox bug (see comment below) */\n    if (useDOMParser) {\n      try {\n        doc = new DOMParser().parseFromString(dirty, 'text/html');\n      } catch (error) {}\n    }\n\n    /* Remove title to fix a mXSS bug in older MS Edge */\n    if (removeTitle) {\n      addToSet(FORBID_TAGS, ['title']);\n    }\n\n    /* Otherwise use createHTMLDocument, because DOMParser is unsafe in\n    Safari (see comment below) */\n    if (!doc || !doc.documentElement) {\n      doc = implementation.createHTMLDocument('');\n      const { body } = doc;\n      body.parentNode.removeChild(body.parentNode.firstElementChild);\n      body.outerHTML = trustedTypesPolicy\n        ? trustedTypesPolicy.createHTML(dirty)\n        : dirty;\n    }\n\n    if (leadingWhitespace) {\n      doc.body.insertBefore(\n        document.createTextNode(leadingWhitespace),\n        doc.body.childNodes[0] || null\n      );\n    }\n\n    /* Work on whole document or just its body */\n    return getElementsByTagName.call(doc, WHOLE_DOCUMENT ? 'html' : 'body')[0];\n  };\n\n  // Firefox uses a different parser for innerHTML rather than\n  // DOMParser (see https://bugzilla.mozilla.org/show_bug.cgi?id=1205631)\n  // which means that you *must* use DOMParser, otherwise the output may\n  // not be safe if used in a document.write context later.\n  //\n  // So we feature detect the Firefox bug and use the DOMParser if necessary.\n  //\n  // MS Edge, in older versions, is affected by an mXSS behavior. The second\n  // check tests for the behavior and fixes it if necessary.\n  if (DOMPurify.isSupported) {\n    (function() {\n      try {\n        const doc = _initDocument(\n          '<svg><p><style><img src=\"</style><img src=x onerror=1//\">'\n        );\n        if (doc.querySelector('svg img')) {\n          useDOMParser = true;\n        }\n      } catch (error) {}\n    })();\n\n    (function() {\n      try {\n        const doc = _initDocument('<x/><title>&lt;/title&gt;&lt;img&gt;');\n        if (doc.querySelector('title').innerHTML.match(/<\\/title/)) {\n          removeTitle = true;\n        }\n      } catch (error) {}\n    })();\n  }\n\n  /**\n   * _createIterator\n   *\n   * @param  {Document} root document/fragment to create iterator for\n   * @return {Iterator} iterator instance\n   */\n  const _createIterator = function(root) {\n    return createNodeIterator.call(\n      root.ownerDocument || root,\n      root,\n      NodeFilter.SHOW_ELEMENT | NodeFilter.SHOW_COMMENT | NodeFilter.SHOW_TEXT,\n      () => {\n        return NodeFilter.FILTER_ACCEPT;\n      },\n      false\n    );\n  };\n\n  /**\n   * _isClobbered\n   *\n   * @param  {Node} elm element to check for clobbering attacks\n   * @return {Boolean} true if clobbered, false if safe\n   */\n  const _isClobbered = function(elm) {\n    if (elm instanceof Text || elm instanceof Comment) {\n      return false;\n    }\n\n    if (\n      typeof elm.nodeName !== 'string' ||\n      typeof elm.textContent !== 'string' ||\n      typeof elm.removeChild !== 'function' ||\n      !(elm.attributes instanceof NamedNodeMap) ||\n      typeof elm.removeAttribute !== 'function' ||\n      typeof elm.setAttribute !== 'function'\n    ) {\n      return true;\n    }\n\n    return false;\n  };\n\n  /**\n   * _isNode\n   *\n   * @param  {Node} obj object to check whether it's a DOM node\n   * @return {Boolean} true is object is a DOM node\n   */\n  const _isNode = function(obj) {\n    return typeof Node === 'object'\n      ? obj instanceof Node\n      : obj &&\n          typeof obj === 'object' &&\n          typeof obj.nodeType === 'number' &&\n          typeof obj.nodeName === 'string';\n  };\n\n  /**\n   * _executeHook\n   * Execute user configurable hooks\n   *\n   * @param  {String} entryPoint  Name of the hook's entry point\n   * @param  {Node} currentNode node to work on with the hook\n   * @param  {Object} data additional hook parameters\n   */\n  const _executeHook = function(entryPoint, currentNode, data) {\n    if (!hooks[entryPoint]) {\n      return;\n    }\n\n    hooks[entryPoint].forEach(hook => {\n      hook.call(DOMPurify, currentNode, data, CONFIG);\n    });\n  };\n\n  /**\n   * _sanitizeElements\n   *\n   * @protect nodeName\n   * @protect textContent\n   * @protect removeChild\n   *\n   * @param   {Node} currentNode to check for permission to exist\n   * @return  {Boolean} true if node was killed, false if left alive\n   */\n  // eslint-disable-next-line complexity\n  const _sanitizeElements = function(currentNode) {\n    let content;\n\n    /* Execute a hook if present */\n    _executeHook('beforeSanitizeElements', currentNode, null);\n\n    /* Check if element is clobbered or can clobber */\n    if (_isClobbered(currentNode)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Now let's check the element's type and name */\n    const tagName = currentNode.nodeName.toLowerCase();\n\n    /* Execute a hook if present */\n    _executeHook('uponSanitizeElement', currentNode, {\n      tagName,\n      allowedTags: ALLOWED_TAGS,\n    });\n\n    /* Remove element if anything forbids its presence */\n    if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n      /* Keep content except for black-listed elements */\n      if (\n        KEEP_CONTENT &&\n        !FORBID_CONTENTS[tagName] &&\n        typeof currentNode.insertAdjacentHTML === 'function'\n      ) {\n        try {\n          const htmlToInsert = currentNode.innerHTML;\n          currentNode.insertAdjacentHTML(\n            'AfterEnd',\n            trustedTypesPolicy\n              ? trustedTypesPolicy.createHTML(htmlToInsert)\n              : htmlToInsert\n          );\n        } catch (error) {}\n      }\n\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Remove in case a noscript/noembed XSS is suspected */\n    if (tagName === 'noscript' && currentNode.innerHTML.match(/<\\/noscript/i)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    if (tagName === 'noembed' && currentNode.innerHTML.match(/<\\/noembed/i)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Convert markup to cover jQuery behavior */\n    if (\n      SAFE_FOR_JQUERY &&\n      !currentNode.firstElementChild &&\n      (!currentNode.content || !currentNode.content.firstElementChild) &&\n      /</g.test(currentNode.textContent)\n    ) {\n      DOMPurify.removed.push({ element: currentNode.cloneNode() });\n      if (currentNode.innerHTML) {\n        currentNode.innerHTML = currentNode.innerHTML.replace(/</g, '&lt;');\n      } else {\n        currentNode.innerHTML = currentNode.textContent.replace(/</g, '&lt;');\n      }\n    }\n\n    /* Sanitize element content to be template-safe */\n    if (SAFE_FOR_TEMPLATES && currentNode.nodeType === 3) {\n      /* Get the element's text content */\n      content = currentNode.textContent;\n      content = content.replace(MUSTACHE_EXPR, ' ');\n      content = content.replace(ERB_EXPR, ' ');\n      if (currentNode.textContent !== content) {\n        DOMPurify.removed.push({ element: currentNode.cloneNode() });\n        currentNode.textContent = content;\n      }\n    }\n\n    /* Execute a hook if present */\n    _executeHook('afterSanitizeElements', currentNode, null);\n\n    return false;\n  };\n\n  /**\n   * _isValidAttribute\n   *\n   * @param  {string} lcTag Lowercase tag name of containing element.\n   * @param  {string} lcName Lowercase attribute name.\n   * @param  {string} value Attribute value.\n   * @return {Boolean} Returns true if `value` is valid, otherwise false.\n   */\n  // eslint-disable-next-line complexity\n  const _isValidAttribute = function(lcTag, lcName, value) {\n    /* Make sure attribute cannot clobber */\n    if (\n      SANITIZE_DOM &&\n      (lcName === 'id' || lcName === 'name') &&\n      (value in document || value in formElement)\n    ) {\n      return false;\n    }\n\n    /* Allow valid data-* attributes: At least one character after \"-\"\n        (https://html.spec.whatwg.org/multipage/dom.html#embedding-custom-non-visible-data-with-the-data-*-attributes)\n        XML-compatible (https://html.spec.whatwg.org/multipage/infrastructure.html#xml-compatible and http://www.w3.org/TR/xml/#d0e804)\n        We don't need to check the value; it's always URI safe. */\n    if (ALLOW_DATA_ATTR && DATA_ATTR.test(lcName)) {\n      // This attribute is safe\n    } else if (ALLOW_ARIA_ATTR && ARIA_ATTR.test(lcName)) {\n      // This attribute is safe\n      /* Otherwise, check the name is permitted */\n    } else if (!ALLOWED_ATTR[lcName] || FORBID_ATTR[lcName]) {\n      return false;\n\n      /* Check value is safe. First, is attr inert? If so, is safe */\n    } else if (URI_SAFE_ATTRIBUTES[lcName]) {\n      // This attribute is safe\n      /* Check no script, data or unknown possibly unsafe URI\n        unless we know URI values are safe for that attribute */\n    } else if (IS_ALLOWED_URI.test(value.replace(ATTR_WHITESPACE, ''))) {\n      // This attribute is safe\n      /* Keep image data URIs alive if src/xlink:href is allowed */\n      /* Further prevent gadget XSS for dynamically built script tags */\n    } else if (\n      (lcName === 'src' || lcName === 'xlink:href') &&\n      lcTag !== 'script' &&\n      value.indexOf('data:') === 0 &&\n      DATA_URI_TAGS[lcTag]\n    ) {\n      // This attribute is safe\n      /* Allow unknown protocols: This provides support for links that\n        are handled by protocol handlers which may be unknown ahead of\n        time, e.g. fb:, spotify: */\n    } else if (\n      ALLOW_UNKNOWN_PROTOCOLS &&\n      !IS_SCRIPT_OR_DATA.test(value.replace(ATTR_WHITESPACE, ''))\n    ) {\n      // This attribute is safe\n      /* Check for binary attributes */\n      // eslint-disable-next-line no-negated-condition\n    } else if (!value) {\n      // Binary attributes are safe at this point\n      /* Anything else, presume unsafe, do not add it back */\n    } else {\n      return false;\n    }\n\n    return true;\n  };\n\n  /**\n   * _sanitizeAttributes\n   *\n   * @protect attributes\n   * @protect nodeName\n   * @protect removeAttribute\n   * @protect setAttribute\n   *\n   * @param  {Node} currentNode to sanitize\n   */\n  const _sanitizeAttributes = function(currentNode) {\n    let attr;\n    let value;\n    let lcName;\n    let idAttr;\n    let l;\n    /* Execute a hook if present */\n    _executeHook('beforeSanitizeAttributes', currentNode, null);\n\n    let { attributes } = currentNode;\n\n    /* Check if we have attributes; if not we might have a text node */\n    if (!attributes) {\n      return;\n    }\n\n    const hookEvent = {\n      attrName: '',\n      attrValue: '',\n      keepAttr: true,\n      allowedAttributes: ALLOWED_ATTR,\n    };\n    l = attributes.length;\n\n    /* Go backwards over all attributes; safely remove bad ones */\n    while (l--) {\n      attr = attributes[l];\n      const { name, namespaceURI } = attr;\n      value = attr.value.trim();\n      lcName = name.toLowerCase();\n\n      /* Execute a hook if present */\n      hookEvent.attrName = lcName;\n      hookEvent.attrValue = value;\n      hookEvent.keepAttr = true;\n      _executeHook('uponSanitizeAttribute', currentNode, hookEvent);\n      value = hookEvent.attrValue;\n\n      /* Remove attribute */\n      // Safari (iOS + Mac), last tested v8.0.5, crashes if you try to\n      // remove a \"name\" attribute from an <img> tag that has an \"id\"\n      // attribute at the time.\n      if (\n        lcName === 'name' &&\n        currentNode.nodeName === 'IMG' &&\n        attributes.id\n      ) {\n        idAttr = attributes.id;\n        attributes = apply(arraySlice, attributes, []);\n        _removeAttribute('id', currentNode);\n        _removeAttribute(name, currentNode);\n        if (attributes.indexOf(idAttr) > l) {\n          currentNode.setAttribute('id', idAttr.value);\n        }\n      } else if (\n        // This works around a bug in Safari, where input[type=file]\n        // cannot be dynamically set after type has been removed\n        currentNode.nodeName === 'INPUT' &&\n        lcName === 'type' &&\n        value === 'file' &&\n        hookEvent.keepAttr &&\n        (ALLOWED_ATTR[lcName] || !FORBID_ATTR[lcName])\n      ) {\n        continue;\n      } else {\n        // This avoids a crash in Safari v9.0 with double-ids.\n        // The trick is to first set the id to be empty and then to\n        // remove the attribute\n        if (name === 'id') {\n          currentNode.setAttribute(name, '');\n        }\n\n        _removeAttribute(name, currentNode);\n      }\n\n      /* Did the hooks approve of the attribute? */\n      if (!hookEvent.keepAttr) {\n        continue;\n      }\n\n      /* Sanitize attribute content to be template-safe */\n      if (SAFE_FOR_TEMPLATES) {\n        value = value.replace(MUSTACHE_EXPR, ' ');\n        value = value.replace(ERB_EXPR, ' ');\n      }\n\n      /* Is `value` valid for this attribute? */\n      const lcTag = currentNode.nodeName.toLowerCase();\n      if (!_isValidAttribute(lcTag, lcName, value)) {\n        continue;\n      }\n\n      /* Handle invalid data-* attribute set by try-catching it */\n      try {\n        if (namespaceURI) {\n          currentNode.setAttributeNS(namespaceURI, name, value);\n        } else {\n          /* Fallback to setAttribute() for browser-unrecognized namespaces e.g. \"x-schema\". */\n          currentNode.setAttribute(name, value);\n        }\n\n        DOMPurify.removed.pop();\n      } catch (error) {}\n    }\n\n    /* Execute a hook if present */\n    _executeHook('afterSanitizeAttributes', currentNode, null);\n  };\n\n  /**\n   * _sanitizeShadowDOM\n   *\n   * @param  {DocumentFragment} fragment to iterate over recursively\n   */\n  const _sanitizeShadowDOM = function(fragment) {\n    let shadowNode;\n    const shadowIterator = _createIterator(fragment);\n\n    /* Execute a hook if present */\n    _executeHook('beforeSanitizeShadowDOM', fragment, null);\n\n    while ((shadowNode = shadowIterator.nextNode())) {\n      /* Execute a hook if present */\n      _executeHook('uponSanitizeShadowNode', shadowNode, null);\n\n      /* Sanitize tags and elements */\n      if (_sanitizeElements(shadowNode)) {\n        continue;\n      }\n\n      /* Deep shadow DOM detected */\n      if (shadowNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(shadowNode.content);\n      }\n\n      /* Check attributes, sanitize if necessary */\n      _sanitizeAttributes(shadowNode);\n    }\n\n    /* Execute a hook if present */\n    _executeHook('afterSanitizeShadowDOM', fragment, null);\n  };\n\n  /**\n   * Sanitize\n   * Public method providing core sanitation functionality\n   *\n   * @param {String|Node} dirty string or DOM node\n   * @param {Object} configuration object\n   */\n  // eslint-disable-next-line complexity\n  DOMPurify.sanitize = function(dirty, cfg) {\n    let body;\n    let importedNode;\n    let currentNode;\n    let oldNode;\n    let returnNode;\n    /* Make sure we have a string to sanitize.\n      DO NOT return early, as this will return the wrong type if\n      the user has requested a DOM object rather than a string */\n    if (!dirty) {\n      dirty = '<!-->';\n    }\n\n    /* Stringify, in case dirty is an object */\n    if (typeof dirty !== 'string' && !_isNode(dirty)) {\n      // eslint-disable-next-line no-negated-condition\n      if (typeof dirty.toString !== 'function') {\n        throw new TypeError('toString is not a function');\n      } else {\n        dirty = dirty.toString();\n        if (typeof dirty !== 'string') {\n          throw new TypeError('dirty is not a string, aborting');\n        }\n      }\n    }\n\n    /* Check we can run. Otherwise fall back or ignore */\n    if (!DOMPurify.isSupported) {\n      if (\n        typeof window.toStaticHTML === 'object' ||\n        typeof window.toStaticHTML === 'function'\n      ) {\n        if (typeof dirty === 'string') {\n          return window.toStaticHTML(dirty);\n        }\n\n        if (_isNode(dirty)) {\n          return window.toStaticHTML(dirty.outerHTML);\n        }\n      }\n\n      return dirty;\n    }\n\n    /* Assign config vars */\n    if (!SET_CONFIG) {\n      _parseConfig(cfg);\n    }\n\n    /* Clean up removed elements */\n    DOMPurify.removed = [];\n\n    if (IN_PLACE) {\n      /* No special handling necessary for in-place sanitization */\n    } else if (dirty instanceof Node) {\n      /* If dirty is a DOM element, append to an empty document to avoid\n         elements being stripped by the parser */\n      body = _initDocument('<!-->');\n      importedNode = body.ownerDocument.importNode(dirty, true);\n      if (importedNode.nodeType === 1 && importedNode.nodeName === 'BODY') {\n        /* Node is already a body, use as is */\n        body = importedNode;\n      } else if (importedNode.nodeName === 'HTML') {\n        body = importedNode;\n      } else {\n        // eslint-disable-next-line unicorn/prefer-node-append\n        body.appendChild(importedNode);\n      }\n    } else {\n      /* Exit directly if we have nothing to do */\n      if (\n        !RETURN_DOM &&\n        !SAFE_FOR_TEMPLATES &&\n        !WHOLE_DOCUMENT &&\n        dirty.indexOf('<') === -1\n      ) {\n        return trustedTypesPolicy\n          ? trustedTypesPolicy.createHTML(dirty)\n          : dirty;\n      }\n\n      /* Initialize the document to work on */\n      body = _initDocument(dirty);\n\n      /* Check we have a DOM node from the data */\n      if (!body) {\n        return RETURN_DOM ? null : emptyHTML;\n      }\n    }\n\n    /* Remove first element node (ours) if FORCE_BODY is set */\n    if (body && FORCE_BODY) {\n      _forceRemove(body.firstChild);\n    }\n\n    /* Get node iterator */\n    const nodeIterator = _createIterator(IN_PLACE ? dirty : body);\n\n    /* Now start iterating over the created document */\n    while ((currentNode = nodeIterator.nextNode())) {\n      /* Fix IE's strange behavior with manipulated textNodes #89 */\n      if (currentNode.nodeType === 3 && currentNode === oldNode) {\n        continue;\n      }\n\n      /* Sanitize tags and elements */\n      if (_sanitizeElements(currentNode)) {\n        continue;\n      }\n\n      /* Shadow DOM detected, sanitize it */\n      if (currentNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(currentNode.content);\n      }\n\n      /* Check attributes, sanitize if necessary */\n      _sanitizeAttributes(currentNode);\n\n      oldNode = currentNode;\n    }\n\n    oldNode = null;\n\n    /* If we sanitized `dirty` in-place, return it. */\n    if (IN_PLACE) {\n      return dirty;\n    }\n\n    /* Return sanitized string or DOM */\n    if (RETURN_DOM) {\n      if (RETURN_DOM_FRAGMENT) {\n        returnNode = createDocumentFragment.call(body.ownerDocument);\n\n        while (body.firstChild) {\n          // eslint-disable-next-line unicorn/prefer-node-append\n          returnNode.appendChild(body.firstChild);\n        }\n      } else {\n        returnNode = body;\n      }\n\n      if (RETURN_DOM_IMPORT) {\n        /* AdoptNode() is not used because internal state is not reset\n               (e.g. the past names map of a HTMLFormElement), this is safe\n               in theory but we would rather not risk another attack vector.\n               The state that is cloned by importNode() is explicitly defined\n               by the specs. */\n        returnNode = importNode.call(originalDocument, returnNode, true);\n      }\n\n      return returnNode;\n    }\n\n    let serializedHTML = WHOLE_DOCUMENT ? body.outerHTML : body.innerHTML;\n\n    /* Sanitize final string template-safe */\n    if (SAFE_FOR_TEMPLATES) {\n      serializedHTML = serializedHTML.replace(MUSTACHE_EXPR, ' ');\n      serializedHTML = serializedHTML.replace(ERB_EXPR, ' ');\n    }\n\n    return trustedTypesPolicy\n      ? trustedTypesPolicy.createHTML(serializedHTML)\n      : serializedHTML;\n  };\n\n  /**\n   * Public method to set the configuration once\n   * setConfig\n   *\n   * @param {Object} cfg configuration object\n   */\n  DOMPurify.setConfig = function(cfg) {\n    _parseConfig(cfg);\n    SET_CONFIG = true;\n  };\n\n  /**\n   * Public method to remove the configuration\n   * clearConfig\n   *\n   */\n  DOMPurify.clearConfig = function() {\n    CONFIG = null;\n    SET_CONFIG = false;\n  };\n\n  /**\n   * Public method to check if an attribute value is valid.\n   * Uses last set config, if any. Otherwise, uses config defaults.\n   * isValidAttribute\n   *\n   * @param  {string} tag Tag name of containing element.\n   * @param  {string} attr Attribute name.\n   * @param  {string} value Attribute value.\n   * @return {Boolean} Returns true if `value` is valid. Otherwise, returns false.\n   */\n  DOMPurify.isValidAttribute = function(tag, attr, value) {\n    /* Initialize shared config vars if necessary. */\n    if (!CONFIG) {\n      _parseConfig({});\n    }\n\n    const lcTag = tag.toLowerCase();\n    const lcName = attr.toLowerCase();\n    return _isValidAttribute(lcTag, lcName, value);\n  };\n\n  /**\n   * AddHook\n   * Public method to add DOMPurify hooks\n   *\n   * @param {String} entryPoint entry point for the hook to add\n   * @param {Function} hookFunction function to execute\n   */\n  DOMPurify.addHook = function(entryPoint, hookFunction) {\n    if (typeof hookFunction !== 'function') {\n      return;\n    }\n\n    hooks[entryPoint] = hooks[entryPoint] || [];\n    hooks[entryPoint].push(hookFunction);\n  };\n\n  /**\n   * RemoveHook\n   * Public method to remove a DOMPurify hook at a given entryPoint\n   * (pops it from the stack of hooks if more are present)\n   *\n   * @param {String} entryPoint entry point for the hook to remove\n   */\n  DOMPurify.removeHook = function(entryPoint) {\n    if (hooks[entryPoint]) {\n      hooks[entryPoint].pop();\n    }\n  };\n\n  /**\n   * RemoveHooks\n   * Public method to remove all DOMPurify hooks at a given entryPoint\n   *\n   * @param  {String} entryPoint entry point for the hooks to remove\n   */\n  DOMPurify.removeHooks = function(entryPoint) {\n    if (hooks[entryPoint]) {\n      hooks[entryPoint] = [];\n    }\n  };\n\n  /**\n   * RemoveAllHooks\n   * Public method to remove all DOMPurify hooks\n   *\n   */\n  DOMPurify.removeAllHooks = function() {\n    hooks = {};\n  };\n\n  return DOMPurify;\n}\n\nexport default createDOMPurify();\n", "const freeze =\n  Object.freeze ||\n  function(x) {\n    return x;\n  };\n\nexport const html = freeze([\n  'a',\n  'abbr',\n  'acronym',\n  'address',\n  'area',\n  'article',\n  'aside',\n  'audio',\n  'b',\n  'bdi',\n  'bdo',\n  'big',\n  'blink',\n  'blockquote',\n  'body',\n  'br',\n  'button',\n  'canvas',\n  'caption',\n  'center',\n  'cite',\n  'code',\n  'col',\n  'colgroup',\n  'content',\n  'data',\n  'datalist',\n  'dd',\n  'decorator',\n  'del',\n  'details',\n  'dfn',\n  'dir',\n  'div',\n  'dl',\n  'dt',\n  'element',\n  'em',\n  'fieldset',\n  'figcaption',\n  'figure',\n  'font',\n  'footer',\n  'form',\n  'h1',\n  'h2',\n  'h3',\n  'h4',\n  'h5',\n  'h6',\n  'head',\n  'header',\n  'hgroup',\n  'hr',\n  'html',\n  'i',\n  'img',\n  'input',\n  'ins',\n  'kbd',\n  'label',\n  'legend',\n  'li',\n  'main',\n  'map',\n  'mark',\n  'marquee',\n  'menu',\n  'menuitem',\n  'meter',\n  'nav',\n  'nobr',\n  'ol',\n  'optgroup',\n  'option',\n  'output',\n  'p',\n  'pre',\n  'progress',\n  'q',\n  'rp',\n  'rt',\n  'ruby',\n  's',\n  'samp',\n  'section',\n  'select',\n  'shadow',\n  'small',\n  'source',\n  'spacer',\n  'span',\n  'strike',\n  'strong',\n  'style',\n  'sub',\n  'summary',\n  'sup',\n  'table',\n  'tbody',\n  'td',\n  'template',\n  'textarea',\n  'tfoot',\n  'th',\n  'thead',\n  'time',\n  'tr',\n  'track',\n  'tt',\n  'u',\n  'ul',\n  'var',\n  'video',\n  'wbr',\n]);\n\n// SVG\nexport const svg = freeze([\n  'svg',\n  'a',\n  'altglyph',\n  'altglyphdef',\n  'altglyphitem',\n  'animatecolor',\n  'animatemotion',\n  'animatetransform',\n  'audio',\n  'canvas',\n  'circle',\n  'clippath',\n  'defs',\n  'desc',\n  'ellipse',\n  'filter',\n  'font',\n  'g',\n  'glyph',\n  'glyphref',\n  'hkern',\n  'image',\n  'line',\n  'lineargradient',\n  'marker',\n  'mask',\n  'metadata',\n  'mpath',\n  'path',\n  'pattern',\n  'polygon',\n  'polyline',\n  'radialgradient',\n  'rect',\n  'stop',\n  'style',\n  'switch',\n  'symbol',\n  'text',\n  'textpath',\n  'title',\n  'tref',\n  'tspan',\n  'video',\n  'view',\n  'vkern',\n]);\n\nexport const svgFilters = freeze([\n  'feBlend',\n  'feColorMatrix',\n  'feComponentTransfer',\n  'feComposite',\n  'feConvolveMatrix',\n  'feDiffuseLighting',\n  'feDisplacementMap',\n  'feDistantLight',\n  'feFlood',\n  'feFuncA',\n  'feFuncB',\n  'feFuncG',\n  'feFuncR',\n  'feGaussianBlur',\n  'feMerge',\n  'feMergeNode',\n  'feMorphology',\n  'feOffset',\n  'fePointLight',\n  'feSpecularLighting',\n  'feSpotLight',\n  'feTile',\n  'feTurbulence',\n]);\n\nexport const mathMl = freeze([\n  'math',\n  'menclose',\n  'merror',\n  'mfenced',\n  'mfrac',\n  'mglyph',\n  'mi',\n  'mlabeledtr',\n  'mmultiscripts',\n  'mn',\n  'mo',\n  'mover',\n  'mpadded',\n  'mphantom',\n  'mroot',\n  'mrow',\n  'ms',\n  'mspace',\n  'msqrt',\n  'mstyle',\n  'msub',\n  'msup',\n  'msubsup',\n  'mtable',\n  'mtd',\n  'mtext',\n  'mtr',\n  'munder',\n  'munderover',\n]);\n\nexport const text = freeze(['#text']);\n", "const freeze =\n  Object.freeze ||\n  function(x) {\n    return x;\n  };\n\nexport const html = freeze([\n  'accept',\n  'action',\n  'align',\n  'alt',\n  'autocomplete',\n  'background',\n  'bgcolor',\n  'border',\n  'cellpadding',\n  'cellspacing',\n  'checked',\n  'cite',\n  'class',\n  'clear',\n  'color',\n  'cols',\n  'colspan',\n  'controls',\n  'coords',\n  'crossorigin',\n  'datetime',\n  'default',\n  'dir',\n  'disabled',\n  'download',\n  'enctype',\n  'face',\n  'for',\n  'headers',\n  'height',\n  'hidden',\n  'high',\n  'href',\n  'hreflang',\n  'id',\n  'integrity',\n  'ismap',\n  'label',\n  'lang',\n  'list',\n  'loop',\n  'low',\n  'max',\n  'maxlength',\n  'media',\n  'method',\n  'min',\n  'multiple',\n  'name',\n  'noshade',\n  'novalidate',\n  'nowrap',\n  'open',\n  'optimum',\n  'pattern',\n  'placeholder',\n  'poster',\n  'preload',\n  'pubdate',\n  'radiogroup',\n  'readonly',\n  'rel',\n  'required',\n  'rev',\n  'reversed',\n  'role',\n  'rows',\n  'rowspan',\n  'spellcheck',\n  'scope',\n  'selected',\n  'shape',\n  'size',\n  'sizes',\n  'span',\n  'srclang',\n  'start',\n  'src',\n  'srcset',\n  'step',\n  'style',\n  'summary',\n  'tabindex',\n  'title',\n  'type',\n  'usemap',\n  'valign',\n  'value',\n  'width',\n  'xmlns',\n]);\n\nexport const svg = freeze([\n  'accent-height',\n  'accumulate',\n  'additive',\n  'alignment-baseline',\n  'ascent',\n  'attributename',\n  'attributetype',\n  'azimuth',\n  'basefrequency',\n  'baseline-shift',\n  'begin',\n  'bias',\n  'by',\n  'class',\n  'clip',\n  'clip-path',\n  'clip-rule',\n  'color',\n  'color-interpolation',\n  'color-interpolation-filters',\n  'color-profile',\n  'color-rendering',\n  'cx',\n  'cy',\n  'd',\n  'dx',\n  'dy',\n  'diffuseconstant',\n  'direction',\n  'display',\n  'divisor',\n  'dur',\n  'edgemode',\n  'elevation',\n  'end',\n  'fill',\n  'fill-opacity',\n  'fill-rule',\n  'filter',\n  'filterunits',\n  'flood-color',\n  'flood-opacity',\n  'font-family',\n  'font-size',\n  'font-size-adjust',\n  'font-stretch',\n  'font-style',\n  'font-variant',\n  'font-weight',\n  'fx',\n  'fy',\n  'g1',\n  'g2',\n  'glyph-name',\n  'glyphref',\n  'gradientunits',\n  'gradienttransform',\n  'height',\n  'href',\n  'id',\n  'image-rendering',\n  'in',\n  'in2',\n  'k',\n  'k1',\n  'k2',\n  'k3',\n  'k4',\n  'kerning',\n  'keypoints',\n  'keysplines',\n  'keytimes',\n  'lang',\n  'lengthadjust',\n  'letter-spacing',\n  'kernelmatrix',\n  'kernelunitlength',\n  'lighting-color',\n  'local',\n  'marker-end',\n  'marker-mid',\n  'marker-start',\n  'markerheight',\n  'markerunits',\n  'markerwidth',\n  'maskcontentunits',\n  'maskunits',\n  'max',\n  'mask',\n  'media',\n  'method',\n  'mode',\n  'min',\n  'name',\n  'numoctaves',\n  'offset',\n  'operator',\n  'opacity',\n  'order',\n  'orient',\n  'orientation',\n  'origin',\n  'overflow',\n  'paint-order',\n  'path',\n  'pathlength',\n  'patterncontentunits',\n  'patterntransform',\n  'patternunits',\n  'points',\n  'preservealpha',\n  'preserveaspectratio',\n  'primitiveunits',\n  'r',\n  'rx',\n  'ry',\n  'radius',\n  'refx',\n  'refy',\n  'repeatcount',\n  'repeatdur',\n  'restart',\n  'result',\n  'rotate',\n  'scale',\n  'seed',\n  'shape-rendering',\n  'specularconstant',\n  'specularexponent',\n  'spreadmethod',\n  'stddeviation',\n  'stitchtiles',\n  'stop-color',\n  'stop-opacity',\n  'stroke-dasharray',\n  'stroke-dashoffset',\n  'stroke-linecap',\n  'stroke-linejoin',\n  'stroke-miterlimit',\n  'stroke-opacity',\n  'stroke',\n  'stroke-width',\n  'style',\n  'surfacescale',\n  'tabindex',\n  'targetx',\n  'targety',\n  'transform',\n  'text-anchor',\n  'text-decoration',\n  'text-rendering',\n  'textlength',\n  'type',\n  'u1',\n  'u2',\n  'unicode',\n  'values',\n  'viewbox',\n  'visibility',\n  'version',\n  'vert-adv-y',\n  'vert-origin-x',\n  'vert-origin-y',\n  'width',\n  'word-spacing',\n  'wrap',\n  'writing-mode',\n  'xchannelselector',\n  'ychannelselector',\n  'x',\n  'x1',\n  'x2',\n  'xmlns',\n  'y',\n  'y1',\n  'y2',\n  'z',\n  'zoomandpan',\n]);\n\nexport const mathMl = freeze([\n  'accent',\n  'accentunder',\n  'align',\n  'bevelled',\n  'close',\n  'columnsalign',\n  'columnlines',\n  'columnspan',\n  'denomalign',\n  'depth',\n  'dir',\n  'display',\n  'displaystyle',\n  'fence',\n  'frame',\n  'height',\n  'href',\n  'id',\n  'largeop',\n  'length',\n  'linethickness',\n  'lspace',\n  'lquote',\n  'mathbackground',\n  'mathcolor',\n  'mathsize',\n  'mathvariant',\n  'maxsize',\n  'minsize',\n  'movablelimits',\n  'notation',\n  'numalign',\n  'open',\n  'rowalign',\n  'rowlines',\n  'rowspacing',\n  'rowspan',\n  'rspace',\n  'rquote',\n  'scriptlevel',\n  'scriptminsize',\n  'scriptsizemultiplier',\n  'selection',\n  'separator',\n  'separators',\n  'stretchy',\n  'subscriptshift',\n  'supscriptshift',\n  'symmetric',\n  'voffset',\n  'width',\n  'xmlns',\n]);\n\nexport const xml = freeze([\n  'xlink:href',\n  'xml:id',\n  'xlink:title',\n  'xml:space',\n  'xmlns:xlink',\n]);\n", "const seal =\n  Object.seal ||\n  function(x) {\n    return x;\n  };\n\nexport const MUSTACHE_EXPR = seal(/\\{\\{[\\s\\S]*|[\\s\\S]*\\}\\}/gm); // Specify template detection regex for SAFE_FOR_TEMPLATES mode\nexport const ERB_EXPR = seal(/<%[\\s\\S]*|[\\s\\S]*%>/gm);\nexport const DATA_ATTR = seal(/^data-[\\-\\w.\\u00B7-\\uFFFF]/); // eslint-disable-line no-useless-escape\nexport const ARIA_ATTR = seal(/^aria-[\\-\\w]+$/); // eslint-disable-line no-useless-escape\nexport const IS_ALLOWED_URI = seal(\n  /^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\\-]+(?:[^a-z+.\\-:]|$))/i // eslint-disable-line no-useless-escape\n);\nexport const IS_SCRIPT_OR_DATA = seal(/^(?:\\w+script|data):/i);\nexport const ATTR_WHITESPACE = seal(\n  /[\\u0000-\\u0020\\u00A0\\u1680\\u180E\\u2000-\\u2029\\u205f\\u3000]/g // eslint-disable-line no-control-regex\n);\n"], "names": ["addToSet", "set", "array", "setPrototypeOf", "l", "length", "element", "lcElement", "toLowerCase", "Object", "isFrozen", "clone", "object", "newObject", "property", "apply", "hasOwnProperty", "createDOMPurify", "window", "getGlobal", "DOMPurify", "root", "version", "VERSION", "removed", "document", "nodeType", "isSupported", "originalDocument", "useDOMParser", "removeTitle", "DocumentFragment", "HTMLTemplateElement", "Node", "Node<PERSON><PERSON><PERSON>", "NamedNodeMap", "MozNamedAttrMap", "Text", "Comment", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TrustedTypes", "template", "createElement", "content", "ownerDocument", "trustedTypesPolicy", "_createTrustedTypesPolicy", "emptyHTML", "createHTML", "implementation", "createNodeIterator", "getElementsByTagName", "createDocumentFragment", "importNode", "hooks", "createHTMLDocument", "documentMode", "MUSTACHE_EXPR", "EXPRESSIONS", "ERB_EXPR", "DATA_ATTR", "ARIA_ATTR", "IS_SCRIPT_OR_DATA", "ATTR_WHITESPACE", "IS_ALLOWED_URI", "ALLOWED_TAGS", "DEFAULT_ALLOWED_TAGS", "TAGS", "ALLOWED_ATTR", "DEFAULT_ALLOWED_ATTR", "ATTRS", "FORBID_TAGS", "FORBID_ATTR", "ALLOW_ARIA_ATTR", "ALLOW_DATA_ATTR", "ALLOW_UNKNOWN_PROTOCOLS", "SAFE_FOR_JQUERY", "SAFE_FOR_TEMPLATES", "WHOLE_DOCUMENT", "SET_CONFIG", "FORCE_BODY", "RETURN_DOM", "RETURN_DOM_FRAGMENT", "RETURN_DOM_IMPORT", "SANITIZE_DOM", "KEEP_CONTENT", "IN_PLACE", "USE_PROFILES", "FORBID_CONTENTS", "DATA_URI_TAGS", "URI_SAFE_ATTRIBUTES", "DEFAULT_URI_SAFE_ATTRIBUTES", "CONFIG", "formElement", "_parseConfig", "cfg", "ADD_URI_SAFE_ATTR", "ALLOWED_URI_REGEXP", "html", "svg", "svgFilters", "mathMl", "ADD_TAGS", "ADD_ATTR", "table", "freeze", "_forceRemove", "node", "push", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "error", "outerHTML", "_removeAttribute", "name", "getAttributeNode", "removeAttribute", "_initDocument", "dirty", "doc", "leadingWhitespace", "matches", "match", "slice", "parseFromString", "documentElement", "body", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "insertBefore", "createTextNode", "childNodes", "call", "querySelector", "innerHTML", "_createIterator", "SHOW_ELEMENT", "SHOW_COMMENT", "SHOW_TEXT", "FILTER_ACCEPT", "_isClobbered", "elm", "nodeName", "textContent", "attributes", "setAttribute", "_isNode", "obj", "_executeHook", "entryPoint", "currentNode", "data", "for<PERSON>ach", "_sanitizeElements", "tagName", "insertAdjacentHTML", "htmlToInsert", "test", "cloneNode", "replace", "_isValidAttribute", "lcTag", "lcName", "value", "indexOf", "_sanitizeAttributes", "attr", "idAttr", "hookEvent", "namespaceURI", "trim", "attrName", "attrValue", "keepAttr", "id", "arraySlice", "setAttributeNS", "pop", "_sanitizeShadowDOM", "fragment", "shadowNode", "shadowIterator", "nextNode", "sanitize", "importedNode", "oldNode", "returnNode", "toString", "TypeError", "_typeof", "toStaticHTML", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "nodeIterator", "serializedHTML", "setConfig", "clearConfig", "isValidAttribute", "tag", "addHook", "hookFunction", "removeH<PERSON>", "removeHooks", "removeAllHooks", "x", "text", "xml", "Reflect", "fun", "thisValue", "args", "seal", "Array", "prototype", "trustedTypes", "createPolicy", "suffix", "currentScript", "hasAttribute", "getAttribute", "policyName", "warn"], "mappings": "qLAUA,SAAgBA,EAASC,EAAKC,GACxBC,KAIaF,EAAK,cAGlBG,EAAIF,EAAMG,OACPD,KAAK,KACNE,EAAUJ,EAAME,MACG,iBAAZE,EAAsB,KACzBC,EAAYD,EAAQE,cACtBD,IAAcD,IAEXG,OAAOC,SAASR,OACbE,GAAKG,KAGHA,KAIVD,IAAW,SAGVL,EAIT,SAAgBU,EAAMC,OACdC,KAEFC,aACCA,KAAYF,EACXG,EAAMC,EAAgBJ,GAASE,QACvBA,GAAYF,EAAOE,WAI1BD,0HCaT,SAASI,QAAgBC,yDAASC,IAC1BC,EAAY,mBAAQH,EAAgBI,SAMhCC,QAAUC,WAMVC,YAELN,IAAWA,EAAOO,UAAyC,IAA7BP,EAAOO,SAASC,kBAGvCC,aAAc,EAEjBP,MAGHQ,EAAmBV,EAAOO,SAC5BI,GAAe,EACfC,GAAc,EAEZL,EAAaP,EAAbO,SAEJM,EASEb,EATFa,iBACAC,EAQEd,EARFc,oBACAC,EAOEf,EAPFe,KACAC,EAMEhB,EANFgB,aAMEhB,EALFiB,aAAAA,aAAejB,EAAOiB,cAAgBjB,EAAOkB,kBAC7CC,EAIEnB,EAJFmB,KACAC,EAGEpB,EAHFoB,QACAC,EAEErB,EAFFqB,UACAC,EACEtB,EADFsB,gBASiC,mBAAxBR,EAAoC,KACvCS,EAAWhB,EAASiB,cAAc,YACpCD,EAASE,SAAWF,EAASE,QAAQC,kBAC5BH,EAASE,QAAQC,mBAI1BC,EAAqBC,EACzBN,EACAZ,GAEImB,EAAYF,EAAqBA,EAAmBG,WAAW,IAAM,KAOvEvB,EAJFwB,IAAAA,eACAC,IAAAA,mBACAC,IAAAA,qBACAC,IAAAA,uBAEMC,EAAezB,EAAfyB,WAEJC,OAKM3B,YACRsB,QAC6C,IAAtCA,EAAeM,oBACI,IAA1B9B,EAAS+B,iBAGTC,EAMEC,EALFC,EAKED,EAJFE,GAIEF,EAHFG,GAGEH,EAFFI,GAEEJ,EADFK,GACEL,EAEEM,GAAmBN,EAOrBO,GAAe,KACbC,GAAuBlE,iBACxBmE,KACAA,KACAA,KACAA,KACAA,KAIDC,GAAe,KACbC,GAAuBrE,iBACxBsE,KACAA,KACAA,KACAA,KAIDC,GAAc,KAGdC,GAAc,KAGdC,IAAkB,EAGlBC,IAAkB,EAGlBC,IAA0B,EAG1BC,IAAkB,EAKlBC,IAAqB,EAGrBC,IAAiB,EAGjBC,IAAa,EAIbC,IAAa,EAMbC,IAAa,EAIbC,IAAsB,EAMtBC,IAAoB,EAGpBC,IAAe,EAGfC,IAAe,EAIfC,IAAW,EAGXC,MAGEC,GAAkBxF,MACtB,QACA,OACA,OACA,SACA,QACA,WACA,MACA,UAIIyF,GAAgBzF,MACpB,QACA,QACA,MACA,SACA,UAIE0F,GAAsB,KACpBC,GAA8B3F,MAClC,MACA,QACA,MACA,KACA,QACA,OACA,UACA,cACA,UACA,QACA,QACA,QACA,UAIE4F,GAAS,KAKPC,GAAcpE,EAASiB,cAAc,QAQrCoD,GAAe,SAASC,GACxBH,IAAUA,KAAWG,IAKpBA,GAAsB,qBAARA,gBAAAA,eAMjB,iBAAkBA,EACd/F,KAAa+F,EAAI9B,cACjBC,MAEJ,iBAAkB6B,EACd/F,KAAa+F,EAAI3B,cACjBC,MAEJ,sBAAuB0B,EACnB/F,KAAa+F,EAAIC,mBACjBL,MACQ,gBAAiBI,EAAM/F,KAAa+F,EAAIxB,mBACxC,gBAAiBwB,EAAM/F,KAAa+F,EAAIvB,mBACvC,iBAAkBuB,GAAMA,EAAIR,iBACD,IAAxBQ,EAAItB,oBACoB,IAAxBsB,EAAIrB,mBACIqB,EAAIpB,0BAA2B,KACvCoB,EAAInB,kBAAmB,KACpBmB,EAAIlB,qBAAsB,KAC9BkB,EAAIjB,iBAAkB,KAC1BiB,EAAId,aAAc,KACTc,EAAIb,sBAAuB,KAC7Ba,EAAIZ,oBAAqB,KAChCY,EAAIf,aAAc,MACK,IAArBe,EAAIX,iBACiB,IAArBW,EAAIV,gBACRU,EAAIT,WAAY,KAEVS,EAAIE,oBAAsBjC,GAEvCa,SACgB,GAGhBK,SACW,GAIXK,QACavF,iBAAiBmE,YAEN,IAAtBoB,GAAaW,SACNjC,GAAcE,KACdC,GAAcE,KAGA,IAArBiB,GAAaY,QACNlC,GAAcE,KACdC,GAAcE,KACdF,GAAcE,KAGO,IAA5BiB,GAAaa,eACNnC,GAAcE,KACdC,GAAcE,KACdF,GAAcE,KAGG,IAAxBiB,GAAac,WACNpC,GAAcE,KACdC,GAAcE,KACdF,GAAcE,KAKvByB,EAAIO,WACFrC,KAAiBC,QACJvD,EAAMsD,OAGdA,GAAc8B,EAAIO,WAGzBP,EAAIQ,WACFnC,KAAiBC,QACJ1D,EAAMyD,OAGdA,GAAc2B,EAAIQ,WAGzBR,EAAIC,qBACGN,GAAqBK,EAAIC,mBAIhCX,QACW,UAAW,GAItBP,MACOb,IAAe,OAAQ,OAAQ,SAItCA,GAAauC,SACNvC,IAAe,UAKtBwC,KACKV,MAGAA,IAQLW,GAAe,SAASC,KAClBnF,QAAQoF,MAAOtG,QAASqG,UAE3BE,WAAWC,YAAYH,GAC5B,MAAOI,KACFC,UAAYjE,IAUfkE,GAAmB,SAASC,EAAMP,SAE1BnF,QAAQoF,gBACLD,EAAKQ,iBAAiBD,QAC3BP,IAER,MAAOI,KACGvF,QAAQoF,gBACL,UACLD,MAILS,gBAAgBF,IASjBG,GAAgB,SAASC,OAEzBC,SACAC,YAEAxC,KACM,oBAAsBsC,MACzB,KAECG,EAAUH,EAAMI,MAAM,aACRD,GAAWA,EAAQ,QAE7BH,EAAMK,MAAMH,EAAkBnH,YAKtCwB,SAEM,IAAIU,GAAYqF,gBAAgBN,EAAO,aAC7C,MAAOP,OAIPjF,KACOyC,IAAc,WAKpBgD,IAAQA,EAAIM,gBAAiB,KAExBC,KADF7E,EAAeM,mBAAmB,KAChCuE,OACHjB,WAAWC,YAAYgB,EAAKjB,WAAWkB,qBACvCf,UAAYnE,EACbA,EAAmBG,WAAWsE,GAC9BA,SAGFE,KACEM,KAAKE,aACPvG,EAASwG,eAAeT,GACxBD,EAAIO,KAAKI,WAAW,IAAM,MAKvB/E,EAAqBgF,KAAKZ,EAAKzC,GAAiB,OAAS,QAAQ,IAYtE1D,EAAUO,6BAGI0F,GACV,6DAEMe,cAAc,gBACL,GAEjB,MAAOrB,uBAKKM,GAAc,wCAClBe,cAAc,SAASC,UAAUX,MAAM,iBAC/B,GAEhB,MAAOX,aAUPuB,GAAkB,SAASjH,UACxB6B,EAAmBiF,KACxB9G,EAAKuB,eAAiBvB,EACtBA,EACAa,EAAWqG,aAAerG,EAAWsG,aAAetG,EAAWuG,UAC/D,kBACSvG,EAAWwG,gBAEpB,IAUEC,GAAe,SAASC,WACxBA,aAAevG,GAAQuG,aAAetG,MAKhB,iBAAjBsG,EAAIC,UACgB,iBAApBD,EAAIE,aACgB,mBAApBF,EAAI9B,aACT8B,EAAIG,sBAAsB5G,GACG,mBAAxByG,EAAIxB,iBACiB,mBAArBwB,EAAII,eAcTC,GAAU,SAASC,SACA,qBAATjH,gBAAAA,IACViH,aAAejH,EACfiH,GACiB,qBAARA,gBAAAA,KACiB,iBAAjBA,EAAIxH,UACa,iBAAjBwH,EAAIL,UAWbM,GAAe,SAASC,EAAYC,EAAaC,GAChDhG,EAAM8F,MAILA,GAAYG,QAAQ,cACnBpB,KAAK/G,EAAWiI,EAAaC,EAAM1D,OAetC4D,GAAoB,SAASH,OAC7B1G,eAGS,yBAA0B0G,EAAa,MAGhDV,GAAaU,aACFA,IACN,MAIHI,EAAUJ,EAAYR,SAASrI,oBAGxB,sBAAuB6I,yBAErBpF,MAIVA,GAAawF,IAAYlF,GAAYkF,GAAU,IAGhDpE,KACCG,GAAgBiE,IACyB,mBAAnCJ,EAAYK,2BAGXC,EAAeN,EAAYhB,YACrBqB,mBACV,WACA7G,EACIA,EAAmBG,WAAW2G,GAC9BA,GAEN,MAAO5C,cAGEsC,IACN,QAIO,aAAZI,GAA0BJ,EAAYhB,UAAUX,MAAM,oBAC3C2B,IACN,GAGO,YAAZI,GAAyBJ,EAAYhB,UAAUX,MAAM,mBAC1C2B,IACN,KAKPzE,IACCyE,EAAYtB,mBACXsB,EAAY1G,SAAY0G,EAAY1G,QAAQoF,oBAC9C,KAAK6B,KAAKP,EAAYP,iBAEZtH,QAAQoF,MAAOtG,QAAS+I,EAAYQ,cAC1CR,EAAYhB,YACFA,UAAYgB,EAAYhB,UAAUyB,QAAQ,KAAM,UAEhDzB,UAAYgB,EAAYP,YAAYgB,QAAQ,KAAM,SAK9DjF,IAA+C,IAAzBwE,EAAY3H,mBAE1B2H,EAAYP,aACJgB,QAAQrG,EAAe,MACvBqG,QAAQnG,EAAU,KAChC0F,EAAYP,cAAgBnG,MACpBnB,QAAQoF,MAAOtG,QAAS+I,EAAYQ,gBAClCf,YAAcnG,OAKjB,wBAAyB0G,EAAa,OAE5C,IAYHU,GAAoB,SAASC,EAAOC,EAAQC,MAG9C9E,KACY,OAAX6E,GAA8B,SAAXA,KACnBC,KAASzI,GAAYyI,KAASrE,WAExB,KAOLnB,IAAmBd,GAAUgG,KAAKK,SAE/B,GAAIxF,IAAmBZ,GAAU+F,KAAKK,QAGtC,CAAA,IAAK7F,GAAa6F,IAAWzF,GAAYyF,UACvC,EAGF,GAAIvE,GAAoBuE,SAIxB,GAAIjG,GAAe4F,KAAKM,EAAMJ,QAAQ/F,GAAiB,WAIvD,GACO,QAAXkG,GAA+B,eAAXA,GACX,WAAVD,GAC2B,IAA3BE,EAAMC,QAAQ,WACd1E,GAAcuE,IAMT,GACLrF,KACCb,GAAkB8F,KAAKM,EAAMJ,QAAQ/F,GAAiB,WAKlD,GAAKmG,SAIH,eAGF,GAaHE,GAAsB,SAASf,OAC/BgB,SACAH,SACAD,SACAK,SACAlK,YAES,2BAA4BiJ,EAAa,UAEhDN,EAAeM,EAAfN,cAGDA,OAICwB,YACM,aACC,aACD,oBACSnG,UAEjB2E,EAAW1I,OAGRD,KAAK,SACH2I,EAAW3I,GACV8G,IAAAA,KAAMsD,IAAAA,kBACNH,EAAKH,MAAMO,SACVvD,EAAK1G,gBAGJkK,SAAWT,IACXU,UAAYT,IACZU,UAAW,KACR,wBAAyBvB,EAAakB,KAC3CA,EAAUI,UAOL,SAAXV,GACyB,QAAzBZ,EAAYR,UACZE,EAAW8B,KAEF9B,EAAW8B,KACP9J,EAAM+J,EAAY/B,SACd,KAAMM,MACNnC,EAAMmC,GACnBN,EAAWoB,QAAQG,GAAUlK,KACnB4I,aAAa,KAAMsB,EAAOJ,WAEnC,CAAA,GAGoB,YAAbrB,UACD,SAAXoB,GACU,SAAVC,GACAK,EAAUK,WACTxG,GAAa6F,KAAYzF,GAAYyF,aAOzB,OAAT/C,KACU8B,aAAa9B,EAAM,OAGhBA,EAAMmC,MAIpBkB,EAAUK,UAKX/F,UACMqF,EAAMJ,QAAQrG,EAAe,MACvBqG,QAAQnG,EAAU,UAI5BqG,EAAQX,EAAYR,SAASrI,iBAC9BuJ,GAAkBC,EAAOC,EAAQC,OAMhCM,IACUO,eAAeP,EAActD,EAAMgD,KAGnClB,aAAa9B,EAAMgD,KAGvB1I,QAAQwJ,MAClB,MAAOjE,SAIE,0BAA2BsC,EAAa,QAQjD4B,GAAqB,SAArBA,EAA8BC,OAC9BC,SACEC,EAAiB9C,GAAgB4C,UAG1B,0BAA2BA,EAAU,MAE1CC,EAAaC,EAAeC,eAErB,yBAA0BF,EAAY,MAG/C3B,GAAkB2B,KAKlBA,EAAWxI,mBAAmBZ,KACboJ,EAAWxI,YAIZwI,OAIT,yBAA0BD,EAAU,gBAWzCI,SAAW,SAAShE,EAAOvB,OAC/B+B,SACAyD,SACAlC,SACAmC,SACAC,YAICnE,MACK,eAIW,iBAAVA,IAAuB2B,GAAQ3B,GAAQ,IAElB,mBAAnBA,EAAMoE,eACT,IAAIC,UAAU,iCAGC,mBADbrE,EAAMoE,kBAEN,IAAIC,UAAU,uCAMrBvK,EAAUO,YAAa,IAEO,WAA/BiK,EAAO1K,EAAO2K,eACiB,mBAAxB3K,EAAO2K,aACd,IACqB,iBAAVvE,SACFpG,EAAO2K,aAAavE,MAGzB2B,GAAQ3B,UACHpG,EAAO2K,aAAavE,EAAMN,kBAI9BM,KAIJvC,OACUgB,KAILvE,WAEN8D,SAEG,GAAIgC,aAAiBrF,EAKI,UAFvBoF,GAAc,gBACDzE,cAAcS,WAAWiE,GAAO,IACnC5F,UAA4C,SAA1B6J,EAAa1C,WAEvC0C,EAC4B,SAA1BA,EAAa1C,WACf0C,IAGFO,YAAYP,OAEd,KAGFtG,KACAJ,KACAC,KACuB,IAAxBwC,EAAM6C,QAAQ,YAEPtH,EACHA,EAAmBG,WAAWsE,GAC9BA,SAICD,GAAcC,WAIZrC,GAAa,KAAOlC,EAK3B+E,GAAQ9C,OACG8C,EAAKiE,oBAIdC,EAAe1D,GAAgBhD,GAAWgC,EAAQQ,GAGhDuB,EAAc2C,EAAaX,YAEJ,IAAzBhC,EAAY3H,UAAkB2H,IAAgBmC,GAK9ChC,GAAkBH,KAKlBA,EAAY1G,mBAAmBZ,MACdsH,EAAY1G,YAIb0G,KAEVA,QAGF,KAGN/D,UACKgC,KAILrC,GAAY,IACVC,SACW9B,EAAuB+E,KAAKL,EAAKlF,eAEvCkF,EAAKiE,cAECD,YAAYhE,EAAKiE,mBAGjBjE,SAGX3C,OAMW9B,EAAW8E,KAAKvG,EAAkB6J,GAAY,IAGtDA,MAGLQ,EAAiBnH,GAAiBgD,EAAKd,UAAYc,EAAKO,iBAGxDxD,UACeoH,EAAenC,QAAQrG,EAAe,MACvBqG,QAAQnG,EAAU,MAG7Cd,EACHA,EAAmBG,WAAWiJ,GAC9BA,KASIC,UAAY,SAASnG,MAChBA,OACA,KAQLoG,YAAc,cACb,SACI,KAaLC,iBAAmB,SAASC,EAAKhC,EAAMH,GAE1CtE,eAICoE,EAAQqC,EAAI7L,cACZyJ,EAASI,EAAK7J,qBACbuJ,GAAkBC,EAAOC,EAAQC,MAUhCoC,QAAU,SAASlD,EAAYmD,GACX,mBAAjBA,MAILnD,GAAc9F,EAAM8F,SACpBA,GAAYxC,KAAK2F,OAUfC,WAAa,SAASpD,GAC1B9F,EAAM8F,MACFA,GAAY4B,SAUZyB,YAAc,SAASrD,GAC3B9F,EAAM8F,OACFA,UASAsD,eAAiB,iBAIpBtL,EC5qCT,IAAMqF,EACJhG,OAAOgG,QACP,SAASkG,UACAA,GAGEzG,EAAOO,GAClB,IACA,OACA,UACA,UACA,OACA,UACA,QACA,QACA,IACA,MACA,MACA,MACA,QACA,aACA,OACA,KACA,SACA,SACA,UACA,SACA,OACA,OACA,MACA,WACA,UACA,OACA,WACA,KACA,YACA,MACA,UACA,MACA,MACA,MACA,KACA,KACA,UACA,KACA,WACA,aACA,SACA,OACA,SACA,OACA,KACA,KACA,KACA,KACA,KACA,KACA,OACA,SACA,SACA,KACA,OACA,IACA,MACA,QACA,MACA,MACA,QACA,SACA,KACA,OACA,MACA,OACA,UACA,OACA,WACA,QACA,MACA,OACA,KACA,WACA,SACA,SACA,IACA,MACA,WACA,IACA,KACA,KACA,OACA,IACA,OACA,UACA,SACA,SACA,QACA,SACA,SACA,OACA,SACA,SACA,QACA,MACA,UACA,MACA,QACA,QACA,KACA,WACA,WACA,QACA,KACA,QACA,OACA,KACA,QACA,KACA,IACA,KACA,MACA,QACA,QAIWN,EAAMM,GACjB,MACA,IACA,WACA,cACA,eACA,eACA,gBACA,mBACA,QACA,SACA,SACA,WACA,OACA,OACA,UACA,SACA,OACA,IACA,QACA,WACA,QACA,QACA,OACA,iBACA,SACA,OACA,WACA,QACA,OACA,UACA,UACA,WACA,iBACA,OACA,OACA,QACA,SACA,SACA,OACA,WACA,QACA,OACA,QACA,QACA,OACA,UAGWL,EAAaK,GACxB,UACA,gBACA,sBACA,cACA,mBACA,oBACA,oBACA,iBACA,UACA,UACA,UACA,UACA,UACA,iBACA,UACA,cACA,eACA,WACA,eACA,qBACA,cACA,SACA,iBAGWJ,EAASI,GACpB,OACA,WACA,SACA,UACA,QACA,SACA,KACA,aACA,gBACA,KACA,KACA,QACA,UACA,WACA,QACA,OACA,KACA,SACA,QACA,SACA,OACA,OACA,UACA,SACA,MACA,QACA,MACA,SACA,eAGWmG,EAAOnG,GAAQ,UCxOtBA,EACJhG,OAAOgG,QACP,SAASkG,UACAA,GAGEzG,EAAOO,GAClB,SACA,SACA,QACA,MACA,eACA,aACA,UACA,SACA,cACA,cACA,UACA,OACA,QACA,QACA,QACA,OACA,UACA,WACA,SACA,cACA,WACA,UACA,MACA,WACA,WACA,UACA,OACA,MACA,UACA,SACA,SACA,OACA,OACA,WACA,KACA,YACA,QACA,QACA,OACA,OACA,OACA,MACA,MACA,YACA,QACA,SACA,MACA,WACA,OACA,UACA,aACA,SACA,OACA,UACA,UACA,cACA,SACA,UACA,UACA,aACA,WACA,MACA,WACA,MACA,WACA,OACA,OACA,UACA,aACA,QACA,WACA,QACA,OACA,QACA,OACA,UACA,QACA,MACA,SACA,OACA,QACA,UACA,WACA,QACA,OACA,SACA,SACA,QACA,QACA,UAGWN,EAAMM,GACjB,gBACA,aACA,WACA,qBACA,SACA,gBACA,gBACA,UACA,gBACA,iBACA,QACA,OACA,KACA,QACA,OACA,YACA,YACA,QACA,sBACA,8BACA,gBACA,kBACA,KACA,KACA,IACA,KACA,KACA,kBACA,YACA,UACA,UACA,MACA,WACA,YACA,MACA,OACA,eACA,YACA,SACA,cACA,cACA,gBACA,cACA,YACA,mBACA,eACA,aACA,eACA,cACA,KACA,KACA,KACA,KACA,aACA,WACA,gBACA,oBACA,SACA,OACA,KACA,kBACA,KACA,MACA,IACA,KACA,KACA,KACA,KACA,UACA,YACA,aACA,WACA,OACA,eACA,iBACA,eACA,mBACA,iBACA,QACA,aACA,aACA,eACA,eACA,cACA,cACA,mBACA,YACA,MACA,OACA,QACA,SACA,OACA,MACA,OACA,aACA,SACA,WACA,UACA,QACA,SACA,cACA,SACA,WACA,cACA,OACA,aACA,sBACA,mBACA,eACA,SACA,gBACA,sBACA,iBACA,IACA,KACA,KACA,SACA,OACA,OACA,cACA,YACA,UACA,SACA,SACA,QACA,OACA,kBACA,mBACA,mBACA,eACA,eACA,cACA,aACA,eACA,mBACA,oBACA,iBACA,kBACA,oBACA,iBACA,SACA,eACA,QACA,eACA,WACA,UACA,UACA,YACA,cACA,kBACA,iBACA,aACA,OACA,KACA,KACA,UACA,SACA,UACA,aACA,UACA,aACA,gBACA,gBACA,QACA,eACA,OACA,eACA,mBACA,mBACA,IACA,KACA,KACA,QACA,IACA,KACA,KACA,IACA,eAGWJ,EAASI,GACpB,SACA,cACA,QACA,WACA,QACA,eACA,cACA,aACA,aACA,QACA,MACA,UACA,eACA,QACA,QACA,SACA,OACA,KACA,UACA,SACA,gBACA,SACA,SACA,iBACA,YACA,WACA,cACA,UACA,UACA,gBACA,WACA,WACA,OACA,WACA,WACA,aACA,UACA,SACA,SACA,cACA,gBACA,uBACA,YACA,YACA,aACA,WACA,iBACA,iBACA,YACA,UACA,QACA,UAGWoG,EAAMpG,GACjB,aACA,SACA,cACA,YACA,gBHpVMzF,EAAmCP,OAAnCO,eAAgBb,EAAmBM,OAAnBN,eAClBY,GAA6B,oBAAZ+L,SAA2BA,SAA5C/L,MAEDA,MACK,SAASgM,EAAKC,EAAWC,UACxBF,EAAIhM,MAAMiM,EAAWC,KILhC,IAAMC,EACJzM,OAAOyM,MACP,SAASP,UACAA,GAGElJ,EAAgByJ,EAAK,6BACrBvJ,EAAWuJ,EAAK,yBAChBtJ,EAAYsJ,EAAK,8BACjBrJ,EAAYqJ,EAAK,kBACjBlJ,EAAiBkJ,EAC5B,yFAEWpJ,EAAoBoJ,EAAK,yBACzBnJ,EAAkBmJ,EAC7B,2QHVInM,GAA6B,oBAAZ+L,SAA2BA,SAA5C/L,MACS+J,EAAeqC,MAAMC,UAA5BzF,MACAlB,EAAWhG,OAAXgG,OACFtF,EAAY,iBAAyB,oBAAXD,OAAyB,KAAOA,QAE3DH,MACK,SAASgM,EAAKC,EAAWC,UACxBF,EAAIhM,MAAMiM,EAAWC,KAYhC,IAAMnK,EAA4B,SAASuK,EAAc5L,MAE7B,qBAAjB4L,gBAAAA,KAC8B,mBAA9BA,EAAaC,oBAEb,SAMLC,EAAS,KAGX9L,EAAS+L,eACT/L,EAAS+L,cAAcC,aAHP,6BAKPhM,EAAS+L,cAAcE,aALhB,8BAQZC,EAAa,aAAeJ,EAAS,IAAMA,EAAS,eAGjDF,EAAaC,aAAaK,uBACpBzH,UACFA,KAGX,MAAOa,kBAIC6G,KACN,uBAAyBD,EAAa,0BAEjC,cAonCI1M"}