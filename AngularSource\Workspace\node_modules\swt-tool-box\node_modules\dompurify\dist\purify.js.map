{"version": 3, "file": "purify.js", "sources": ["../src/tags.js", "../src/attrs.js", "../src/utils.js", "../src/regexp.js", "../src/purify.js"], "sourcesContent": ["const freeze =\n  Object.freeze ||\n  function(x) {\n    return x;\n  };\n\nexport const html = freeze([\n  'a',\n  'abbr',\n  'acronym',\n  'address',\n  'area',\n  'article',\n  'aside',\n  'audio',\n  'b',\n  'bdi',\n  'bdo',\n  'big',\n  'blink',\n  'blockquote',\n  'body',\n  'br',\n  'button',\n  'canvas',\n  'caption',\n  'center',\n  'cite',\n  'code',\n  'col',\n  'colgroup',\n  'content',\n  'data',\n  'datalist',\n  'dd',\n  'decorator',\n  'del',\n  'details',\n  'dfn',\n  'dir',\n  'div',\n  'dl',\n  'dt',\n  'element',\n  'em',\n  'fieldset',\n  'figcaption',\n  'figure',\n  'font',\n  'footer',\n  'form',\n  'h1',\n  'h2',\n  'h3',\n  'h4',\n  'h5',\n  'h6',\n  'head',\n  'header',\n  'hgroup',\n  'hr',\n  'html',\n  'i',\n  'img',\n  'input',\n  'ins',\n  'kbd',\n  'label',\n  'legend',\n  'li',\n  'main',\n  'map',\n  'mark',\n  'marquee',\n  'menu',\n  'menuitem',\n  'meter',\n  'nav',\n  'nobr',\n  'ol',\n  'optgroup',\n  'option',\n  'output',\n  'p',\n  'pre',\n  'progress',\n  'q',\n  'rp',\n  'rt',\n  'ruby',\n  's',\n  'samp',\n  'section',\n  'select',\n  'shadow',\n  'small',\n  'source',\n  'spacer',\n  'span',\n  'strike',\n  'strong',\n  'style',\n  'sub',\n  'summary',\n  'sup',\n  'table',\n  'tbody',\n  'td',\n  'template',\n  'textarea',\n  'tfoot',\n  'th',\n  'thead',\n  'time',\n  'tr',\n  'track',\n  'tt',\n  'u',\n  'ul',\n  'var',\n  'video',\n  'wbr',\n]);\n\n// SVG\nexport const svg = freeze([\n  'svg',\n  'a',\n  'altglyph',\n  'altglyphdef',\n  'altglyphitem',\n  'animatecolor',\n  'animatemotion',\n  'animatetransform',\n  'audio',\n  'canvas',\n  'circle',\n  'clippath',\n  'defs',\n  'desc',\n  'ellipse',\n  'filter',\n  'font',\n  'g',\n  'glyph',\n  'glyphref',\n  'hkern',\n  'image',\n  'line',\n  'lineargradient',\n  'marker',\n  'mask',\n  'metadata',\n  'mpath',\n  'path',\n  'pattern',\n  'polygon',\n  'polyline',\n  'radialgradient',\n  'rect',\n  'stop',\n  'style',\n  'switch',\n  'symbol',\n  'text',\n  'textpath',\n  'title',\n  'tref',\n  'tspan',\n  'video',\n  'view',\n  'vkern',\n]);\n\nexport const svgFilters = freeze([\n  'feBlend',\n  'feColorMatrix',\n  'feComponentTransfer',\n  'feComposite',\n  'feConvolveMatrix',\n  'feDiffuseLighting',\n  'feDisplacementMap',\n  'feDistantLight',\n  'feFlood',\n  'feFuncA',\n  'feFuncB',\n  'feFuncG',\n  'feFuncR',\n  'feGaussianBlur',\n  'feMerge',\n  'feMergeNode',\n  'feMorphology',\n  'feOffset',\n  'fePointLight',\n  'feSpecularLighting',\n  'feSpotLight',\n  'feTile',\n  'feTurbulence',\n]);\n\nexport const mathMl = freeze([\n  'math',\n  'menclose',\n  'merror',\n  'mfenced',\n  'mfrac',\n  'mglyph',\n  'mi',\n  'mlabeledtr',\n  'mmultiscripts',\n  'mn',\n  'mo',\n  'mover',\n  'mpadded',\n  'mphantom',\n  'mroot',\n  'mrow',\n  'ms',\n  'mspace',\n  'msqrt',\n  'mstyle',\n  'msub',\n  'msup',\n  'msubsup',\n  'mtable',\n  'mtd',\n  'mtext',\n  'mtr',\n  'munder',\n  'munderover',\n]);\n\nexport const text = freeze(['#text']);\n", "const freeze =\n  Object.freeze ||\n  function(x) {\n    return x;\n  };\n\nexport const html = freeze([\n  'accept',\n  'action',\n  'align',\n  'alt',\n  'autocomplete',\n  'background',\n  'bgcolor',\n  'border',\n  'cellpadding',\n  'cellspacing',\n  'checked',\n  'cite',\n  'class',\n  'clear',\n  'color',\n  'cols',\n  'colspan',\n  'controls',\n  'coords',\n  'crossorigin',\n  'datetime',\n  'default',\n  'dir',\n  'disabled',\n  'download',\n  'enctype',\n  'face',\n  'for',\n  'headers',\n  'height',\n  'hidden',\n  'high',\n  'href',\n  'hreflang',\n  'id',\n  'integrity',\n  'ismap',\n  'label',\n  'lang',\n  'list',\n  'loop',\n  'low',\n  'max',\n  'maxlength',\n  'media',\n  'method',\n  'min',\n  'multiple',\n  'name',\n  'noshade',\n  'novalidate',\n  'nowrap',\n  'open',\n  'optimum',\n  'pattern',\n  'placeholder',\n  'poster',\n  'preload',\n  'pubdate',\n  'radiogroup',\n  'readonly',\n  'rel',\n  'required',\n  'rev',\n  'reversed',\n  'role',\n  'rows',\n  'rowspan',\n  'spellcheck',\n  'scope',\n  'selected',\n  'shape',\n  'size',\n  'sizes',\n  'span',\n  'srclang',\n  'start',\n  'src',\n  'srcset',\n  'step',\n  'style',\n  'summary',\n  'tabindex',\n  'title',\n  'type',\n  'usemap',\n  'valign',\n  'value',\n  'width',\n  'xmlns',\n]);\n\nexport const svg = freeze([\n  'accent-height',\n  'accumulate',\n  'additive',\n  'alignment-baseline',\n  'ascent',\n  'attributename',\n  'attributetype',\n  'azimuth',\n  'basefrequency',\n  'baseline-shift',\n  'begin',\n  'bias',\n  'by',\n  'class',\n  'clip',\n  'clip-path',\n  'clip-rule',\n  'color',\n  'color-interpolation',\n  'color-interpolation-filters',\n  'color-profile',\n  'color-rendering',\n  'cx',\n  'cy',\n  'd',\n  'dx',\n  'dy',\n  'diffuseconstant',\n  'direction',\n  'display',\n  'divisor',\n  'dur',\n  'edgemode',\n  'elevation',\n  'end',\n  'fill',\n  'fill-opacity',\n  'fill-rule',\n  'filter',\n  'filterunits',\n  'flood-color',\n  'flood-opacity',\n  'font-family',\n  'font-size',\n  'font-size-adjust',\n  'font-stretch',\n  'font-style',\n  'font-variant',\n  'font-weight',\n  'fx',\n  'fy',\n  'g1',\n  'g2',\n  'glyph-name',\n  'glyphref',\n  'gradientunits',\n  'gradienttransform',\n  'height',\n  'href',\n  'id',\n  'image-rendering',\n  'in',\n  'in2',\n  'k',\n  'k1',\n  'k2',\n  'k3',\n  'k4',\n  'kerning',\n  'keypoints',\n  'keysplines',\n  'keytimes',\n  'lang',\n  'lengthadjust',\n  'letter-spacing',\n  'kernelmatrix',\n  'kernelunitlength',\n  'lighting-color',\n  'local',\n  'marker-end',\n  'marker-mid',\n  'marker-start',\n  'markerheight',\n  'markerunits',\n  'markerwidth',\n  'maskcontentunits',\n  'maskunits',\n  'max',\n  'mask',\n  'media',\n  'method',\n  'mode',\n  'min',\n  'name',\n  'numoctaves',\n  'offset',\n  'operator',\n  'opacity',\n  'order',\n  'orient',\n  'orientation',\n  'origin',\n  'overflow',\n  'paint-order',\n  'path',\n  'pathlength',\n  'patterncontentunits',\n  'patterntransform',\n  'patternunits',\n  'points',\n  'preservealpha',\n  'preserveaspectratio',\n  'primitiveunits',\n  'r',\n  'rx',\n  'ry',\n  'radius',\n  'refx',\n  'refy',\n  'repeatcount',\n  'repeatdur',\n  'restart',\n  'result',\n  'rotate',\n  'scale',\n  'seed',\n  'shape-rendering',\n  'specularconstant',\n  'specularexponent',\n  'spreadmethod',\n  'stddeviation',\n  'stitchtiles',\n  'stop-color',\n  'stop-opacity',\n  'stroke-dasharray',\n  'stroke-dashoffset',\n  'stroke-linecap',\n  'stroke-linejoin',\n  'stroke-miterlimit',\n  'stroke-opacity',\n  'stroke',\n  'stroke-width',\n  'style',\n  'surfacescale',\n  'tabindex',\n  'targetx',\n  'targety',\n  'transform',\n  'text-anchor',\n  'text-decoration',\n  'text-rendering',\n  'textlength',\n  'type',\n  'u1',\n  'u2',\n  'unicode',\n  'values',\n  'viewbox',\n  'visibility',\n  'version',\n  'vert-adv-y',\n  'vert-origin-x',\n  'vert-origin-y',\n  'width',\n  'word-spacing',\n  'wrap',\n  'writing-mode',\n  'xchannelselector',\n  'ychannelselector',\n  'x',\n  'x1',\n  'x2',\n  'xmlns',\n  'y',\n  'y1',\n  'y2',\n  'z',\n  'zoomandpan',\n]);\n\nexport const mathMl = freeze([\n  'accent',\n  'accentunder',\n  'align',\n  'bevelled',\n  'close',\n  'columnsalign',\n  'columnlines',\n  'columnspan',\n  'denomalign',\n  'depth',\n  'dir',\n  'display',\n  'displaystyle',\n  'fence',\n  'frame',\n  'height',\n  'href',\n  'id',\n  'largeop',\n  'length',\n  'linethickness',\n  'lspace',\n  'lquote',\n  'mathbackground',\n  'mathcolor',\n  'mathsize',\n  'mathvariant',\n  'maxsize',\n  'minsize',\n  'movablelimits',\n  'notation',\n  'numalign',\n  'open',\n  'rowalign',\n  'rowlines',\n  'rowspacing',\n  'rowspan',\n  'rspace',\n  'rquote',\n  'scriptlevel',\n  'scriptminsize',\n  'scriptsizemultiplier',\n  'selection',\n  'separator',\n  'separators',\n  'stretchy',\n  'subscriptshift',\n  'supscriptshift',\n  'symmetric',\n  'voffset',\n  'width',\n  'xmlns',\n]);\n\nexport const xml = freeze([\n  'xlink:href',\n  'xml:id',\n  'xlink:title',\n  'xml:space',\n  'xmlns:xlink',\n]);\n", "const { hasOwnProperty, setPrototypeOf } = Object;\nlet { apply } = typeof Reflect !== 'undefined' && Reflect;\n\nif (!apply) {\n  apply = function(fun, thisValue, args) {\n    return fun.apply(thisValue, args);\n  };\n}\n\n/* Add properties to a lookup table */\nexport function addToSet(set, array) {\n  if (setPrototypeOf) {\n    // Make 'in' and truthy checks like <PERSON><PERSON><PERSON>(set.constructor)\n    // independent of any properties defined on Object.prototype.\n    // Prevent prototype setters from intercepting set as a this value.\n    setPrototypeOf(set, null);\n  }\n\n  let l = array.length;\n  while (l--) {\n    let element = array[l];\n    if (typeof element === 'string') {\n      const lcElement = element.toLowerCase();\n      if (lcElement !== element) {\n        // Config presets (e.g. tags.js, attrs.js) are immutable.\n        if (!Object.isFrozen(array)) {\n          array[l] = lcElement;\n        }\n\n        element = lcElement;\n      }\n    }\n\n    set[element] = true;\n  }\n\n  return set;\n}\n\n/* Shallow clone an object */\nexport function clone(object) {\n  const newObject = {};\n\n  let property;\n  for (property in object) {\n    if (apply(hasOwnProperty, object, [property])) {\n      newObject[property] = object[property];\n    }\n  }\n\n  return newObject;\n}\n", "const seal =\n  Object.seal ||\n  function(x) {\n    return x;\n  };\n\nexport const MUSTACHE_EXPR = seal(/\\{\\{[\\s\\S]*|[\\s\\S]*\\}\\}/gm); // Specify template detection regex for SAFE_FOR_TEMPLATES mode\nexport const ERB_EXPR = seal(/<%[\\s\\S]*|[\\s\\S]*%>/gm);\nexport const DATA_ATTR = seal(/^data-[\\-\\w.\\u00B7-\\uFFFF]/); // eslint-disable-line no-useless-escape\nexport const ARIA_ATTR = seal(/^aria-[\\-\\w]+$/); // eslint-disable-line no-useless-escape\nexport const IS_ALLOWED_URI = seal(\n  /^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\\-]+(?:[^a-z+.\\-:]|$))/i // eslint-disable-line no-useless-escape\n);\nexport const IS_SCRIPT_OR_DATA = seal(/^(?:\\w+script|data):/i);\nexport const ATTR_WHITESPACE = seal(\n  /[\\u0000-\\u0020\\u00A0\\u1680\\u180E\\u2000-\\u2029\\u205f\\u3000]/g // eslint-disable-line no-control-regex\n);\n", "import * as TAGS from './tags';\nimport * as ATTRS from './attrs';\nimport { addToSet, clone } from './utils';\nimport * as EXPRESSIONS from './regexp';\n\nlet { apply } = typeof Reflect !== 'undefined' && Reflect;\nconst { slice: arraySlice } = Array.prototype;\nconst { freeze } = Object;\nconst getGlobal = () => (typeof window === 'undefined' ? null : window);\n\nif (!apply) {\n  apply = function(fun, thisValue, args) {\n    return fun.apply(thisValue, args);\n  };\n}\n\n/**\n * Creates a no-op policy for internal use only.\n * Don't export this function outside this module!\n * @param {?TrustedTypePolicyFactory} trustedTypes The policy factory.\n * @param {Document} document The document object (to determine policy name suffix)\n * @return {?TrustedTypePolicy} The policy created (or null, if Trusted Types\n * are not supported).\n */\nconst _createTrustedTypesPolicy = function(trustedTypes, document) {\n  if (\n    typeof trustedTypes !== 'object' ||\n    typeof trustedTypes.createPolicy !== 'function'\n  ) {\n    return null;\n  }\n\n  // Allow the callers to control the unique policy name\n  // by adding a data-tt-policy-suffix to the script element with the DOMPurify.\n  // Policy creation with duplicate names throws in Trusted Types.\n  let suffix = null;\n  const ATTR_NAME = 'data-tt-policy-suffix';\n  if (\n    document.currentScript &&\n    document.currentScript.hasAttribute(ATTR_NAME)\n  ) {\n    suffix = document.currentScript.getAttribute(ATTR_NAME);\n  }\n\n  const policyName = 'dompurify' + (suffix ? '#' + suffix : '');\n\n  try {\n    return trustedTypes.createPolicy(policyName, {\n      createHTML(html) {\n        return html;\n      },\n    });\n  } catch (error) {\n    // Policy creation failed (most likely another DOMPurify script has\n    // already run). Skip creating the policy, as this will only cause errors\n    // if TT are enforced.\n    console.warn(\n      'TrustedTypes policy ' + policyName + ' could not be created.'\n    );\n    return null;\n  }\n};\n\nfunction createDOMPurify(window = getGlobal()) {\n  const DOMPurify = root => createDOMPurify(root);\n\n  /**\n   * Version label, exposed for easier checks\n   * if DOMPurify is up to date or not\n   */\n  DOMPurify.version = VERSION;\n\n  /**\n   * Array of elements that DOMPurify removed during sanitation.\n   * Empty if nothing was removed.\n   */\n  DOMPurify.removed = [];\n\n  if (!window || !window.document || window.document.nodeType !== 9) {\n    // Not running in a browser, provide a factory function\n    // so that you can pass your own Window\n    DOMPurify.isSupported = false;\n\n    return DOMPurify;\n  }\n\n  const originalDocument = window.document;\n  let useDOMParser = false;\n  let removeTitle = false;\n\n  let { document } = window;\n  const {\n    DocumentFragment,\n    HTMLTemplateElement,\n    Node,\n    NodeFilter,\n    NamedNodeMap = window.NamedNodeMap || window.MozNamedAttrMap,\n    Text,\n    Comment,\n    DOMParser,\n    TrustedTypes,\n  } = window;\n\n  // As per issue #47, the web-components registry is inherited by a\n  // new document created via createHTMLDocument. As per the spec\n  // (http://w3c.github.io/webcomponents/spec/custom/#creating-and-passing-registries)\n  // a new empty registry is used when creating a template contents owner\n  // document, so we use that as our parent document to ensure nothing\n  // is inherited.\n  if (typeof HTMLTemplateElement === 'function') {\n    const template = document.createElement('template');\n    if (template.content && template.content.ownerDocument) {\n      document = template.content.ownerDocument;\n    }\n  }\n\n  const trustedTypesPolicy = _createTrustedTypesPolicy(\n    TrustedTypes,\n    originalDocument\n  );\n  const emptyHTML = trustedTypesPolicy ? trustedTypesPolicy.createHTML('') : '';\n\n  const {\n    implementation,\n    createNodeIterator,\n    getElementsByTagName,\n    createDocumentFragment,\n  } = document;\n  const { importNode } = originalDocument;\n\n  let hooks = {};\n\n  /**\n   * Expose whether this browser supports running the full DOMPurify.\n   */\n  DOMPurify.isSupported =\n    implementation &&\n    typeof implementation.createHTMLDocument !== 'undefined' &&\n    document.documentMode !== 9;\n\n  const {\n    MUSTACHE_EXPR,\n    ERB_EXPR,\n    DATA_ATTR,\n    ARIA_ATTR,\n    IS_SCRIPT_OR_DATA,\n    ATTR_WHITESPACE,\n  } = EXPRESSIONS;\n\n  let { IS_ALLOWED_URI } = EXPRESSIONS;\n  /**\n   * We consider the elements and attributes below to be safe. Ideally\n   * don't add any new ones but feel free to remove unwanted ones.\n   */\n\n  /* allowed element names */\n  let ALLOWED_TAGS = null;\n  const DEFAULT_ALLOWED_TAGS = addToSet({}, [\n    ...TAGS.html,\n    ...TAGS.svg,\n    ...TAGS.svgFilters,\n    ...TAGS.mathMl,\n    ...TAGS.text,\n  ]);\n\n  /* Allowed attribute names */\n  let ALLOWED_ATTR = null;\n  const DEFAULT_ALLOWED_ATTR = addToSet({}, [\n    ...ATTRS.html,\n    ...ATTRS.svg,\n    ...ATTRS.mathMl,\n    ...ATTRS.xml,\n  ]);\n\n  /* Explicitly forbidden tags (overrides ALLOWED_TAGS/ADD_TAGS) */\n  let FORBID_TAGS = null;\n\n  /* Explicitly forbidden attributes (overrides ALLOWED_ATTR/ADD_ATTR) */\n  let FORBID_ATTR = null;\n\n  /* Decide if ARIA attributes are okay */\n  let ALLOW_ARIA_ATTR = true;\n\n  /* Decide if custom data attributes are okay */\n  let ALLOW_DATA_ATTR = true;\n\n  /* Decide if unknown protocols are okay */\n  let ALLOW_UNKNOWN_PROTOCOLS = false;\n\n  /* Output should be safe for jQuery's $() factory? */\n  let SAFE_FOR_JQUERY = false;\n\n  /* Output should be safe for common template engines.\n   * This means, DOMPurify removes data attributes, mustaches and ERB\n   */\n  let SAFE_FOR_TEMPLATES = false;\n\n  /* Decide if document with <html>... should be returned */\n  let WHOLE_DOCUMENT = false;\n\n  /* Track whether config is already set on this instance of DOMPurify. */\n  let SET_CONFIG = false;\n\n  /* Decide if all elements (e.g. style, script) must be children of\n   * document.body. By default, browsers might move them to document.head */\n  let FORCE_BODY = false;\n\n  /* Decide if a DOM `HTMLBodyElement` should be returned, instead of a html\n   * string (or a TrustedHTML object if Trusted Types are supported).\n   * If `WHOLE_DOCUMENT` is enabled a `HTMLHtmlElement` will be returned instead\n   */\n  let RETURN_DOM = false;\n\n  /* Decide if a DOM `DocumentFragment` should be returned, instead of a html\n   * string  (or a TrustedHTML object if Trusted Types are supported) */\n  let RETURN_DOM_FRAGMENT = false;\n\n  /* If `RETURN_DOM` or `RETURN_DOM_FRAGMENT` is enabled, decide if the returned DOM\n   * `Node` is imported into the current `Document`. If this flag is not enabled the\n   * `Node` will belong (its ownerDocument) to a fresh `HTMLDocument`, created by\n   * DOMPurify. */\n  let RETURN_DOM_IMPORT = false;\n\n  /* Output should be free from DOM clobbering attacks? */\n  let SANITIZE_DOM = true;\n\n  /* Keep element content when removing element? */\n  let KEEP_CONTENT = true;\n\n  /* If a `Node` is passed to sanitize(), then performs sanitization in-place instead\n   * of importing it into a new Document and returning a sanitized copy */\n  let IN_PLACE = false;\n\n  /* Allow usage of profiles like html, svg and mathMl */\n  let USE_PROFILES = {};\n\n  /* Tags to ignore content of when KEEP_CONTENT is true */\n  const FORBID_CONTENTS = addToSet({}, [\n    'audio',\n    'head',\n    'math',\n    'script',\n    'style',\n    'template',\n    'svg',\n    'video',\n  ]);\n\n  /* Tags that are safe for data: URIs */\n  const DATA_URI_TAGS = addToSet({}, [\n    'audio',\n    'video',\n    'img',\n    'source',\n    'image',\n  ]);\n\n  /* Attributes safe for values like \"javascript:\" */\n  let URI_SAFE_ATTRIBUTES = null;\n  const DEFAULT_URI_SAFE_ATTRIBUTES = addToSet({}, [\n    'alt',\n    'class',\n    'for',\n    'id',\n    'label',\n    'name',\n    'pattern',\n    'placeholder',\n    'summary',\n    'title',\n    'value',\n    'style',\n    'xmlns',\n  ]);\n\n  /* Keep a reference to config to pass to hooks */\n  let CONFIG = null;\n\n  /* Ideally, do not touch anything below this line */\n  /* ______________________________________________ */\n\n  const formElement = document.createElement('form');\n\n  /**\n   * _parseConfig\n   *\n   * @param  {Object} cfg optional config literal\n   */\n  // eslint-disable-next-line complexity\n  const _parseConfig = function(cfg) {\n    if (CONFIG && CONFIG === cfg) {\n      return;\n    }\n\n    /* Shield configuration object from tampering */\n    if (!cfg || typeof cfg !== 'object') {\n      cfg = {};\n    }\n\n    /* Set configuration parameters */\n    ALLOWED_TAGS =\n      'ALLOWED_TAGS' in cfg\n        ? addToSet({}, cfg.ALLOWED_TAGS)\n        : DEFAULT_ALLOWED_TAGS;\n    ALLOWED_ATTR =\n      'ALLOWED_ATTR' in cfg\n        ? addToSet({}, cfg.ALLOWED_ATTR)\n        : DEFAULT_ALLOWED_ATTR;\n    URI_SAFE_ATTRIBUTES =\n      'ADD_URI_SAFE_ATTR' in cfg\n        ? addToSet({}, cfg.ADD_URI_SAFE_ATTR)\n        : DEFAULT_URI_SAFE_ATTRIBUTES;\n    FORBID_TAGS = 'FORBID_TAGS' in cfg ? addToSet({}, cfg.FORBID_TAGS) : {};\n    FORBID_ATTR = 'FORBID_ATTR' in cfg ? addToSet({}, cfg.FORBID_ATTR) : {};\n    USE_PROFILES = 'USE_PROFILES' in cfg ? cfg.USE_PROFILES : false;\n    ALLOW_ARIA_ATTR = cfg.ALLOW_ARIA_ATTR !== false; // Default true\n    ALLOW_DATA_ATTR = cfg.ALLOW_DATA_ATTR !== false; // Default true\n    ALLOW_UNKNOWN_PROTOCOLS = cfg.ALLOW_UNKNOWN_PROTOCOLS || false; // Default false\n    SAFE_FOR_JQUERY = cfg.SAFE_FOR_JQUERY || false; // Default false\n    SAFE_FOR_TEMPLATES = cfg.SAFE_FOR_TEMPLATES || false; // Default false\n    WHOLE_DOCUMENT = cfg.WHOLE_DOCUMENT || false; // Default false\n    RETURN_DOM = cfg.RETURN_DOM || false; // Default false\n    RETURN_DOM_FRAGMENT = cfg.RETURN_DOM_FRAGMENT || false; // Default false\n    RETURN_DOM_IMPORT = cfg.RETURN_DOM_IMPORT || false; // Default false\n    FORCE_BODY = cfg.FORCE_BODY || false; // Default false\n    SANITIZE_DOM = cfg.SANITIZE_DOM !== false; // Default true\n    KEEP_CONTENT = cfg.KEEP_CONTENT !== false; // Default true\n    IN_PLACE = cfg.IN_PLACE || false; // Default false\n\n    IS_ALLOWED_URI = cfg.ALLOWED_URI_REGEXP || IS_ALLOWED_URI;\n\n    if (SAFE_FOR_TEMPLATES) {\n      ALLOW_DATA_ATTR = false;\n    }\n\n    if (RETURN_DOM_FRAGMENT) {\n      RETURN_DOM = true;\n    }\n\n    /* Parse profile info */\n    if (USE_PROFILES) {\n      ALLOWED_TAGS = addToSet({}, [...TAGS.text]);\n      ALLOWED_ATTR = [];\n      if (USE_PROFILES.html === true) {\n        addToSet(ALLOWED_TAGS, TAGS.html);\n        addToSet(ALLOWED_ATTR, ATTRS.html);\n      }\n\n      if (USE_PROFILES.svg === true) {\n        addToSet(ALLOWED_TAGS, TAGS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n\n      if (USE_PROFILES.svgFilters === true) {\n        addToSet(ALLOWED_TAGS, TAGS.svgFilters);\n        addToSet(ALLOWED_ATTR, ATTRS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n\n      if (USE_PROFILES.mathMl === true) {\n        addToSet(ALLOWED_TAGS, TAGS.mathMl);\n        addToSet(ALLOWED_ATTR, ATTRS.mathMl);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n    }\n\n    /* Merge configuration parameters */\n    if (cfg.ADD_TAGS) {\n      if (ALLOWED_TAGS === DEFAULT_ALLOWED_TAGS) {\n        ALLOWED_TAGS = clone(ALLOWED_TAGS);\n      }\n\n      addToSet(ALLOWED_TAGS, cfg.ADD_TAGS);\n    }\n\n    if (cfg.ADD_ATTR) {\n      if (ALLOWED_ATTR === DEFAULT_ALLOWED_ATTR) {\n        ALLOWED_ATTR = clone(ALLOWED_ATTR);\n      }\n\n      addToSet(ALLOWED_ATTR, cfg.ADD_ATTR);\n    }\n\n    if (cfg.ADD_URI_SAFE_ATTR) {\n      addToSet(URI_SAFE_ATTRIBUTES, cfg.ADD_URI_SAFE_ATTR);\n    }\n\n    /* Add #text in case KEEP_CONTENT is set to true */\n    if (KEEP_CONTENT) {\n      ALLOWED_TAGS['#text'] = true;\n    }\n\n    /* Add html, head and body to ALLOWED_TAGS in case WHOLE_DOCUMENT is true */\n    if (WHOLE_DOCUMENT) {\n      addToSet(ALLOWED_TAGS, ['html', 'head', 'body']);\n    }\n\n    /* Add tbody to ALLOWED_TAGS in case tables are permitted, see #286 */\n    if (ALLOWED_TAGS.table) {\n      addToSet(ALLOWED_TAGS, ['tbody']);\n    }\n\n    // Prevent further manipulation of configuration.\n    // Not available in IE8, Safari 5, etc.\n    if (freeze) {\n      freeze(cfg);\n    }\n\n    CONFIG = cfg;\n  };\n\n  /**\n   * _forceRemove\n   *\n   * @param  {Node} node a DOM node\n   */\n  const _forceRemove = function(node) {\n    DOMPurify.removed.push({ element: node });\n    try {\n      node.parentNode.removeChild(node);\n    } catch (error) {\n      node.outerHTML = emptyHTML;\n    }\n  };\n\n  /**\n   * _removeAttribute\n   *\n   * @param  {String} name an Attribute name\n   * @param  {Node} node a DOM node\n   */\n  const _removeAttribute = function(name, node) {\n    try {\n      DOMPurify.removed.push({\n        attribute: node.getAttributeNode(name),\n        from: node,\n      });\n    } catch (error) {\n      DOMPurify.removed.push({\n        attribute: null,\n        from: node,\n      });\n    }\n\n    node.removeAttribute(name);\n  };\n\n  /**\n   * _initDocument\n   *\n   * @param  {String} dirty a string of dirty markup\n   * @return {Document} a DOM, filled with the dirty markup\n   */\n  const _initDocument = function(dirty) {\n    /* Create a HTML document */\n    let doc;\n    let leadingWhitespace;\n\n    if (FORCE_BODY) {\n      dirty = '<remove></remove>' + dirty;\n    } else {\n      /* If FORCE_BODY isn't used, leading whitespace needs to be preserved manually */\n      const matches = dirty.match(/^[\\s]+/);\n      leadingWhitespace = matches && matches[0];\n      if (leadingWhitespace) {\n        dirty = dirty.slice(leadingWhitespace.length);\n      }\n    }\n\n    /* Use DOMParser to workaround Firefox bug (see comment below) */\n    if (useDOMParser) {\n      try {\n        doc = new DOMParser().parseFromString(dirty, 'text/html');\n      } catch (error) {}\n    }\n\n    /* Remove title to fix a mXSS bug in older MS Edge */\n    if (removeTitle) {\n      addToSet(FORBID_TAGS, ['title']);\n    }\n\n    /* Otherwise use createHTMLDocument, because DOMParser is unsafe in\n    Safari (see comment below) */\n    if (!doc || !doc.documentElement) {\n      doc = implementation.createHTMLDocument('');\n      const { body } = doc;\n      body.parentNode.removeChild(body.parentNode.firstElementChild);\n      body.outerHTML = trustedTypesPolicy\n        ? trustedTypesPolicy.createHTML(dirty)\n        : dirty;\n    }\n\n    if (leadingWhitespace) {\n      doc.body.insertBefore(\n        document.createTextNode(leadingWhitespace),\n        doc.body.childNodes[0] || null\n      );\n    }\n\n    /* Work on whole document or just its body */\n    return getElementsByTagName.call(doc, WHOLE_DOCUMENT ? 'html' : 'body')[0];\n  };\n\n  // Firefox uses a different parser for innerHTML rather than\n  // DOMParser (see https://bugzilla.mozilla.org/show_bug.cgi?id=1205631)\n  // which means that you *must* use DOMParser, otherwise the output may\n  // not be safe if used in a document.write context later.\n  //\n  // So we feature detect the Firefox bug and use the DOMParser if necessary.\n  //\n  // MS Edge, in older versions, is affected by an mXSS behavior. The second\n  // check tests for the behavior and fixes it if necessary.\n  if (DOMPurify.isSupported) {\n    (function() {\n      try {\n        const doc = _initDocument(\n          '<svg><p><style><img src=\"</style><img src=x onerror=1//\">'\n        );\n        if (doc.querySelector('svg img')) {\n          useDOMParser = true;\n        }\n      } catch (error) {}\n    })();\n\n    (function() {\n      try {\n        const doc = _initDocument('<x/><title>&lt;/title&gt;&lt;img&gt;');\n        if (doc.querySelector('title').innerHTML.match(/<\\/title/)) {\n          removeTitle = true;\n        }\n      } catch (error) {}\n    })();\n  }\n\n  /**\n   * _createIterator\n   *\n   * @param  {Document} root document/fragment to create iterator for\n   * @return {Iterator} iterator instance\n   */\n  const _createIterator = function(root) {\n    return createNodeIterator.call(\n      root.ownerDocument || root,\n      root,\n      NodeFilter.SHOW_ELEMENT | NodeFilter.SHOW_COMMENT | NodeFilter.SHOW_TEXT,\n      () => {\n        return NodeFilter.FILTER_ACCEPT;\n      },\n      false\n    );\n  };\n\n  /**\n   * _isClobbered\n   *\n   * @param  {Node} elm element to check for clobbering attacks\n   * @return {Boolean} true if clobbered, false if safe\n   */\n  const _isClobbered = function(elm) {\n    if (elm instanceof Text || elm instanceof Comment) {\n      return false;\n    }\n\n    if (\n      typeof elm.nodeName !== 'string' ||\n      typeof elm.textContent !== 'string' ||\n      typeof elm.removeChild !== 'function' ||\n      !(elm.attributes instanceof NamedNodeMap) ||\n      typeof elm.removeAttribute !== 'function' ||\n      typeof elm.setAttribute !== 'function'\n    ) {\n      return true;\n    }\n\n    return false;\n  };\n\n  /**\n   * _isNode\n   *\n   * @param  {Node} obj object to check whether it's a DOM node\n   * @return {Boolean} true is object is a DOM node\n   */\n  const _isNode = function(obj) {\n    return typeof Node === 'object'\n      ? obj instanceof Node\n      : obj &&\n          typeof obj === 'object' &&\n          typeof obj.nodeType === 'number' &&\n          typeof obj.nodeName === 'string';\n  };\n\n  /**\n   * _executeHook\n   * Execute user configurable hooks\n   *\n   * @param  {String} entryPoint  Name of the hook's entry point\n   * @param  {Node} currentNode node to work on with the hook\n   * @param  {Object} data additional hook parameters\n   */\n  const _executeHook = function(entryPoint, currentNode, data) {\n    if (!hooks[entryPoint]) {\n      return;\n    }\n\n    hooks[entryPoint].forEach(hook => {\n      hook.call(DOMPurify, currentNode, data, CONFIG);\n    });\n  };\n\n  /**\n   * _sanitizeElements\n   *\n   * @protect nodeName\n   * @protect textContent\n   * @protect removeChild\n   *\n   * @param   {Node} currentNode to check for permission to exist\n   * @return  {Boolean} true if node was killed, false if left alive\n   */\n  // eslint-disable-next-line complexity\n  const _sanitizeElements = function(currentNode) {\n    let content;\n\n    /* Execute a hook if present */\n    _executeHook('beforeSanitizeElements', currentNode, null);\n\n    /* Check if element is clobbered or can clobber */\n    if (_isClobbered(currentNode)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Now let's check the element's type and name */\n    const tagName = currentNode.nodeName.toLowerCase();\n\n    /* Execute a hook if present */\n    _executeHook('uponSanitizeElement', currentNode, {\n      tagName,\n      allowedTags: ALLOWED_TAGS,\n    });\n\n    /* Remove element if anything forbids its presence */\n    if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n      /* Keep content except for black-listed elements */\n      if (\n        KEEP_CONTENT &&\n        !FORBID_CONTENTS[tagName] &&\n        typeof currentNode.insertAdjacentHTML === 'function'\n      ) {\n        try {\n          const htmlToInsert = currentNode.innerHTML;\n          currentNode.insertAdjacentHTML(\n            'AfterEnd',\n            trustedTypesPolicy\n              ? trustedTypesPolicy.createHTML(htmlToInsert)\n              : htmlToInsert\n          );\n        } catch (error) {}\n      }\n\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Remove in case a noscript/noembed XSS is suspected */\n    if (tagName === 'noscript' && currentNode.innerHTML.match(/<\\/noscript/i)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    if (tagName === 'noembed' && currentNode.innerHTML.match(/<\\/noembed/i)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Convert markup to cover jQuery behavior */\n    if (\n      SAFE_FOR_JQUERY &&\n      !currentNode.firstElementChild &&\n      (!currentNode.content || !currentNode.content.firstElementChild) &&\n      /</g.test(currentNode.textContent)\n    ) {\n      DOMPurify.removed.push({ element: currentNode.cloneNode() });\n      if (currentNode.innerHTML) {\n        currentNode.innerHTML = currentNode.innerHTML.replace(/</g, '&lt;');\n      } else {\n        currentNode.innerHTML = currentNode.textContent.replace(/</g, '&lt;');\n      }\n    }\n\n    /* Sanitize element content to be template-safe */\n    if (SAFE_FOR_TEMPLATES && currentNode.nodeType === 3) {\n      /* Get the element's text content */\n      content = currentNode.textContent;\n      content = content.replace(MUSTACHE_EXPR, ' ');\n      content = content.replace(ERB_EXPR, ' ');\n      if (currentNode.textContent !== content) {\n        DOMPurify.removed.push({ element: currentNode.cloneNode() });\n        currentNode.textContent = content;\n      }\n    }\n\n    /* Execute a hook if present */\n    _executeHook('afterSanitizeElements', currentNode, null);\n\n    return false;\n  };\n\n  /**\n   * _isValidAttribute\n   *\n   * @param  {string} lcTag Lowercase tag name of containing element.\n   * @param  {string} lcName Lowercase attribute name.\n   * @param  {string} value Attribute value.\n   * @return {Boolean} Returns true if `value` is valid, otherwise false.\n   */\n  // eslint-disable-next-line complexity\n  const _isValidAttribute = function(lcTag, lcName, value) {\n    /* Make sure attribute cannot clobber */\n    if (\n      SANITIZE_DOM &&\n      (lcName === 'id' || lcName === 'name') &&\n      (value in document || value in formElement)\n    ) {\n      return false;\n    }\n\n    /* Allow valid data-* attributes: At least one character after \"-\"\n        (https://html.spec.whatwg.org/multipage/dom.html#embedding-custom-non-visible-data-with-the-data-*-attributes)\n        XML-compatible (https://html.spec.whatwg.org/multipage/infrastructure.html#xml-compatible and http://www.w3.org/TR/xml/#d0e804)\n        We don't need to check the value; it's always URI safe. */\n    if (ALLOW_DATA_ATTR && DATA_ATTR.test(lcName)) {\n      // This attribute is safe\n    } else if (ALLOW_ARIA_ATTR && ARIA_ATTR.test(lcName)) {\n      // This attribute is safe\n      /* Otherwise, check the name is permitted */\n    } else if (!ALLOWED_ATTR[lcName] || FORBID_ATTR[lcName]) {\n      return false;\n\n      /* Check value is safe. First, is attr inert? If so, is safe */\n    } else if (URI_SAFE_ATTRIBUTES[lcName]) {\n      // This attribute is safe\n      /* Check no script, data or unknown possibly unsafe URI\n        unless we know URI values are safe for that attribute */\n    } else if (IS_ALLOWED_URI.test(value.replace(ATTR_WHITESPACE, ''))) {\n      // This attribute is safe\n      /* Keep image data URIs alive if src/xlink:href is allowed */\n      /* Further prevent gadget XSS for dynamically built script tags */\n    } else if (\n      (lcName === 'src' || lcName === 'xlink:href') &&\n      lcTag !== 'script' &&\n      value.indexOf('data:') === 0 &&\n      DATA_URI_TAGS[lcTag]\n    ) {\n      // This attribute is safe\n      /* Allow unknown protocols: This provides support for links that\n        are handled by protocol handlers which may be unknown ahead of\n        time, e.g. fb:, spotify: */\n    } else if (\n      ALLOW_UNKNOWN_PROTOCOLS &&\n      !IS_SCRIPT_OR_DATA.test(value.replace(ATTR_WHITESPACE, ''))\n    ) {\n      // This attribute is safe\n      /* Check for binary attributes */\n      // eslint-disable-next-line no-negated-condition\n    } else if (!value) {\n      // Binary attributes are safe at this point\n      /* Anything else, presume unsafe, do not add it back */\n    } else {\n      return false;\n    }\n\n    return true;\n  };\n\n  /**\n   * _sanitizeAttributes\n   *\n   * @protect attributes\n   * @protect nodeName\n   * @protect removeAttribute\n   * @protect setAttribute\n   *\n   * @param  {Node} currentNode to sanitize\n   */\n  const _sanitizeAttributes = function(currentNode) {\n    let attr;\n    let value;\n    let lcName;\n    let idAttr;\n    let l;\n    /* Execute a hook if present */\n    _executeHook('beforeSanitizeAttributes', currentNode, null);\n\n    let { attributes } = currentNode;\n\n    /* Check if we have attributes; if not we might have a text node */\n    if (!attributes) {\n      return;\n    }\n\n    const hookEvent = {\n      attrName: '',\n      attrValue: '',\n      keepAttr: true,\n      allowedAttributes: ALLOWED_ATTR,\n    };\n    l = attributes.length;\n\n    /* Go backwards over all attributes; safely remove bad ones */\n    while (l--) {\n      attr = attributes[l];\n      const { name, namespaceURI } = attr;\n      value = attr.value.trim();\n      lcName = name.toLowerCase();\n\n      /* Execute a hook if present */\n      hookEvent.attrName = lcName;\n      hookEvent.attrValue = value;\n      hookEvent.keepAttr = true;\n      _executeHook('uponSanitizeAttribute', currentNode, hookEvent);\n      value = hookEvent.attrValue;\n\n      /* Remove attribute */\n      // Safari (iOS + Mac), last tested v8.0.5, crashes if you try to\n      // remove a \"name\" attribute from an <img> tag that has an \"id\"\n      // attribute at the time.\n      if (\n        lcName === 'name' &&\n        currentNode.nodeName === 'IMG' &&\n        attributes.id\n      ) {\n        idAttr = attributes.id;\n        attributes = apply(arraySlice, attributes, []);\n        _removeAttribute('id', currentNode);\n        _removeAttribute(name, currentNode);\n        if (attributes.indexOf(idAttr) > l) {\n          currentNode.setAttribute('id', idAttr.value);\n        }\n      } else if (\n        // This works around a bug in Safari, where input[type=file]\n        // cannot be dynamically set after type has been removed\n        currentNode.nodeName === 'INPUT' &&\n        lcName === 'type' &&\n        value === 'file' &&\n        hookEvent.keepAttr &&\n        (ALLOWED_ATTR[lcName] || !FORBID_ATTR[lcName])\n      ) {\n        continue;\n      } else {\n        // This avoids a crash in Safari v9.0 with double-ids.\n        // The trick is to first set the id to be empty and then to\n        // remove the attribute\n        if (name === 'id') {\n          currentNode.setAttribute(name, '');\n        }\n\n        _removeAttribute(name, currentNode);\n      }\n\n      /* Did the hooks approve of the attribute? */\n      if (!hookEvent.keepAttr) {\n        continue;\n      }\n\n      /* Sanitize attribute content to be template-safe */\n      if (SAFE_FOR_TEMPLATES) {\n        value = value.replace(MUSTACHE_EXPR, ' ');\n        value = value.replace(ERB_EXPR, ' ');\n      }\n\n      /* Is `value` valid for this attribute? */\n      const lcTag = currentNode.nodeName.toLowerCase();\n      if (!_isValidAttribute(lcTag, lcName, value)) {\n        continue;\n      }\n\n      /* Handle invalid data-* attribute set by try-catching it */\n      try {\n        if (namespaceURI) {\n          currentNode.setAttributeNS(namespaceURI, name, value);\n        } else {\n          /* Fallback to setAttribute() for browser-unrecognized namespaces e.g. \"x-schema\". */\n          currentNode.setAttribute(name, value);\n        }\n\n        DOMPurify.removed.pop();\n      } catch (error) {}\n    }\n\n    /* Execute a hook if present */\n    _executeHook('afterSanitizeAttributes', currentNode, null);\n  };\n\n  /**\n   * _sanitizeShadowDOM\n   *\n   * @param  {DocumentFragment} fragment to iterate over recursively\n   */\n  const _sanitizeShadowDOM = function(fragment) {\n    let shadowNode;\n    const shadowIterator = _createIterator(fragment);\n\n    /* Execute a hook if present */\n    _executeHook('beforeSanitizeShadowDOM', fragment, null);\n\n    while ((shadowNode = shadowIterator.nextNode())) {\n      /* Execute a hook if present */\n      _executeHook('uponSanitizeShadowNode', shadowNode, null);\n\n      /* Sanitize tags and elements */\n      if (_sanitizeElements(shadowNode)) {\n        continue;\n      }\n\n      /* Deep shadow DOM detected */\n      if (shadowNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(shadowNode.content);\n      }\n\n      /* Check attributes, sanitize if necessary */\n      _sanitizeAttributes(shadowNode);\n    }\n\n    /* Execute a hook if present */\n    _executeHook('afterSanitizeShadowDOM', fragment, null);\n  };\n\n  /**\n   * Sanitize\n   * Public method providing core sanitation functionality\n   *\n   * @param {String|Node} dirty string or DOM node\n   * @param {Object} configuration object\n   */\n  // eslint-disable-next-line complexity\n  DOMPurify.sanitize = function(dirty, cfg) {\n    let body;\n    let importedNode;\n    let currentNode;\n    let oldNode;\n    let returnNode;\n    /* Make sure we have a string to sanitize.\n      DO NOT return early, as this will return the wrong type if\n      the user has requested a DOM object rather than a string */\n    if (!dirty) {\n      dirty = '<!-->';\n    }\n\n    /* Stringify, in case dirty is an object */\n    if (typeof dirty !== 'string' && !_isNode(dirty)) {\n      // eslint-disable-next-line no-negated-condition\n      if (typeof dirty.toString !== 'function') {\n        throw new TypeError('toString is not a function');\n      } else {\n        dirty = dirty.toString();\n        if (typeof dirty !== 'string') {\n          throw new TypeError('dirty is not a string, aborting');\n        }\n      }\n    }\n\n    /* Check we can run. Otherwise fall back or ignore */\n    if (!DOMPurify.isSupported) {\n      if (\n        typeof window.toStaticHTML === 'object' ||\n        typeof window.toStaticHTML === 'function'\n      ) {\n        if (typeof dirty === 'string') {\n          return window.toStaticHTML(dirty);\n        }\n\n        if (_isNode(dirty)) {\n          return window.toStaticHTML(dirty.outerHTML);\n        }\n      }\n\n      return dirty;\n    }\n\n    /* Assign config vars */\n    if (!SET_CONFIG) {\n      _parseConfig(cfg);\n    }\n\n    /* Clean up removed elements */\n    DOMPurify.removed = [];\n\n    if (IN_PLACE) {\n      /* No special handling necessary for in-place sanitization */\n    } else if (dirty instanceof Node) {\n      /* If dirty is a DOM element, append to an empty document to avoid\n         elements being stripped by the parser */\n      body = _initDocument('<!-->');\n      importedNode = body.ownerDocument.importNode(dirty, true);\n      if (importedNode.nodeType === 1 && importedNode.nodeName === 'BODY') {\n        /* Node is already a body, use as is */\n        body = importedNode;\n      } else if (importedNode.nodeName === 'HTML') {\n        body = importedNode;\n      } else {\n        // eslint-disable-next-line unicorn/prefer-node-append\n        body.appendChild(importedNode);\n      }\n    } else {\n      /* Exit directly if we have nothing to do */\n      if (\n        !RETURN_DOM &&\n        !SAFE_FOR_TEMPLATES &&\n        !WHOLE_DOCUMENT &&\n        dirty.indexOf('<') === -1\n      ) {\n        return trustedTypesPolicy\n          ? trustedTypesPolicy.createHTML(dirty)\n          : dirty;\n      }\n\n      /* Initialize the document to work on */\n      body = _initDocument(dirty);\n\n      /* Check we have a DOM node from the data */\n      if (!body) {\n        return RETURN_DOM ? null : emptyHTML;\n      }\n    }\n\n    /* Remove first element node (ours) if FORCE_BODY is set */\n    if (body && FORCE_BODY) {\n      _forceRemove(body.firstChild);\n    }\n\n    /* Get node iterator */\n    const nodeIterator = _createIterator(IN_PLACE ? dirty : body);\n\n    /* Now start iterating over the created document */\n    while ((currentNode = nodeIterator.nextNode())) {\n      /* Fix IE's strange behavior with manipulated textNodes #89 */\n      if (currentNode.nodeType === 3 && currentNode === oldNode) {\n        continue;\n      }\n\n      /* Sanitize tags and elements */\n      if (_sanitizeElements(currentNode)) {\n        continue;\n      }\n\n      /* Shadow DOM detected, sanitize it */\n      if (currentNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(currentNode.content);\n      }\n\n      /* Check attributes, sanitize if necessary */\n      _sanitizeAttributes(currentNode);\n\n      oldNode = currentNode;\n    }\n\n    oldNode = null;\n\n    /* If we sanitized `dirty` in-place, return it. */\n    if (IN_PLACE) {\n      return dirty;\n    }\n\n    /* Return sanitized string or DOM */\n    if (RETURN_DOM) {\n      if (RETURN_DOM_FRAGMENT) {\n        returnNode = createDocumentFragment.call(body.ownerDocument);\n\n        while (body.firstChild) {\n          // eslint-disable-next-line unicorn/prefer-node-append\n          returnNode.appendChild(body.firstChild);\n        }\n      } else {\n        returnNode = body;\n      }\n\n      if (RETURN_DOM_IMPORT) {\n        /* AdoptNode() is not used because internal state is not reset\n               (e.g. the past names map of a HTMLFormElement), this is safe\n               in theory but we would rather not risk another attack vector.\n               The state that is cloned by importNode() is explicitly defined\n               by the specs. */\n        returnNode = importNode.call(originalDocument, returnNode, true);\n      }\n\n      return returnNode;\n    }\n\n    let serializedHTML = WHOLE_DOCUMENT ? body.outerHTML : body.innerHTML;\n\n    /* Sanitize final string template-safe */\n    if (SAFE_FOR_TEMPLATES) {\n      serializedHTML = serializedHTML.replace(MUSTACHE_EXPR, ' ');\n      serializedHTML = serializedHTML.replace(ERB_EXPR, ' ');\n    }\n\n    return trustedTypesPolicy\n      ? trustedTypesPolicy.createHTML(serializedHTML)\n      : serializedHTML;\n  };\n\n  /**\n   * Public method to set the configuration once\n   * setConfig\n   *\n   * @param {Object} cfg configuration object\n   */\n  DOMPurify.setConfig = function(cfg) {\n    _parseConfig(cfg);\n    SET_CONFIG = true;\n  };\n\n  /**\n   * Public method to remove the configuration\n   * clearConfig\n   *\n   */\n  DOMPurify.clearConfig = function() {\n    CONFIG = null;\n    SET_CONFIG = false;\n  };\n\n  /**\n   * Public method to check if an attribute value is valid.\n   * Uses last set config, if any. Otherwise, uses config defaults.\n   * isValidAttribute\n   *\n   * @param  {string} tag Tag name of containing element.\n   * @param  {string} attr Attribute name.\n   * @param  {string} value Attribute value.\n   * @return {Boolean} Returns true if `value` is valid. Otherwise, returns false.\n   */\n  DOMPurify.isValidAttribute = function(tag, attr, value) {\n    /* Initialize shared config vars if necessary. */\n    if (!CONFIG) {\n      _parseConfig({});\n    }\n\n    const lcTag = tag.toLowerCase();\n    const lcName = attr.toLowerCase();\n    return _isValidAttribute(lcTag, lcName, value);\n  };\n\n  /**\n   * AddHook\n   * Public method to add DOMPurify hooks\n   *\n   * @param {String} entryPoint entry point for the hook to add\n   * @param {Function} hookFunction function to execute\n   */\n  DOMPurify.addHook = function(entryPoint, hookFunction) {\n    if (typeof hookFunction !== 'function') {\n      return;\n    }\n\n    hooks[entryPoint] = hooks[entryPoint] || [];\n    hooks[entryPoint].push(hookFunction);\n  };\n\n  /**\n   * RemoveHook\n   * Public method to remove a DOMPurify hook at a given entryPoint\n   * (pops it from the stack of hooks if more are present)\n   *\n   * @param {String} entryPoint entry point for the hook to remove\n   */\n  DOMPurify.removeHook = function(entryPoint) {\n    if (hooks[entryPoint]) {\n      hooks[entryPoint].pop();\n    }\n  };\n\n  /**\n   * RemoveHooks\n   * Public method to remove all DOMPurify hooks at a given entryPoint\n   *\n   * @param  {String} entryPoint entry point for the hooks to remove\n   */\n  DOMPurify.removeHooks = function(entryPoint) {\n    if (hooks[entryPoint]) {\n      hooks[entryPoint] = [];\n    }\n  };\n\n  /**\n   * RemoveAllHooks\n   * Public method to remove all DOMPurify hooks\n   *\n   */\n  DOMPurify.removeAllHooks = function() {\n    hooks = {};\n  };\n\n  return DOMPurify;\n}\n\nexport default createDOMPurify();\n"], "names": ["freeze", "Object", "x", "html", "svg", "svgFilters", "mathMl", "text", "xml", "hasOwnProperty", "setPrototypeOf", "Reflect", "apply", "fun", "thisValue", "args", "addToSet", "set", "array", "l", "length", "element", "lcElement", "toLowerCase", "isFrozen", "clone", "object", "newObject", "property", "seal", "MUSTACHE_EXPR", "ERB_EXPR", "DATA_ATTR", "ARIA_ATTR", "IS_ALLOWED_URI", "IS_SCRIPT_OR_DATA", "ATTR_WHITESPACE", "arraySlice", "Array", "prototype", "slice", "getGlobal", "window", "_createTrustedTypesPolicy", "trustedTypes", "document", "createPolicy", "suffix", "ATTR_NAME", "currentScript", "hasAttribute", "getAttribute", "policyName", "error", "warn", "createDOMPurify", "DOMPurify", "root", "version", "VERSION", "removed", "nodeType", "isSupported", "originalDocument", "useDOMParser", "removeTitle", "DocumentFragment", "HTMLTemplateElement", "Node", "Node<PERSON><PERSON><PERSON>", "NamedNodeMap", "MozNamedAttrMap", "Text", "Comment", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TrustedTypes", "template", "createElement", "content", "ownerDocument", "trustedTypesPolicy", "emptyHTML", "createHTML", "implementation", "createNodeIterator", "getElementsByTagName", "createDocumentFragment", "importNode", "hooks", "createHTMLDocument", "documentMode", "EXPRESSIONS", "ALLOWED_TAGS", "DEFAULT_ALLOWED_TAGS", "TAGS", "ALLOWED_ATTR", "DEFAULT_ALLOWED_ATTR", "ATTRS", "FORBID_TAGS", "FORBID_ATTR", "ALLOW_ARIA_ATTR", "ALLOW_DATA_ATTR", "ALLOW_UNKNOWN_PROTOCOLS", "SAFE_FOR_JQUERY", "SAFE_FOR_TEMPLATES", "WHOLE_DOCUMENT", "SET_CONFIG", "FORCE_BODY", "RETURN_DOM", "RETURN_DOM_FRAGMENT", "RETURN_DOM_IMPORT", "SANITIZE_DOM", "KEEP_CONTENT", "IN_PLACE", "USE_PROFILES", "FORBID_CONTENTS", "DATA_URI_TAGS", "URI_SAFE_ATTRIBUTES", "DEFAULT_URI_SAFE_ATTRIBUTES", "CONFIG", "formElement", "_parseConfig", "cfg", "ADD_URI_SAFE_ATTR", "ALLOWED_URI_REGEXP", "ADD_TAGS", "ADD_ATTR", "table", "_forceRemove", "node", "push", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "outerHTML", "_removeAttribute", "name", "getAttributeNode", "removeAttribute", "_initDocument", "dirty", "doc", "leadingWhitespace", "matches", "match", "parseFromString", "documentElement", "body", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "insertBefore", "createTextNode", "childNodes", "call", "querySelector", "innerHTML", "_createIterator", "SHOW_ELEMENT", "SHOW_COMMENT", "SHOW_TEXT", "FILTER_ACCEPT", "_isClobbered", "elm", "nodeName", "textContent", "attributes", "setAttribute", "_isNode", "obj", "_executeHook", "entryPoint", "currentNode", "data", "for<PERSON>ach", "_sanitizeElements", "tagName", "insertAdjacentHTML", "htmlToInsert", "test", "cloneNode", "replace", "_isValidAttribute", "lcTag", "lcName", "value", "indexOf", "_sanitizeAttributes", "attr", "idAttr", "hookEvent", "namespaceURI", "trim", "attrName", "attrValue", "keepAttr", "id", "setAttributeNS", "pop", "_sanitizeShadowDOM", "fragment", "shadowNode", "shadowIterator", "nextNode", "sanitize", "importedNode", "oldNode", "returnNode", "toString", "TypeError", "toStaticHTML", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "nodeIterator", "serializedHTML", "setConfig", "clearConfig", "isValidAttribute", "tag", "addHook", "hookFunction", "removeH<PERSON>", "removeHooks", "removeAllHooks"], "mappings": ";;;;;;AAAA,IAAMA,WACJC,OAAOD,MAAP,IACA,UAASE,CAAT,EAAY;SACHA,CAAP;CAHJ;;AAMA,AAAO,IAAMC,OAAOH,SAAO,CACzB,GADyB,EAEzB,MAFyB,EAGzB,SAHyB,EAIzB,SAJyB,EAKzB,MALyB,EAMzB,SANyB,EAOzB,OAPyB,EAQzB,OARyB,EASzB,GATyB,EAUzB,KAVyB,EAWzB,KAXyB,EAYzB,KAZyB,EAazB,OAbyB,EAczB,YAdyB,EAezB,MAfyB,EAgBzB,IAhByB,EAiBzB,QAjByB,EAkBzB,QAlByB,EAmBzB,SAnByB,EAoBzB,QApByB,EAqBzB,MArByB,EAsBzB,MAtByB,EAuBzB,KAvByB,EAwBzB,UAxByB,EAyBzB,SAzByB,EA0BzB,MA1ByB,EA2BzB,UA3ByB,EA4BzB,IA5ByB,EA6BzB,WA7ByB,EA8BzB,KA9ByB,EA+BzB,SA/ByB,EAgCzB,KAhCyB,EAiCzB,KAjCyB,EAkCzB,KAlCyB,EAmCzB,IAnCyB,EAoCzB,IApCyB,EAqCzB,SArCyB,EAsCzB,IAtCyB,EAuCzB,UAvCyB,EAwCzB,YAxCyB,EAyCzB,QAzCyB,EA0CzB,MA1CyB,EA2CzB,QA3CyB,EA4CzB,MA5CyB,EA6CzB,IA7CyB,EA8CzB,IA9CyB,EA+CzB,IA/CyB,EAgDzB,IAhDyB,EAiDzB,IAjDyB,EAkDzB,IAlDyB,EAmDzB,MAnDyB,EAoDzB,QApDyB,EAqDzB,QArDyB,EAsDzB,IAtDyB,EAuDzB,MAvDyB,EAwDzB,GAxDyB,EAyDzB,KAzDyB,EA0DzB,OA1DyB,EA2DzB,KA3DyB,EA4DzB,KA5DyB,EA6DzB,OA7DyB,EA8DzB,QA9DyB,EA+DzB,IA/DyB,EAgEzB,MAhEyB,EAiEzB,KAjEyB,EAkEzB,MAlEyB,EAmEzB,SAnEyB,EAoEzB,MApEyB,EAqEzB,UArEyB,EAsEzB,OAtEyB,EAuEzB,KAvEyB,EAwEzB,MAxEyB,EAyEzB,IAzEyB,EA0EzB,UA1EyB,EA2EzB,QA3EyB,EA4EzB,QA5EyB,EA6EzB,GA7EyB,EA8EzB,KA9EyB,EA+EzB,UA/EyB,EAgFzB,GAhFyB,EAiFzB,IAjFyB,EAkFzB,IAlFyB,EAmFzB,MAnFyB,EAoFzB,GApFyB,EAqFzB,MArFyB,EAsFzB,SAtFyB,EAuFzB,QAvFyB,EAwFzB,QAxFyB,EAyFzB,OAzFyB,EA0FzB,QA1FyB,EA2FzB,QA3FyB,EA4FzB,MA5FyB,EA6FzB,QA7FyB,EA8FzB,QA9FyB,EA+FzB,OA/FyB,EAgGzB,KAhGyB,EAiGzB,SAjGyB,EAkGzB,KAlGyB,EAmGzB,OAnGyB,EAoGzB,OApGyB,EAqGzB,IArGyB,EAsGzB,UAtGyB,EAuGzB,UAvGyB,EAwGzB,OAxGyB,EAyGzB,IAzGyB,EA0GzB,OA1GyB,EA2GzB,MA3GyB,EA4GzB,IA5GyB,EA6GzB,OA7GyB,EA8GzB,IA9GyB,EA+GzB,GA/GyB,EAgHzB,IAhHyB,EAiHzB,KAjHyB,EAkHzB,OAlHyB,EAmHzB,KAnHyB,CAAP,CAAb;;;AAuHP,AAAO,IAAMI,MAAMJ,SAAO,CACxB,KADwB,EAExB,GAFwB,EAGxB,UAHwB,EAIxB,aAJwB,EAKxB,cALwB,EAMxB,cANwB,EAOxB,eAPwB,EAQxB,kBARwB,EASxB,OATwB,EAUxB,QAVwB,EAWxB,QAXwB,EAYxB,UAZwB,EAaxB,MAbwB,EAcxB,MAdwB,EAexB,SAfwB,EAgBxB,QAhBwB,EAiBxB,MAjBwB,EAkBxB,GAlBwB,EAmBxB,OAnBwB,EAoBxB,UApBwB,EAqBxB,OArBwB,EAsBxB,OAtBwB,EAuBxB,MAvBwB,EAwBxB,gBAxBwB,EAyBxB,QAzBwB,EA0BxB,MA1BwB,EA2BxB,UA3BwB,EA4BxB,OA5BwB,EA6BxB,MA7BwB,EA8BxB,SA9BwB,EA+BxB,SA/BwB,EAgCxB,UAhCwB,EAiCxB,gBAjCwB,EAkCxB,MAlCwB,EAmCxB,MAnCwB,EAoCxB,OApCwB,EAqCxB,QArCwB,EAsCxB,QAtCwB,EAuCxB,MAvCwB,EAwCxB,UAxCwB,EAyCxB,OAzCwB,EA0CxB,MA1CwB,EA2CxB,OA3CwB,EA4CxB,OA5CwB,EA6CxB,MA7CwB,EA8CxB,OA9CwB,CAAP,CAAZ;;AAiDP,AAAO,IAAMK,aAAaL,SAAO,CAC/B,SAD+B,EAE/B,eAF+B,EAG/B,qBAH+B,EAI/B,aAJ+B,EAK/B,kBAL+B,EAM/B,mBAN+B,EAO/B,mBAP+B,EAQ/B,gBAR+B,EAS/B,SAT+B,EAU/B,SAV+B,EAW/B,SAX+B,EAY/B,SAZ+B,EAa/B,SAb+B,EAc/B,gBAd+B,EAe/B,SAf+B,EAgB/B,aAhB+B,EAiB/B,cAjB+B,EAkB/B,UAlB+B,EAmB/B,cAnB+B,EAoB/B,oBApB+B,EAqB/B,aArB+B,EAsB/B,QAtB+B,EAuB/B,cAvB+B,CAAP,CAAnB;;AA0BP,AAAO,IAAMM,SAASN,SAAO,CAC3B,MAD2B,EAE3B,UAF2B,EAG3B,QAH2B,EAI3B,SAJ2B,EAK3B,OAL2B,EAM3B,QAN2B,EAO3B,IAP2B,EAQ3B,YAR2B,EAS3B,eAT2B,EAU3B,IAV2B,EAW3B,IAX2B,EAY3B,OAZ2B,EAa3B,SAb2B,EAc3B,UAd2B,EAe3B,OAf2B,EAgB3B,MAhB2B,EAiB3B,IAjB2B,EAkB3B,QAlB2B,EAmB3B,OAnB2B,EAoB3B,QApB2B,EAqB3B,MArB2B,EAsB3B,MAtB2B,EAuB3B,SAvB2B,EAwB3B,QAxB2B,EAyB3B,KAzB2B,EA0B3B,OA1B2B,EA2B3B,KA3B2B,EA4B3B,QA5B2B,EA6B3B,YA7B2B,CAAP,CAAf;;AAgCP,AAAO,IAAMO,OAAOP,SAAO,CAAC,OAAD,CAAP,CAAb;;ACxOP,IAAMA,WACJC,OAAOD,MAAP,IACA,UAASE,CAAT,EAAY;SACHA,CAAP;CAHJ;;AAMA,AAAO,IAAMC,SAAOH,SAAO,CACzB,QADyB,EAEzB,QAFyB,EAGzB,OAHyB,EAIzB,KAJyB,EAKzB,cALyB,EAMzB,YANyB,EAOzB,SAPyB,EAQzB,QARyB,EASzB,aATyB,EAUzB,aAVyB,EAWzB,SAXyB,EAYzB,MAZyB,EAazB,OAbyB,EAczB,OAdyB,EAezB,OAfyB,EAgBzB,MAhByB,EAiBzB,SAjByB,EAkBzB,UAlByB,EAmBzB,QAnByB,EAoBzB,aApByB,EAqBzB,UArByB,EAsBzB,SAtByB,EAuBzB,KAvByB,EAwBzB,UAxByB,EAyBzB,UAzByB,EA0BzB,SA1ByB,EA2BzB,MA3ByB,EA4BzB,KA5ByB,EA6BzB,SA7ByB,EA8BzB,QA9ByB,EA+BzB,QA/ByB,EAgCzB,MAhCyB,EAiCzB,MAjCyB,EAkCzB,UAlCyB,EAmCzB,IAnCyB,EAoCzB,WApCyB,EAqCzB,OArCyB,EAsCzB,OAtCyB,EAuCzB,MAvCyB,EAwCzB,MAxCyB,EAyCzB,MAzCyB,EA0CzB,KA1CyB,EA2CzB,KA3CyB,EA4CzB,WA5CyB,EA6CzB,OA7CyB,EA8CzB,QA9CyB,EA+CzB,KA/CyB,EAgDzB,UAhDyB,EAiDzB,MAjDyB,EAkDzB,SAlDyB,EAmDzB,YAnDyB,EAoDzB,QApDyB,EAqDzB,MArDyB,EAsDzB,SAtDyB,EAuDzB,SAvDyB,EAwDzB,aAxDyB,EAyDzB,QAzDyB,EA0DzB,SA1DyB,EA2DzB,SA3DyB,EA4DzB,YA5DyB,EA6DzB,UA7DyB,EA8DzB,KA9DyB,EA+DzB,UA/DyB,EAgEzB,KAhEyB,EAiEzB,UAjEyB,EAkEzB,MAlEyB,EAmEzB,MAnEyB,EAoEzB,SApEyB,EAqEzB,YArEyB,EAsEzB,OAtEyB,EAuEzB,UAvEyB,EAwEzB,OAxEyB,EAyEzB,MAzEyB,EA0EzB,OA1EyB,EA2EzB,MA3EyB,EA4EzB,SA5EyB,EA6EzB,OA7EyB,EA8EzB,KA9EyB,EA+EzB,QA/EyB,EAgFzB,MAhFyB,EAiFzB,OAjFyB,EAkFzB,SAlFyB,EAmFzB,UAnFyB,EAoFzB,OApFyB,EAqFzB,MArFyB,EAsFzB,QAtFyB,EAuFzB,QAvFyB,EAwFzB,OAxFyB,EAyFzB,OAzFyB,EA0FzB,OA1FyB,CAAP,CAAb;;AA6FP,AAAO,IAAMI,QAAMJ,SAAO,CACxB,eADwB,EAExB,YAFwB,EAGxB,UAHwB,EAIxB,oBAJwB,EAKxB,QALwB,EAMxB,eANwB,EAOxB,eAPwB,EAQxB,SARwB,EASxB,eATwB,EAUxB,gBAVwB,EAWxB,OAXwB,EAYxB,MAZwB,EAaxB,IAbwB,EAcxB,OAdwB,EAexB,MAfwB,EAgBxB,WAhBwB,EAiBxB,WAjBwB,EAkBxB,OAlBwB,EAmBxB,qBAnBwB,EAoBxB,6BApBwB,EAqBxB,eArBwB,EAsBxB,iBAtBwB,EAuBxB,IAvBwB,EAwBxB,IAxBwB,EAyBxB,GAzBwB,EA0BxB,IA1BwB,EA2BxB,IA3BwB,EA4BxB,iBA5BwB,EA6BxB,WA7BwB,EA8BxB,SA9BwB,EA+BxB,SA/BwB,EAgCxB,KAhCwB,EAiCxB,UAjCwB,EAkCxB,WAlCwB,EAmCxB,KAnCwB,EAoCxB,MApCwB,EAqCxB,cArCwB,EAsCxB,WAtCwB,EAuCxB,QAvCwB,EAwCxB,aAxCwB,EAyCxB,aAzCwB,EA0CxB,eA1CwB,EA2CxB,aA3CwB,EA4CxB,WA5CwB,EA6CxB,kBA7CwB,EA8CxB,cA9CwB,EA+CxB,YA/CwB,EAgDxB,cAhDwB,EAiDxB,aAjDwB,EAkDxB,IAlDwB,EAmDxB,IAnDwB,EAoDxB,IApDwB,EAqDxB,IArDwB,EAsDxB,YAtDwB,EAuDxB,UAvDwB,EAwDxB,eAxDwB,EAyDxB,mBAzDwB,EA0DxB,QA1DwB,EA2DxB,MA3DwB,EA4DxB,IA5DwB,EA6DxB,iBA7DwB,EA8DxB,IA9DwB,EA+DxB,KA/DwB,EAgExB,GAhEwB,EAiExB,IAjEwB,EAkExB,IAlEwB,EAmExB,IAnEwB,EAoExB,IApEwB,EAqExB,SArEwB,EAsExB,WAtEwB,EAuExB,YAvEwB,EAwExB,UAxEwB,EAyExB,MAzEwB,EA0ExB,cA1EwB,EA2ExB,gBA3EwB,EA4ExB,cA5EwB,EA6ExB,kBA7EwB,EA8ExB,gBA9EwB,EA+ExB,OA/EwB,EAgFxB,YAhFwB,EAiFxB,YAjFwB,EAkFxB,cAlFwB,EAmFxB,cAnFwB,EAoFxB,aApFwB,EAqFxB,aArFwB,EAsFxB,kBAtFwB,EAuFxB,WAvFwB,EAwFxB,KAxFwB,EAyFxB,MAzFwB,EA0FxB,OA1FwB,EA2FxB,QA3FwB,EA4FxB,MA5FwB,EA6FxB,KA7FwB,EA8FxB,MA9FwB,EA+FxB,YA/FwB,EAgGxB,QAhGwB,EAiGxB,UAjGwB,EAkGxB,SAlGwB,EAmGxB,OAnGwB,EAoGxB,QApGwB,EAqGxB,aArGwB,EAsGxB,QAtGwB,EAuGxB,UAvGwB,EAwGxB,aAxGwB,EAyGxB,MAzGwB,EA0GxB,YA1GwB,EA2GxB,qBA3GwB,EA4GxB,kBA5GwB,EA6GxB,cA7GwB,EA8GxB,QA9GwB,EA+GxB,eA/GwB,EAgHxB,qBAhHwB,EAiHxB,gBAjHwB,EAkHxB,GAlHwB,EAmHxB,IAnHwB,EAoHxB,IApHwB,EAqHxB,QArHwB,EAsHxB,MAtHwB,EAuHxB,MAvHwB,EAwHxB,aAxHwB,EAyHxB,WAzHwB,EA0HxB,SA1HwB,EA2HxB,QA3HwB,EA4HxB,QA5HwB,EA6HxB,OA7HwB,EA8HxB,MA9HwB,EA+HxB,iBA/HwB,EAgIxB,kBAhIwB,EAiIxB,kBAjIwB,EAkIxB,cAlIwB,EAmIxB,cAnIwB,EAoIxB,aApIwB,EAqIxB,YArIwB,EAsIxB,cAtIwB,EAuIxB,kBAvIwB,EAwIxB,mBAxIwB,EAyIxB,gBAzIwB,EA0IxB,iBA1IwB,EA2IxB,mBA3IwB,EA4IxB,gBA5IwB,EA6IxB,QA7IwB,EA8IxB,cA9IwB,EA+IxB,OA/IwB,EAgJxB,cAhJwB,EAiJxB,UAjJwB,EAkJxB,SAlJwB,EAmJxB,SAnJwB,EAoJxB,WApJwB,EAqJxB,aArJwB,EAsJxB,iBAtJwB,EAuJxB,gBAvJwB,EAwJxB,YAxJwB,EAyJxB,MAzJwB,EA0JxB,IA1JwB,EA2JxB,IA3JwB,EA4JxB,SA5JwB,EA6JxB,QA7JwB,EA8JxB,SA9JwB,EA+JxB,YA/JwB,EAgKxB,SAhKwB,EAiKxB,YAjKwB,EAkKxB,eAlKwB,EAmKxB,eAnKwB,EAoKxB,OApKwB,EAqKxB,cArKwB,EAsKxB,MAtKwB,EAuKxB,cAvKwB,EAwKxB,kBAxKwB,EAyKxB,kBAzKwB,EA0KxB,GA1KwB,EA2KxB,IA3KwB,EA4KxB,IA5KwB,EA6KxB,OA7KwB,EA8KxB,GA9KwB,EA+KxB,IA/KwB,EAgLxB,IAhLwB,EAiLxB,GAjLwB,EAkLxB,YAlLwB,CAAP,CAAZ;;AAqLP,AAAO,IAAMM,WAASN,SAAO,CAC3B,QAD2B,EAE3B,aAF2B,EAG3B,OAH2B,EAI3B,UAJ2B,EAK3B,OAL2B,EAM3B,cAN2B,EAO3B,aAP2B,EAQ3B,YAR2B,EAS3B,YAT2B,EAU3B,OAV2B,EAW3B,KAX2B,EAY3B,SAZ2B,EAa3B,cAb2B,EAc3B,OAd2B,EAe3B,OAf2B,EAgB3B,QAhB2B,EAiB3B,MAjB2B,EAkB3B,IAlB2B,EAmB3B,SAnB2B,EAoB3B,QApB2B,EAqB3B,eArB2B,EAsB3B,QAtB2B,EAuB3B,QAvB2B,EAwB3B,gBAxB2B,EAyB3B,WAzB2B,EA0B3B,UA1B2B,EA2B3B,aA3B2B,EA4B3B,SA5B2B,EA6B3B,SA7B2B,EA8B3B,eA9B2B,EA+B3B,UA/B2B,EAgC3B,UAhC2B,EAiC3B,MAjC2B,EAkC3B,UAlC2B,EAmC3B,UAnC2B,EAoC3B,YApC2B,EAqC3B,SArC2B,EAsC3B,QAtC2B,EAuC3B,QAvC2B,EAwC3B,aAxC2B,EAyC3B,eAzC2B,EA0C3B,sBA1C2B,EA2C3B,WA3C2B,EA4C3B,WA5C2B,EA6C3B,YA7C2B,EA8C3B,UA9C2B,EA+C3B,gBA/C2B,EAgD3B,gBAhD2B,EAiD3B,WAjD2B,EAkD3B,SAlD2B,EAmD3B,OAnD2B,EAoD3B,OApD2B,CAAP,CAAf;;AAuDP,AAAO,IAAMQ,MAAMR,SAAO,CACxB,YADwB,EAExB,QAFwB,EAGxB,aAHwB,EAIxB,WAJwB,EAKxB,aALwB,CAAP,CAAZ;;IC/UCS,iBAAmCR,OAAnCQ;IAAgBC,iBAAmBT,OAAnBS;;aACR,OAAOC,OAAP,KAAmB,WAAnB,IAAkCA;IAA5CC,iBAAAA;;AAEN,IAAI,CAACA,OAAL,EAAY;YACF,eAASC,GAAT,EAAcC,SAAd,EAAyBC,IAAzB,EAA+B;WAC9BF,IAAID,KAAJ,CAAUE,SAAV,EAAqBC,IAArB,CAAP;GADF;;;;AAMF,AAAO,SAASC,QAAT,CAAkBC,GAAlB,EAAuBC,KAAvB,EAA8B;MAC/BR,cAAJ,EAAoB;;;;mBAIHO,GAAf,EAAoB,IAApB;;;MAGEE,IAAID,MAAME,MAAd;SACOD,GAAP,EAAY;QACNE,UAAUH,MAAMC,CAAN,CAAd;QACI,OAAOE,OAAP,KAAmB,QAAvB,EAAiC;UACzBC,YAAYD,QAAQE,WAAR,EAAlB;UACID,cAAcD,OAAlB,EAA2B;;YAErB,CAACpB,OAAOuB,QAAP,CAAgBN,KAAhB,CAAL,EAA6B;gBACrBC,CAAN,IAAWG,SAAX;;;kBAGQA,SAAV;;;;QAIAD,OAAJ,IAAe,IAAf;;;SAGKJ,GAAP;;;;AAIF,AAAO,SAASQ,KAAT,CAAeC,MAAf,EAAuB;MACtBC,YAAY,EAAlB;;MAEIC,iBAAJ;OACKA,QAAL,IAAiBF,MAAjB,EAAyB;QACnBd,QAAMH,cAAN,EAAsBiB,MAAtB,EAA8B,CAACE,QAAD,CAA9B,CAAJ,EAA+C;gBACnCA,QAAV,IAAsBF,OAAOE,QAAP,CAAtB;;;;SAIGD,SAAP;;;AClDF,IAAME,OACJ5B,OAAO4B,IAAP,IACA,UAAS3B,CAAT,EAAY;SACHA,CAAP;CAHJ;;AAMA,AAAO,IAAM4B,gBAAgBD,KAAK,2BAAL,CAAtB;AACP,AAAO,IAAME,WAAWF,KAAK,uBAAL,CAAjB;AACP,AAAO,IAAMG,YAAYH,KAAK,4BAAL,CAAlB;AACP,AAAO,IAAMI,YAAYJ,KAAK,gBAAL,CAAlB;AACP,AAAO,IAAMK,iBAAiBL,KAC5B,uFAD4B;CAAvB;AAGP,AAAO,IAAMM,oBAAoBN,KAAK,uBAAL,CAA1B;AACP,AAAO,IAAMO,kBAAkBP,KAC7B,6DAD6B;CAAxB;;;;;;ACdP,WAKgB,OAAOlB,OAAP,KAAmB,WAAnB,IAAkCA;IAA5CC,aAAAA;;IACSyB,aAAeC,MAAMC,UAA5BC;IACAxC,SAAWC,OAAXD;;AACR,IAAMyC,YAAY,SAAZA,SAAY;SAAO,OAAOC,MAAP,KAAkB,WAAlB,GAAgC,IAAhC,GAAuCA,MAA9C;CAAlB;;AAEA,IAAI,CAAC9B,KAAL,EAAY;UACF,eAASC,GAAT,EAAcC,SAAd,EAAyBC,IAAzB,EAA+B;WAC9BF,IAAID,KAAJ,CAAUE,SAAV,EAAqBC,IAArB,CAAP;GADF;;;;;;;;;;;AAaF,IAAM4B,4BAA4B,SAA5BA,yBAA4B,CAASC,YAAT,EAAuBC,QAAvB,EAAiC;MAE/D,QAAOD,YAAP,yCAAOA,YAAP,OAAwB,QAAxB,IACA,OAAOA,aAAaE,YAApB,KAAqC,UAFvC,EAGE;WACO,IAAP;;;;;;MAMEC,SAAS,IAAb;MACMC,YAAY,uBAAlB;MAEEH,SAASI,aAAT,IACAJ,SAASI,aAAT,CAAuBC,YAAvB,CAAoCF,SAApC,CAFF,EAGE;aACSH,SAASI,aAAT,CAAuBE,YAAvB,CAAoCH,SAApC,CAAT;;;MAGII,aAAa,eAAeL,SAAS,MAAMA,MAAf,GAAwB,EAAvC,CAAnB;;MAEI;WACKH,aAAaE,YAAb,CAA0BM,UAA1B,EAAsC;gBAAA,sBAChCjD,OADgC,EAC1B;eACRA,OAAP;;KAFG,CAAP;GADF,CAME,OAAOkD,KAAP,EAAc;;;;YAINC,IAAR,CACE,yBAAyBF,UAAzB,GAAsC,wBADxC;WAGO,IAAP;;CAnCJ;;AAuCA,SAASG,eAAT,GAA+C;MAAtBb,MAAsB,uEAAbD,WAAa;;MACvCe,YAAY,SAAZA,SAAY;WAAQD,gBAAgBE,IAAhB,CAAR;GAAlB;;;;;;YAMUC,OAAV,GAAoBC,QAApB;;;;;;YAMUC,OAAV,GAAoB,EAApB;;MAEI,CAAClB,MAAD,IAAW,CAACA,OAAOG,QAAnB,IAA+BH,OAAOG,QAAP,CAAgBgB,QAAhB,KAA6B,CAAhE,EAAmE;;;cAGvDC,WAAV,GAAwB,KAAxB;;WAEON,SAAP;;;MAGIO,mBAAmBrB,OAAOG,QAAhC;MACImB,eAAe,KAAnB;MACIC,cAAc,KAAlB;;MAEMpB,QA3BuC,GA2B1BH,MA3B0B,CA2BvCG,QA3BuC;MA6B3CqB,gBA7B2C,GAsCzCxB,MAtCyC,CA6B3CwB,gBA7B2C;MA8B3CC,mBA9B2C,GAsCzCzB,MAtCyC,CA8B3CyB,mBA9B2C;MA+B3CC,IA/B2C,GAsCzC1B,MAtCyC,CA+B3C0B,IA/B2C;MAgC3CC,UAhC2C,GAsCzC3B,MAtCyC,CAgC3C2B,UAhC2C;6BAsCzC3B,MAtCyC,CAiC3C4B,YAjC2C;MAiC3CA,YAjC2C,wCAiC5B5B,OAAO4B,YAAP,IAAuB5B,OAAO6B,eAjCF;MAkC3CC,IAlC2C,GAsCzC9B,MAtCyC,CAkC3C8B,IAlC2C;MAmC3CC,OAnC2C,GAsCzC/B,MAtCyC,CAmC3C+B,OAnC2C;MAoC3CC,SApC2C,GAsCzChC,MAtCyC,CAoC3CgC,SApC2C;MAqC3CC,YArC2C,GAsCzCjC,MAtCyC,CAqC3CiC,YArC2C;;;;;;;;;MA8CzC,OAAOR,mBAAP,KAA+B,UAAnC,EAA+C;QACvCS,WAAW/B,SAASgC,aAAT,CAAuB,UAAvB,CAAjB;QACID,SAASE,OAAT,IAAoBF,SAASE,OAAT,CAAiBC,aAAzC,EAAwD;iBAC3CH,SAASE,OAAT,CAAiBC,aAA5B;;;;MAIEC,qBAAqBrC,0BACzBgC,YADyB,EAEzBZ,gBAFyB,CAA3B;MAIMkB,YAAYD,qBAAqBA,mBAAmBE,UAAnB,CAA8B,EAA9B,CAArB,GAAyD,EAA3E;;kBAOIrC,QAhEyC;MA4D3CsC,cA5D2C,aA4D3CA,cA5D2C;MA6D3CC,kBA7D2C,aA6D3CA,kBA7D2C;MA8D3CC,oBA9D2C,aA8D3CA,oBA9D2C;MA+D3CC,sBA/D2C,aA+D3CA,sBA/D2C;MAiErCC,UAjEqC,GAiEtBxB,gBAjEsB,CAiErCwB,UAjEqC;;;MAmEzCC,QAAQ,EAAZ;;;;;YAKU1B,WAAV,GACEqB,kBACA,OAAOA,eAAeM,kBAAtB,KAA6C,WAD7C,IAEA5C,SAAS6C,YAAT,KAA0B,CAH5B;;MAME5D,gBA9E2C,GAoFzC6D,aApFyC;MA+E3C5D,WA/E2C,GAoFzC4D,QApFyC;MAgF3C3D,YAhF2C,GAoFzC2D,SApFyC;MAiF3C1D,YAjF2C,GAoFzC0D,SApFyC;MAkF3CxD,oBAlF2C,GAoFzCwD,iBApFyC;MAmF3CvD,kBAnF2C,GAoFzCuD,eApFyC;MAsFvCzD,iBAtFuC,GAsFpByD,cAtFoB;;;;;;;;MA6FzCC,eAAe,IAAnB;MACMC,uBAAuB7E,SAAS,EAAT,+BACxB8E,IADwB,sBAExBA,GAFwB,sBAGxBA,UAHwB,sBAIxBA,MAJwB,sBAKxBA,IALwB,GAA7B;;;MASIC,eAAe,IAAnB;MACMC,uBAAuBhF,SAAS,EAAT,+BACxBiF,MADwB,sBAExBA,KAFwB,sBAGxBA,QAHwB,sBAIxBA,GAJwB,GAA7B;;;MAQIC,cAAc,IAAlB;;;MAGIC,cAAc,IAAlB;;;MAGIC,kBAAkB,IAAtB;;;MAGIC,kBAAkB,IAAtB;;;MAGIC,0BAA0B,KAA9B;;;MAGIC,kBAAkB,KAAtB;;;;;MAKIC,qBAAqB,KAAzB;;;MAGIC,iBAAiB,KAArB;;;MAGIC,aAAa,KAAjB;;;;MAIIC,aAAa,KAAjB;;;;;;MAMIC,aAAa,KAAjB;;;;MAIIC,sBAAsB,KAA1B;;;;;;MAMIC,oBAAoB,KAAxB;;;MAGIC,eAAe,IAAnB;;;MAGIC,eAAe,IAAnB;;;;MAIIC,WAAW,KAAf;;;MAGIC,eAAe,EAAnB;;;MAGMC,kBAAkBnG,SAAS,EAAT,EAAa,CACnC,OADmC,EAEnC,MAFmC,EAGnC,MAHmC,EAInC,QAJmC,EAKnC,OALmC,EAMnC,UANmC,EAOnC,KAPmC,EAQnC,OARmC,CAAb,CAAxB;;;MAYMoG,gBAAgBpG,SAAS,EAAT,EAAa,CACjC,OADiC,EAEjC,OAFiC,EAGjC,KAHiC,EAIjC,QAJiC,EAKjC,OALiC,CAAb,CAAtB;;;MASIqG,sBAAsB,IAA1B;MACMC,8BAA8BtG,SAAS,EAAT,EAAa,CAC/C,KAD+C,EAE/C,OAF+C,EAG/C,KAH+C,EAI/C,IAJ+C,EAK/C,OAL+C,EAM/C,MAN+C,EAO/C,SAP+C,EAQ/C,aAR+C,EAS/C,SAT+C,EAU/C,OAV+C,EAW/C,OAX+C,EAY/C,OAZ+C,EAa/C,OAb+C,CAAb,CAApC;;;MAiBIuG,SAAS,IAAb;;;;;MAKMC,cAAc3E,SAASgC,aAAT,CAAuB,MAAvB,CAApB;;;;;;;;MAQM4C,eAAe,SAAfA,YAAe,CAASC,GAAT,EAAc;QAC7BH,UAAUA,WAAWG,GAAzB,EAA8B;;;;;QAK1B,CAACA,GAAD,IAAQ,QAAOA,GAAP,yCAAOA,GAAP,OAAe,QAA3B,EAAqC;YAC7B,EAAN;;;;mBAKA,kBAAkBA,GAAlB,GACI1G,SAAS,EAAT,EAAa0G,IAAI9B,YAAjB,CADJ,GAEIC,oBAHN;mBAKE,kBAAkB6B,GAAlB,GACI1G,SAAS,EAAT,EAAa0G,IAAI3B,YAAjB,CADJ,GAEIC,oBAHN;0BAKE,uBAAuB0B,GAAvB,GACI1G,SAAS,EAAT,EAAa0G,IAAIC,iBAAjB,CADJ,GAEIL,2BAHN;kBAIc,iBAAiBI,GAAjB,GAAuB1G,SAAS,EAAT,EAAa0G,IAAIxB,WAAjB,CAAvB,GAAuD,EAArE;kBACc,iBAAiBwB,GAAjB,GAAuB1G,SAAS,EAAT,EAAa0G,IAAIvB,WAAjB,CAAvB,GAAuD,EAArE;mBACe,kBAAkBuB,GAAlB,GAAwBA,IAAIR,YAA5B,GAA2C,KAA1D;sBACkBQ,IAAItB,eAAJ,KAAwB,KAA1C,CA1BiC;sBA2BfsB,IAAIrB,eAAJ,KAAwB,KAA1C,CA3BiC;8BA4BPqB,IAAIpB,uBAAJ,IAA+B,KAAzD,CA5BiC;sBA6BfoB,IAAInB,eAAJ,IAAuB,KAAzC,CA7BiC;yBA8BZmB,IAAIlB,kBAAJ,IAA0B,KAA/C,CA9BiC;qBA+BhBkB,IAAIjB,cAAJ,IAAsB,KAAvC,CA/BiC;iBAgCpBiB,IAAId,UAAJ,IAAkB,KAA/B,CAhCiC;0BAiCXc,IAAIb,mBAAJ,IAA2B,KAAjD,CAjCiC;wBAkCba,IAAIZ,iBAAJ,IAAyB,KAA7C,CAlCiC;iBAmCpBY,IAAIf,UAAJ,IAAkB,KAA/B,CAnCiC;mBAoClBe,IAAIX,YAAJ,KAAqB,KAApC,CApCiC;mBAqClBW,IAAIV,YAAJ,KAAqB,KAApC,CArCiC;eAsCtBU,IAAIT,QAAJ,IAAgB,KAA3B,CAtCiC;;wBAwChBS,IAAIE,kBAAJ,IAA0B1F,iBAA3C;;QAEIsE,kBAAJ,EAAwB;wBACJ,KAAlB;;;QAGEK,mBAAJ,EAAyB;mBACV,IAAb;;;;QAIEK,YAAJ,EAAkB;qBACDlG,SAAS,EAAT,+BAAiB8E,IAAjB,GAAf;qBACe,EAAf;UACIoB,aAAa/G,IAAb,KAAsB,IAA1B,EAAgC;iBACrByF,YAAT,EAAuBE,IAAvB;iBACSC,YAAT,EAAuBE,MAAvB;;;UAGEiB,aAAa9G,GAAb,KAAqB,IAAzB,EAA+B;iBACpBwF,YAAT,EAAuBE,GAAvB;iBACSC,YAAT,EAAuBE,KAAvB;iBACSF,YAAT,EAAuBE,GAAvB;;;UAGEiB,aAAa7G,UAAb,KAA4B,IAAhC,EAAsC;iBAC3BuF,YAAT,EAAuBE,UAAvB;iBACSC,YAAT,EAAuBE,KAAvB;iBACSF,YAAT,EAAuBE,GAAvB;;;UAGEiB,aAAa5G,MAAb,KAAwB,IAA5B,EAAkC;iBACvBsF,YAAT,EAAuBE,MAAvB;iBACSC,YAAT,EAAuBE,QAAvB;iBACSF,YAAT,EAAuBE,GAAvB;;;;;QAKAyB,IAAIG,QAAR,EAAkB;UACZjC,iBAAiBC,oBAArB,EAA2C;uBAC1BpE,MAAMmE,YAAN,CAAf;;;eAGOA,YAAT,EAAuB8B,IAAIG,QAA3B;;;QAGEH,IAAII,QAAR,EAAkB;UACZ/B,iBAAiBC,oBAArB,EAA2C;uBAC1BvE,MAAMsE,YAAN,CAAf;;;eAGOA,YAAT,EAAuB2B,IAAII,QAA3B;;;QAGEJ,IAAIC,iBAAR,EAA2B;eAChBN,mBAAT,EAA8BK,IAAIC,iBAAlC;;;;QAIEX,YAAJ,EAAkB;mBACH,OAAb,IAAwB,IAAxB;;;;QAIEP,cAAJ,EAAoB;eACTb,YAAT,EAAuB,CAAC,MAAD,EAAS,MAAT,EAAiB,MAAjB,CAAvB;;;;QAIEA,aAAamC,KAAjB,EAAwB;eACbnC,YAAT,EAAuB,CAAC,OAAD,CAAvB;;;;;QAKE5F,MAAJ,EAAY;aACH0H,GAAP;;;aAGOA,GAAT;GAxHF;;;;;;;MAgIMM,eAAe,SAAfA,YAAe,CAASC,IAAT,EAAe;cACxBrE,OAAV,CAAkBsE,IAAlB,CAAuB,EAAE7G,SAAS4G,IAAX,EAAvB;QACI;WACGE,UAAL,CAAgBC,WAAhB,CAA4BH,IAA5B;KADF,CAEE,OAAO5E,KAAP,EAAc;WACTgF,SAAL,GAAiBpD,SAAjB;;GALJ;;;;;;;;MAeMqD,mBAAmB,SAAnBA,gBAAmB,CAASC,IAAT,EAAeN,IAAf,EAAqB;QACxC;gBACQrE,OAAV,CAAkBsE,IAAlB,CAAuB;mBACVD,KAAKO,gBAAL,CAAsBD,IAAtB,CADU;cAEfN;OAFR;KADF,CAKE,OAAO5E,KAAP,EAAc;gBACJO,OAAV,CAAkBsE,IAAlB,CAAuB;mBACV,IADU;cAEfD;OAFR;;;SAMGQ,eAAL,CAAqBF,IAArB;GAbF;;;;;;;;MAsBMG,gBAAgB,SAAhBA,aAAgB,CAASC,KAAT,EAAgB;;QAEhCC,YAAJ;QACIC,0BAAJ;;QAEIlC,UAAJ,EAAgB;cACN,sBAAsBgC,KAA9B;KADF,MAEO;;UAECG,UAAUH,MAAMI,KAAN,CAAY,QAAZ,CAAhB;0BACoBD,WAAWA,QAAQ,CAAR,CAA/B;UACID,iBAAJ,EAAuB;gBACbF,MAAMnG,KAAN,CAAYqG,kBAAkBzH,MAA9B,CAAR;;;;;QAKA4C,YAAJ,EAAkB;UACZ;cACI,IAAIU,SAAJ,GAAgBsE,eAAhB,CAAgCL,KAAhC,EAAuC,WAAvC,CAAN;OADF,CAEE,OAAOtF,KAAP,EAAc;;;;QAIdY,WAAJ,EAAiB;eACNiC,WAAT,EAAsB,CAAC,OAAD,CAAtB;;;;;QAKE,CAAC0C,GAAD,IAAQ,CAACA,IAAIK,eAAjB,EAAkC;YAC1B9D,eAAeM,kBAAf,CAAkC,EAAlC,CAAN;iBACiBmD,GAFe;UAExBM,IAFwB,QAExBA,IAFwB;;WAG3Bf,UAAL,CAAgBC,WAAhB,CAA4Bc,KAAKf,UAAL,CAAgBgB,iBAA5C;WACKd,SAAL,GAAiBrD,qBACbA,mBAAmBE,UAAnB,CAA8ByD,KAA9B,CADa,GAEbA,KAFJ;;;QAKEE,iBAAJ,EAAuB;UACjBK,IAAJ,CAASE,YAAT,CACEvG,SAASwG,cAAT,CAAwBR,iBAAxB,CADF,EAEED,IAAIM,IAAJ,CAASI,UAAT,CAAoB,CAApB,KAA0B,IAF5B;;;;WAOKjE,qBAAqBkE,IAArB,CAA0BX,GAA1B,EAA+BnC,iBAAiB,MAAjB,GAA0B,MAAzD,EAAiE,CAAjE,CAAP;GA/CF;;;;;;;;;;;MA2DIjD,UAAUM,WAAd,EAA2B;KACxB,YAAW;UACN;YACI8E,MAAMF,cACV,2DADU,CAAZ;YAGIE,IAAIY,aAAJ,CAAkB,SAAlB,CAAJ,EAAkC;yBACjB,IAAf;;OALJ,CAOE,OAAOnG,KAAP,EAAc;KARlB;;KAWC,YAAW;UACN;YACIuF,MAAMF,cAAc,sCAAd,CAAZ;YACIE,IAAIY,aAAJ,CAAkB,OAAlB,EAA2BC,SAA3B,CAAqCV,KAArC,CAA2C,UAA3C,CAAJ,EAA4D;wBAC5C,IAAd;;OAHJ,CAKE,OAAO1F,KAAP,EAAc;KANlB;;;;;;;;;MAgBIqG,kBAAkB,SAAlBA,eAAkB,CAASjG,IAAT,EAAe;WAC9B2B,mBAAmBmE,IAAnB,CACL9F,KAAKsB,aAAL,IAAsBtB,IADjB,EAELA,IAFK,EAGLY,WAAWsF,YAAX,GAA0BtF,WAAWuF,YAArC,GAAoDvF,WAAWwF,SAH1D,EAIL,YAAM;aACGxF,WAAWyF,aAAlB;KALG,EAOL,KAPK,CAAP;GADF;;;;;;;;MAkBMC,eAAe,SAAfA,YAAe,CAASC,GAAT,EAAc;QAC7BA,eAAexF,IAAf,IAAuBwF,eAAevF,OAA1C,EAAmD;aAC1C,KAAP;;;QAIA,OAAOuF,IAAIC,QAAX,KAAwB,QAAxB,IACA,OAAOD,IAAIE,WAAX,KAA2B,QAD3B,IAEA,OAAOF,IAAI5B,WAAX,KAA2B,UAF3B,IAGA,EAAE4B,IAAIG,UAAJ,YAA0B7F,YAA5B,CAHA,IAIA,OAAO0F,IAAIvB,eAAX,KAA+B,UAJ/B,IAKA,OAAOuB,IAAII,YAAX,KAA4B,UAN9B,EAOE;aACO,IAAP;;;WAGK,KAAP;GAhBF;;;;;;;;MAyBMC,UAAU,SAAVA,OAAU,CAASC,GAAT,EAAc;WACrB,QAAOlG,IAAP,yCAAOA,IAAP,OAAgB,QAAhB,GACHkG,eAAelG,IADZ,GAEHkG,OACE,QAAOA,GAAP,yCAAOA,GAAP,OAAe,QADjB,IAEE,OAAOA,IAAIzG,QAAX,KAAwB,QAF1B,IAGE,OAAOyG,IAAIL,QAAX,KAAwB,QAL9B;GADF;;;;;;;;;;MAiBMM,eAAe,SAAfA,YAAe,CAASC,UAAT,EAAqBC,WAArB,EAAkCC,IAAlC,EAAwC;QACvD,CAAClF,MAAMgF,UAAN,CAAL,EAAwB;;;;UAIlBA,UAAN,EAAkBG,OAAlB,CAA0B,gBAAQ;WAC3BpB,IAAL,CAAU/F,SAAV,EAAqBiH,WAArB,EAAkCC,IAAlC,EAAwCnD,MAAxC;KADF;GALF;;;;;;;;;;;;;MAqBMqD,oBAAoB,SAApBA,iBAAoB,CAASH,WAAT,EAAsB;QAC1C3F,gBAAJ;;;iBAGa,wBAAb,EAAuC2F,WAAvC,EAAoD,IAApD;;;QAGIV,aAAaU,WAAb,CAAJ,EAA+B;mBAChBA,WAAb;aACO,IAAP;;;;QAIII,UAAUJ,YAAYR,QAAZ,CAAqB1I,WAArB,EAAhB;;;iBAGa,qBAAb,EAAoCkJ,WAApC,EAAiD;sBAAA;mBAElC7E;KAFf;;;QAMI,CAACA,aAAaiF,OAAb,CAAD,IAA0B3E,YAAY2E,OAAZ,CAA9B,EAAoD;;UAGhD7D,gBACA,CAACG,gBAAgB0D,OAAhB,CADD,IAEA,OAAOJ,YAAYK,kBAAnB,KAA0C,UAH5C,EAIE;YACI;cACIC,eAAeN,YAAYhB,SAAjC;sBACYqB,kBAAZ,CACE,UADF,EAEE9F,qBACIA,mBAAmBE,UAAnB,CAA8B6F,YAA9B,CADJ,GAEIA,YAJN;SAFF,CAQE,OAAO1H,KAAP,EAAc;;;mBAGLoH,WAAb;aACO,IAAP;;;;QAIEI,YAAY,UAAZ,IAA0BJ,YAAYhB,SAAZ,CAAsBV,KAAtB,CAA4B,cAA5B,CAA9B,EAA2E;mBAC5D0B,WAAb;aACO,IAAP;;;QAGEI,YAAY,SAAZ,IAAyBJ,YAAYhB,SAAZ,CAAsBV,KAAtB,CAA4B,aAA5B,CAA7B,EAAyE;mBAC1D0B,WAAb;aACO,IAAP;;;;QAKAlE,mBACA,CAACkE,YAAYtB,iBADb,KAEC,CAACsB,YAAY3F,OAAb,IAAwB,CAAC2F,YAAY3F,OAAZ,CAAoBqE,iBAF9C,KAGA,KAAK6B,IAAL,CAAUP,YAAYP,WAAtB,CAJF,EAKE;gBACUtG,OAAV,CAAkBsE,IAAlB,CAAuB,EAAE7G,SAASoJ,YAAYQ,SAAZ,EAAX,EAAvB;UACIR,YAAYhB,SAAhB,EAA2B;oBACbA,SAAZ,GAAwBgB,YAAYhB,SAAZ,CAAsByB,OAAtB,CAA8B,IAA9B,EAAoC,MAApC,CAAxB;OADF,MAEO;oBACOzB,SAAZ,GAAwBgB,YAAYP,WAAZ,CAAwBgB,OAAxB,CAAgC,IAAhC,EAAsC,MAAtC,CAAxB;;;;;QAKA1E,sBAAsBiE,YAAY5G,QAAZ,KAAyB,CAAnD,EAAsD;;gBAE1C4G,YAAYP,WAAtB;gBACUpF,QAAQoG,OAAR,CAAgBpJ,gBAAhB,EAA+B,GAA/B,CAAV;gBACUgD,QAAQoG,OAAR,CAAgBnJ,WAAhB,EAA0B,GAA1B,CAAV;UACI0I,YAAYP,WAAZ,KAA4BpF,OAAhC,EAAyC;kBAC7BlB,OAAV,CAAkBsE,IAAlB,CAAuB,EAAE7G,SAASoJ,YAAYQ,SAAZ,EAAX,EAAvB;oBACYf,WAAZ,GAA0BpF,OAA1B;;;;;iBAKS,uBAAb,EAAsC2F,WAAtC,EAAmD,IAAnD;;WAEO,KAAP;GArFF;;;;;;;;;;;MAiGMU,oBAAoB,SAApBA,iBAAoB,CAASC,KAAT,EAAgBC,MAAhB,EAAwBC,KAAxB,EAA+B;;QAGrDvE,iBACCsE,WAAW,IAAX,IAAmBA,WAAW,MAD/B,MAECC,SAASzI,QAAT,IAAqByI,SAAS9D,WAF/B,CADF,EAIE;aACO,KAAP;;;;;;;QAOEnB,mBAAmBrE,aAAUgJ,IAAV,CAAeK,MAAf,CAAvB,EAA+C;;KAA/C,MAEO,IAAIjF,mBAAmBnE,aAAU+I,IAAV,CAAeK,MAAf,CAAvB,EAA+C;;;KAA/C,MAGA,IAAI,CAACtF,aAAasF,MAAb,CAAD,IAAyBlF,YAAYkF,MAAZ,CAA7B,EAAkD;aAChD,KAAP;;;KADK,MAIA,IAAIhE,oBAAoBgE,MAApB,CAAJ,EAAiC;;;;KAAjC,MAIA,IAAInJ,kBAAe8I,IAAf,CAAoBM,MAAMJ,OAAN,CAAc9I,kBAAd,EAA+B,EAA/B,CAApB,CAAJ,EAA6D;;;;KAA7D,MAIA,IACL,CAACiJ,WAAW,KAAX,IAAoBA,WAAW,YAAhC,KACAD,UAAU,QADV,IAEAE,MAAMC,OAAN,CAAc,OAAd,MAA2B,CAF3B,IAGAnE,cAAcgE,KAAd,CAJK,EAKL;;;;;KALK,MAUA,IACL9E,2BACA,CAACnE,qBAAkB6I,IAAlB,CAAuBM,MAAMJ,OAAN,CAAc9I,kBAAd,EAA+B,EAA/B,CAAvB,CAFI,EAGL;;;;KAHK,MAOA,IAAI,CAACkJ,KAAL,EAAY;;;KAAZ,MAGA;aACE,KAAP;;;WAGK,IAAP;GAvDF;;;;;;;;;;;;MAoEME,sBAAsB,SAAtBA,mBAAsB,CAASf,WAAT,EAAsB;QAC5CgB,aAAJ;QACIH,cAAJ;QACID,eAAJ;QACIK,eAAJ;QACIvK,UAAJ;;iBAEa,0BAAb,EAAyCsJ,WAAzC,EAAsD,IAAtD;;QAEMN,UAT0C,GAS3BM,WAT2B,CAS1CN,UAT0C;;;;QAY5C,CAACA,UAAL,EAAiB;;;;QAIXwB,YAAY;gBACN,EADM;iBAEL,EAFK;gBAGN,IAHM;yBAIG5F;KAJrB;QAMIoE,WAAW/I,MAAf;;;WAGOD,GAAP,EAAY;aACHgJ,WAAWhJ,CAAX,CAAP;kBAC+BsK,IAFrB;UAEFlD,IAFE,SAEFA,IAFE;UAEIqD,YAFJ,SAEIA,YAFJ;;cAGFH,KAAKH,KAAL,CAAWO,IAAX,EAAR;eACStD,KAAKhH,WAAL,EAAT;;;gBAGUuK,QAAV,GAAqBT,MAArB;gBACUU,SAAV,GAAsBT,KAAtB;gBACUU,QAAV,GAAqB,IAArB;mBACa,uBAAb,EAAsCvB,WAAtC,EAAmDkB,SAAnD;cACQA,UAAUI,SAAlB;;;;;;UAOEV,WAAW,MAAX,IACAZ,YAAYR,QAAZ,KAAyB,KADzB,IAEAE,WAAW8B,EAHb,EAIE;iBACS9B,WAAW8B,EAApB;qBACarL,MAAMyB,UAAN,EAAkB8H,UAAlB,EAA8B,EAA9B,CAAb;yBACiB,IAAjB,EAAuBM,WAAvB;yBACiBlC,IAAjB,EAAuBkC,WAAvB;YACIN,WAAWoB,OAAX,CAAmBG,MAAnB,IAA6BvK,CAAjC,EAAoC;sBACtBiJ,YAAZ,CAAyB,IAAzB,EAA+BsB,OAAOJ,KAAtC;;OAVJ,MAYO;;;kBAGOrB,QAAZ,KAAyB,OAAzB,IACAoB,WAAW,MADX,IAEAC,UAAU,MAFV,IAGAK,UAAUK,QAHV,KAICjG,aAAasF,MAAb,KAAwB,CAAClF,YAAYkF,MAAZ,CAJ1B,CAHK,EAQL;;OARK,MAUA;;;;YAID9C,SAAS,IAAb,EAAmB;sBACL6B,YAAZ,CAAyB7B,IAAzB,EAA+B,EAA/B;;;yBAGeA,IAAjB,EAAuBkC,WAAvB;;;;UAIE,CAACkB,UAAUK,QAAf,EAAyB;;;;;UAKrBxF,kBAAJ,EAAwB;gBACd8E,MAAMJ,OAAN,CAAcpJ,gBAAd,EAA6B,GAA7B,CAAR;gBACQwJ,MAAMJ,OAAN,CAAcnJ,WAAd,EAAwB,GAAxB,CAAR;;;;UAIIqJ,QAAQX,YAAYR,QAAZ,CAAqB1I,WAArB,EAAd;UACI,CAAC4J,kBAAkBC,KAAlB,EAAyBC,MAAzB,EAAiCC,KAAjC,CAAL,EAA8C;;;;;UAK1C;YACEM,YAAJ,EAAkB;sBACJM,cAAZ,CAA2BN,YAA3B,EAAyCrD,IAAzC,EAA+C+C,KAA/C;SADF,MAEO;;sBAEOlB,YAAZ,CAAyB7B,IAAzB,EAA+B+C,KAA/B;;;kBAGQ1H,OAAV,CAAkBuI,GAAlB;OARF,CASE,OAAO9I,KAAP,EAAc;;;;iBAIL,yBAAb,EAAwCoH,WAAxC,EAAqD,IAArD;GA1GF;;;;;;;MAkHM2B,qBAAqB,SAArBA,kBAAqB,CAASC,QAAT,EAAmB;QACxCC,mBAAJ;QACMC,iBAAiB7C,gBAAgB2C,QAAhB,CAAvB;;;iBAGa,yBAAb,EAAwCA,QAAxC,EAAkD,IAAlD;;WAEQC,aAAaC,eAAeC,QAAf,EAArB,EAAiD;;mBAElC,wBAAb,EAAuCF,UAAvC,EAAmD,IAAnD;;;UAGI1B,kBAAkB0B,UAAlB,CAAJ,EAAmC;;;;;UAK/BA,WAAWxH,OAAX,YAA8BZ,gBAAlC,EAAoD;2BAC/BoI,WAAWxH,OAA9B;;;;0BAIkBwH,UAApB;;;;iBAIW,wBAAb,EAAuCD,QAAvC,EAAiD,IAAjD;GA1BF;;;;;;;;;;YAqCUI,QAAV,GAAqB,UAAS9D,KAAT,EAAgBjB,GAAhB,EAAqB;QACpCwB,aAAJ;QACIwD,qBAAJ;QACIjC,oBAAJ;QACIkC,gBAAJ;QACIC,mBAAJ;;;;QAII,CAACjE,KAAL,EAAY;cACF,OAAR;;;;QAIE,OAAOA,KAAP,KAAiB,QAAjB,IAA6B,CAAC0B,QAAQ1B,KAAR,CAAlC,EAAkD;;UAE5C,OAAOA,MAAMkE,QAAb,KAA0B,UAA9B,EAA0C;cAClC,IAAIC,SAAJ,CAAc,4BAAd,CAAN;OADF,MAEO;gBACGnE,MAAMkE,QAAN,EAAR;YACI,OAAOlE,KAAP,KAAiB,QAArB,EAA+B;gBACvB,IAAImE,SAAJ,CAAc,iCAAd,CAAN;;;;;;QAMF,CAACtJ,UAAUM,WAAf,EAA4B;UAExB,QAAOpB,OAAOqK,YAAd,MAA+B,QAA/B,IACA,OAAOrK,OAAOqK,YAAd,KAA+B,UAFjC,EAGE;YACI,OAAOpE,KAAP,KAAiB,QAArB,EAA+B;iBACtBjG,OAAOqK,YAAP,CAAoBpE,KAApB,CAAP;;;YAGE0B,QAAQ1B,KAAR,CAAJ,EAAoB;iBACXjG,OAAOqK,YAAP,CAAoBpE,MAAMN,SAA1B,CAAP;;;;aAIGM,KAAP;;;;QAIE,CAACjC,UAAL,EAAiB;mBACFgB,GAAb;;;;cAIQ9D,OAAV,GAAoB,EAApB;;QAEIqD,QAAJ,EAAc;;KAAd,MAEO,IAAI0B,iBAAiBvE,IAArB,EAA2B;;;aAGzBsE,cAAc,OAAd,CAAP;qBACeQ,KAAKnE,aAAL,CAAmBQ,UAAnB,CAA8BoD,KAA9B,EAAqC,IAArC,CAAf;UACI+D,aAAa7I,QAAb,KAA0B,CAA1B,IAA+B6I,aAAazC,QAAb,KAA0B,MAA7D,EAAqE;;eAE5DyC,YAAP;OAFF,MAGO,IAAIA,aAAazC,QAAb,KAA0B,MAA9B,EAAsC;eACpCyC,YAAP;OADK,MAEA;;aAEAM,WAAL,CAAiBN,YAAjB;;KAZG,MAcA;;UAGH,CAAC9F,UAAD,IACA,CAACJ,kBADD,IAEA,CAACC,cAFD,IAGAkC,MAAM4C,OAAN,CAAc,GAAd,MAAuB,CAAC,CAJ1B,EAKE;eACOvG,qBACHA,mBAAmBE,UAAnB,CAA8ByD,KAA9B,CADG,GAEHA,KAFJ;;;;aAMKD,cAAcC,KAAd,CAAP;;;UAGI,CAACO,IAAL,EAAW;eACFtC,aAAa,IAAb,GAAoB3B,SAA3B;;;;;QAKAiE,QAAQvC,UAAZ,EAAwB;mBACTuC,KAAK+D,UAAlB;;;;QAIIC,eAAexD,gBAAgBzC,WAAW0B,KAAX,GAAmBO,IAAnC,CAArB;;;WAGQuB,cAAcyC,aAAaV,QAAb,EAAtB,EAAgD;;UAE1C/B,YAAY5G,QAAZ,KAAyB,CAAzB,IAA8B4G,gBAAgBkC,OAAlD,EAA2D;;;;;UAKvD/B,kBAAkBH,WAAlB,CAAJ,EAAoC;;;;;UAKhCA,YAAY3F,OAAZ,YAA+BZ,gBAAnC,EAAqD;2BAChCuG,YAAY3F,OAA/B;;;;0BAIkB2F,WAApB;;gBAEUA,WAAV;;;cAGQ,IAAV;;;QAGIxD,QAAJ,EAAc;aACL0B,KAAP;;;;QAIE/B,UAAJ,EAAgB;UACVC,mBAAJ,EAAyB;qBACVvB,uBAAuBiE,IAAvB,CAA4BL,KAAKnE,aAAjC,CAAb;;eAEOmE,KAAK+D,UAAZ,EAAwB;;qBAEXD,WAAX,CAAuB9D,KAAK+D,UAA5B;;OALJ,MAOO;qBACQ/D,IAAb;;;UAGEpC,iBAAJ,EAAuB;;;;;;qBAMRvB,WAAWgE,IAAX,CAAgBxF,gBAAhB,EAAkC6I,UAAlC,EAA8C,IAA9C,CAAb;;;aAGKA,UAAP;;;QAGEO,iBAAiB1G,iBAAiByC,KAAKb,SAAtB,GAAkCa,KAAKO,SAA5D;;;QAGIjD,kBAAJ,EAAwB;uBACL2G,eAAejC,OAAf,CAAuBpJ,gBAAvB,EAAsC,GAAtC,CAAjB;uBACiBqL,eAAejC,OAAf,CAAuBnJ,WAAvB,EAAiC,GAAjC,CAAjB;;;WAGKiD,qBACHA,mBAAmBE,UAAnB,CAA8BiI,cAA9B,CADG,GAEHA,cAFJ;GAjKF;;;;;;;;YA4KUC,SAAV,GAAsB,UAAS1F,GAAT,EAAc;iBACrBA,GAAb;iBACa,IAAb;GAFF;;;;;;;YAUU2F,WAAV,GAAwB,YAAW;aACxB,IAAT;iBACa,KAAb;GAFF;;;;;;;;;;;;YAeUC,gBAAV,GAA6B,UAASC,GAAT,EAAc9B,IAAd,EAAoBH,KAApB,EAA2B;;QAElD,CAAC/D,MAAL,EAAa;mBACE,EAAb;;;QAGI6D,QAAQmC,IAAIhM,WAAJ,EAAd;QACM8J,SAASI,KAAKlK,WAAL,EAAf;WACO4J,kBAAkBC,KAAlB,EAAyBC,MAAzB,EAAiCC,KAAjC,CAAP;GARF;;;;;;;;;YAkBUkC,OAAV,GAAoB,UAAShD,UAAT,EAAqBiD,YAArB,EAAmC;QACjD,OAAOA,YAAP,KAAwB,UAA5B,EAAwC;;;;UAIlCjD,UAAN,IAAoBhF,MAAMgF,UAAN,KAAqB,EAAzC;UACMA,UAAN,EAAkBtC,IAAlB,CAAuBuF,YAAvB;GANF;;;;;;;;;YAgBUC,UAAV,GAAuB,UAASlD,UAAT,EAAqB;QACtChF,MAAMgF,UAAN,CAAJ,EAAuB;YACfA,UAAN,EAAkB2B,GAAlB;;GAFJ;;;;;;;;YAYUwB,WAAV,GAAwB,UAASnD,UAAT,EAAqB;QACvChF,MAAMgF,UAAN,CAAJ,EAAuB;YACfA,UAAN,IAAoB,EAApB;;GAFJ;;;;;;;YAWUoD,cAAV,GAA2B,YAAW;YAC5B,EAAR;GADF;;SAIOpK,SAAP;;;AAGF,aAAeD,iBAAf;;;;;;;;"}