<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Matching Process - Customer Documentation</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header h1 {
            margin: 0;
            font-size: 3em;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .header p {
            margin: 15px 0 0 0;
            font-size: 1.3em;
            color: #666;
        }
        .document-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }
        .doc-card {
            background: rgba(255,255,255,0.95);
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
            cursor: pointer;
            border-left: 6px solid #667eea;
        }
        .doc-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.25);
            border-left-color: #764ba2;
        }
        .doc-card.executive {
            border-left-color: #ff6b6b;
        }
        .doc-card.executive:hover {
            border-left-color: #ee5a52;
        }
        .doc-card.technical {
            border-left-color: #4ecdc4;
        }
        .doc-card.technical:hover {
            border-left-color: #45b7aa;
        }
        .doc-card.user {
            border-left-color: #45b7d1;
        }
        .doc-card.user:hover {
            border-left-color: #3a9bc1;
        }
        .doc-card.visual {
            border-left-color: #96ceb4;
        }
        .doc-card.visual:hover {
            border-left-color: #85c1a3;
        }
        .doc-icon {
            font-size: 3em;
            margin-bottom: 15px;
            display: block;
        }
        .doc-card h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 1.5em;
        }
        .doc-card p {
            margin: 0 0 20px 0;
            color: #666;
            line-height: 1.6;
        }
        .doc-features {
            list-style: none;
            padding: 0;
            margin: 20px 0;
        }
        .doc-features li {
            padding: 8px 0;
            color: #555;
            position: relative;
            padding-left: 25px;
        }
        .doc-features li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #4caf50;
            font-weight: bold;
            font-size: 1.2em;
        }
        .btn {
            display: inline-block;
            padding: 12px 25px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }
        .btn.secondary {
            background: linear-gradient(135deg, #95a5a6, #7f8c8d);
        }
        .btn.secondary:hover {
            box-shadow: 0 6px 20px rgba(149, 165, 166, 0.4);
        }
        .overview {
            background: rgba(255,255,255,0.95);
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .overview h2 {
            margin: 0 0 20px 0;
            color: #333;
            font-size: 1.8em;
        }
        .overview p {
            margin: 0 0 15px 0;
            color: #666;
            line-height: 1.6;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .stat {
            text-align: center;
            padding: 20px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 8px;
        }
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #667eea;
            display: block;
        }
        .stat-label {
            color: #666;
            font-size: 0.9em;
            margin-top: 5px;
        }
        .footer {
            background: rgba(255,255,255,0.95);
            border-radius: 12px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .footer h3 {
            margin: 0 0 15px 0;
            color: #333;
        }
        .footer p {
            margin: 0;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 Matching Process</h1>
            <p>Comprehensive Customer Documentation Suite</p>
        </div>
        
        <div class="overview">
            <h2>📋 Documentation Overview</h2>
            <p>This comprehensive documentation suite provides everything you need to understand, implement, and operate the Matching Process system. Whether you're an executive seeking strategic insights, a technical professional implementing the system, or an end-user working with daily operations, you'll find the appropriate resources here.</p>
            
            <div class="stats">
                <div class="stat">
                    <span class="stat-number">89.6%</span>
                    <div class="stat-label">Average Match Rate</div>
                </div>
                <div class="stat">
                    <span class="stat-number">50K+</span>
                    <div class="stat-label">Daily Processing Capacity</div>
                </div>
                <div class="stat">
                    <span class="stat-number">80%</span>
                    <div class="stat-label">Manual Effort Reduction</div>
                </div>
                <div class="stat">
                    <span class="stat-number">99.9%</span>
                    <div class="stat-label">System Availability</div>
                </div>
            </div>
        </div>
        
        <div class="document-grid">
            <div class="doc-card executive" onclick="openDocument('Executive_Summary_Matching_Process.md')">
                <span class="doc-icon">📊</span>
                <h3>Executive Summary</h3>
                <p>Strategic overview for business leaders and decision makers, including ROI analysis, business impact, and strategic roadmap.</p>
                <ul class="doc-features">
                    <li>Business value proposition</li>
                    <li>Financial impact analysis</li>
                    <li>Strategic roadmap</li>
                    <li>Key performance indicators</li>
                    <li>Investment & ROI metrics</li>
                </ul>
                <button class="btn" onclick="event.stopPropagation(); openDocument('Executive_Summary_Matching_Process.md')">View Executive Summary</button>
            </div>
            
            <div class="doc-card technical" onclick="openDocument('Matching_Process_Functional_Specification.md')">
                <span class="doc-icon">⚙️</span>
                <h3>Functional Specification</h3>
                <p>Comprehensive technical documentation covering system architecture, configuration, and detailed process flows.</p>
                <ul class="doc-features">
                    <li>Complete system architecture</li>
                    <li>Detailed process workflows</li>
                    <li>Configuration parameters</li>
                    <li>Quality assessment logic</li>
                    <li>Troubleshooting guides</li>
                </ul>
                <button class="btn" onclick="event.stopPropagation(); openDocument('Matching_Process_Functional_Specification.md')">View Technical Specs</button>
            </div>
            
            <div class="doc-card user" onclick="openDocument('Matching_Process_User_Guide.md')">
                <span class="doc-icon">👥</span>
                <h3>User Guide</h3>
                <p>Practical guide for daily users, including step-by-step procedures, best practices, and troubleshooting tips.</p>
                <ul class="doc-features">
                    <li>Daily workflow procedures</li>
                    <li>Match review guidelines</li>
                    <li>Exception handling steps</li>
                    <li>Quality assurance tips</li>
                    <li>Support contact information</li>
                </ul>
                <button class="btn" onclick="event.stopPropagation(); openDocument('Matching_Process_User_Guide.md')">View User Guide</button>
            </div>
            
            <div class="doc-card visual" onclick="openDocument('customer_friendly_flowchart.html')">
                <span class="doc-icon">🎨</span>
                <h3>Interactive Flowchart</h3>
                <p>Visual representation of the matching process with interactive zoom controls and detailed explanations.</p>
                <ul class="doc-features">
                    <li>Interactive process visualization</li>
                    <li>Zoom and navigation controls</li>
                    <li>Color-coded decision points</li>
                    <li>Detailed process legends</li>
                    <li>Customer-friendly design</li>
                </ul>
                <button class="btn" onclick="event.stopPropagation(); openDocument('customer_friendly_flowchart.html')">View Interactive Chart</button>
            </div>
        </div>
        
        <div class="overview">
            <h2>🎯 Quick Navigation</h2>
            <p>Choose your role to get started with the most relevant documentation:</p>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-top: 25px;">
                <div style="text-align: center; padding: 20px; background: rgba(255,107,107,0.1); border-radius: 8px;">
                    <h4 style="margin: 0 0 10px 0; color: #ff6b6b;">👔 Executive</h4>
                    <p style="margin: 0 0 15px 0; font-size: 0.9em;">Strategic insights and business impact</p>
                    <button class="btn" onclick="openDocument('Executive_Summary_Matching_Process.md')">Start Here</button>
                </div>
                
                <div style="text-align: center; padding: 20px; background: rgba(78,205,196,0.1); border-radius: 8px;">
                    <h4 style="margin: 0 0 10px 0; color: #4ecdc4;">🔧 Technical</h4>
                    <p style="margin: 0 0 15px 0; font-size: 0.9em;">Implementation and configuration</p>
                    <button class="btn" onclick="openDocument('Matching_Process_Functional_Specification.md')">Start Here</button>
                </div>
                
                <div style="text-align: center; padding: 20px; background: rgba(69,183,209,0.1); border-radius: 8px;">
                    <h4 style="margin: 0 0 10px 0; color: #45b7d1;">👤 End User</h4>
                    <p style="margin: 0 0 15px 0; font-size: 0.9em;">Daily operations and procedures</p>
                    <button class="btn" onclick="openDocument('Matching_Process_User_Guide.md')">Start Here</button>
                </div>
                
                <div style="text-align: center; padding: 20px; background: rgba(150,206,180,0.1); border-radius: 8px;">
                    <h4 style="margin: 0 0 10px 0; color: #96ceb4;">👁️ Visual Learner</h4>
                    <p style="margin: 0 0 15px 0; font-size: 0.9em;">Interactive process visualization</p>
                    <button class="btn" onclick="openDocument('customer_friendly_flowchart.html')">Start Here</button>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <h3>📞 Need Help?</h3>
            <p>For additional support, training, or questions about this documentation, please contact your support team or system administrator.</p>
            <div style="margin-top: 20px;">
                <button class="btn secondary" onclick="alert('Contact your local support team for assistance.')">Contact Support</button>
                <button class="btn secondary" onclick="alert('Training materials and sessions available through your learning management system.')">Request Training</button>
            </div>
        </div>
    </div>

    <script>
        function openDocument(filename) {
            if (filename.endsWith('.html')) {
                window.open(filename, '_blank');
            } else {
                // For markdown files, we'll open them in a new tab
                // In a real implementation, you might want to render them or provide download links
                window.open(filename, '_blank');
            }
        }
        
        // Add click handlers for better UX
        document.querySelectorAll('.doc-card').forEach(card => {
            card.addEventListener('click', function(e) {
                if (e.target.tagName !== 'BUTTON') {
                    const button = this.querySelector('button');
                    if (button) {
                        button.click();
                    }
                }
            });
        });
    </script>
</body>
</html>
