{"_from": "crypto-js@^3.1.9-1", "_id": "crypto-js@3.3.0", "_inBundle": false, "_integrity": "sha512-DIT51nX0dCfKltpRiXV+/TVZq+Qq2NgF4644+K7Ttnla7zEzqc+kjJyiB96BHNyUTBxyjzRcZYpUdZa+QAqi6Q==", "_location": "/swt-tool-box/crypto-js", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "crypto-js@^3.1.9-1", "name": "crypto-js", "escapedName": "crypto-js", "rawSpec": "^3.1.9-1", "saveSpec": null, "fetchSpec": "^3.1.9-1"}, "_requiredBy": ["/swt-tool-box"], "_resolved": "https://registry.npmjs.org/crypto-js/-/crypto-js-3.3.0.tgz", "_shasum": "846dd1cce2f68aacfa156c8578f926a609b7976b", "_spec": "crypto-js@^3.1.9-1", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace\\bin", "author": {"name": "<PERSON>", "url": "http://github.com/evanvosberg"}, "bugs": {"url": "https://github.com/brix/crypto-js/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "JavaScript library of crypto standards.", "homepage": "http://github.com/brix/crypto-js", "keywords": ["security", "crypto", "Hash", "MD5", "SHA1", "SHA-1", "SHA256", "SHA-256", "RC4", "Rabbit", "AES", "DES", "PBKDF2", "HMAC", "OFB", "CFB", "CTR", "CBC", "Base64"], "license": "MIT", "main": "index.js", "name": "crypto-js", "repository": {"type": "git", "url": "git+ssh://**************/brix/crypto-js.git"}, "version": "3.3.0"}