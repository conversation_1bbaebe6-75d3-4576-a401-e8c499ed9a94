CREATE OR REPLACE PACKAGE PK_MATCHING_PROCESS
AS
   /*
   Modification History
   ****************************************************************************************************
   Release  Who          Date     Description
   -------- ------------ -------- ---------------------------------------------------------------------
   1069.3   Rchatbouri   27/04/23 Mantis 6573: Matching process: Improve performance of a query
   1068.1   Andrew       03/06/22 Mantis 6067: Matching process: Matching process: Improve matching performance related to P_REFERENCE_XREF table.
   1068.1   Rchatbouri   03/06/22 Mantis 6018: Matching process: Add a parameter to control whether sources revert to initial predict status
   1068.1   Rchatbouri   03/06/22 Mantis 6055: Matching process: Anchor variable types to relevant table columns
   1067     DBlyth       26/11/21 Mantis 5750: Error due to CONNECT BY LOOP in account linkage
   1065     Rchatbouri   02/08/21 Mantis 5546: Predict: - Matching Process is failing on smart production environment fix - Auto Matching
   1062     Rchatbouri   07/05/19 Mantis 4615: Matching enhancement request: Levels 3, 4 and 5 to join "One to Many" existing match
   1057     STL-LDN      23/04/15 Mantis 2638: Improve performance after 11.2.0.4 DB upgrade
            STL-LDN      23/04/15 Code clean up (99% formatting changes, only 1% code tidy up)
   1054     Rchatbouri   25/04/13 Mantis 2232: Amend method for flagging accounts for balance processing and currencies
                                   spUpdateMatchDriver replaced by pk_application.SET_CCY_NEW_MOVE_FLAG
   1055     Raj V.R.     18/10/12 Mantis 2082: Match not expected after unlink accounts
   1054     Chandrasekar 14/06/12 Mantis 1966: Matching does not stop
                                    Added autonomous spUpdateMatchDriver and spUpdateMatchEnd to avoid row blocking
            Raj V.R.     15/02/12 Mantis 1642: Unable to unmatch match 'This match has been changed'
            Swaminathan  12/01/12 Mantis 1119: PK_MONITORS package enhancements
            Mayuranathan 09/12/11 Mantis 1489: INTERNAL - MORE UNREFERENCED VARIABLES - PACKAGES
   1053     Raj V.R.     15/07/11 Mantis 1467: Use ALL cross references defined against movements
            Raj V.R.     27/05/11 Mantis 1470: Swift received first and left outstanding
   1052     Raj V.R.     07/04/11 Mantis 446: Cross referencing performance improvement
            Swaminathan  08/03/11 Mantis 1370: Enable sweeping based on external balance
                                    Currently propagates EXT_BAL_STATUS to the highest position level
                                    movement in a confirmed match, which is not really necessary
            Raj V.R.     18/02/11 Mantis 446: Cross reference table enhancement
   1051     Raj V.R.     22/01/11 Mantis 1340: Critical Issues in 1051 Beta (059_STL and 064_STL)
            Raj V.R.     15/12/10 Mantis 1193: Ignore suspended matches
            Raj V.R.     13/12/10 Mantis 1108: 1st event is Swift message
            Raj V.R.     09/12/10 Mantis 1188: OPEN movements matching
                                    Bug fixed for single match id that contains two match statuses
            Shan A       05/05/10 Mantis 1188: OPEN movements matching
            Shan A       05/05/10 Mantis 795: Thorough commenting and error logging
            Raj          24/11/10 Mantis 1296: Make P_MISC_PARAMS multi-entity
            Thulasi      15/11/10 Mantis 1284: Prevent date localisation issues
            Thulasi      10/11/10 Mantis 1249: Extend account id
            Raj V.R.     30/11/10 Mantis 1206: Run matching in last ran order
   1051.3   Raj V.R.     16/09/10 Mantis 1243: Propagation of Book Code
   1050     Raj V.R.     05/07/10 Mantis 1191: Double Counting Error
            Raj V.R.     22/06/10 Mantis 1183: Issues found while testing version 1050
                                    1. Removed wrong date format
                                    2. Added Matching_party column into group by clause
                                    3. Introduced new function fn_ref_chk_others
            Shan A       03/05/10 Mantis 1171: More flexible way to match EUR up to 7 days ahead
                                    like other currencies (required by NIB)
            Shan A       31/03/10 Mantis 1145: Remove hardcoding of highest internal position level of 6
   1049     Shan A       03/03/10 Mantis 1034: New Movement attributes for NIB
   1050     Shan A       24/10/09 Mantis 1026: Ensure data type consistency
   1049     Shan A       30/10/09 Mantis 1042: Random Order Processing Issue
            Shan A       07/10/09 Mantis 1042: Random Order Processing Issue
                                    Modified the core functionality for external target selection
            Shan A       29/09/09 For ING Matching Issue
                                    1. Sweep movement not matching fix
            Shan A       30/06/09 For ING Matching Issue
                                    1. Confirm D quality fix
                                    2. Pre-advice fix
                                    3. Match with out movement fix
            Shan A       07/07/09 Mantis 974: In PL/SQL, all cursor should be explicitly closed
            Shan A       28/05/09 Mantis 987: Does not matching all outstanding
            Shan A       22/04/09 Mantis 961: Match issues while testing at STL
                                    1. modified the sp_matching_process for target and indirect
                                       target processing.
                                    2. New function fn_check_inner_tgt_reference is added to validate
                                       the references between target and indirect target.
                                    3. moved the cr_references (reference validation block) outside the
                                       match_satus != 'L' condition in sp_preadvice_matching procedure.
                                    4. Added pn_src_position_in > gn_max_internal_pos condition in
                                       sp_update_match_actions to avoid the Confirm 'D' status when
                                       source position is internal position.
            Shan A       24/03/09 Mantis 937: Matching error
                                    Fix 892 resolves the error reported in this mantis.
            Shan A       24/03/09 Mantis 892: Pre-advice enhancements
                                    1. SP_MATCHING PROCEDURE to allow pre-advice position to act as source
                                    2. New target cursor added, when source position is pre-advice position.
                                    3. Business logic in target and indirect target processing are changed
                                       to handle the pre-advice position.
                                    4. Indirect target quality selection changed to fn_update_p_b_matqual_poslevel
                                    5. Input date condition removed from source cursor processing as
                                       it will be implemented when back valued movements implementation
                                    6. pk_application.fn_get_parameter_value to get matching p_misc_params data.

   1048.1   Shan A       26/02/09 Mantis 923: Workflow Monitor does not show count for Debit matches

   1048     Shan A       04/02/09 Mantis 900: Problem in setting P_MATCH_DRIVER.NEW_MOVE_FLAG = 'Y'
            Shan A       03/02/09 Mantis 895: Issues found during analysis of matched and unmatched movements
                                    1. Added a validation to to check the source amount = target amount
                                       when source position is lesser then target position.
                                    2. Added functionality the drive the position level 6 to second time.
                                       the above modifications are performed in the SP_MATCHING_PROCEDURE.
            Shan A       26/01/09 Mantis 888: Performance is slow in test version (15th Jan) on Rabo Pre-Prod
            Shan A       15/01/09 Mantis 876: Process indirect targets only when direct targets are decided
                                    Added inner target cursor logic validation to check with source amount
            Shan A       06/01/09 Mantis 869: Matching stop with error
                                    Added debugging functionality
                                    Added delete statement to remove lower quality higher position before inner matching.

   1047.2   Shan A       06/01/09 Mantis 457: Change methodology for writing p_prev_match
                                    1. Removed logic when unmatched movements in outstanding state
                                       selected for Auto matching.
            Shan A       19/12/08 Mantis 862: Scenario PL2 failure
                                    1. Modified Logic to quit source when external movement is source
                                       and when it finds another external movement [account or reference different]
                                       in the inner target cursor. i.e. Instead of quitting current source movement,
                                       allow the source to process next target movements and removing all the movements
                                       of the match_id identified in the last target movement.

   1047     Shan A       09/12/08 Mantis 791: RABO matching issue: incorrect match made
                                    1. Modified SP_MATCHING_PROCEDURE to break a offered match when
                                        internal references are same while driving from position 6.
                                    2. Also avoid breaking a good confirmed Internal match by bad external movement
                                        when amount of external movement is not same as position 6.
                                        But equal to one of the netted movements in lower position (e.g. 3)
            Shan A       05/12/08 Mantis 834: RABO issue - Allow matched pre-advices to be treated as targets
                                    1. Allow pre-advice position to search offer and confirmed movements
            Shan A       01/12/08 Mantis 792: Addd tolerance check to sp_matching_amount_total
                                  Mantis 793: Removed HST condition from c_ins_source cursor in sp_matching_procedure
                                  Mantis 802: Code Clean up (Restructured entire matching process)

   ****************************************************************************************************
   */

   GV_GLOBAL_VAR_SYS_DATE   DATE;
   GN_WARNING_MSG           NUMBER;
   GV_REVERT_PRED_STATUS    VARCHAR2(1);

   TYPE MATCHACTION IS TABLE OF P_MATCH_ACTION.MATCH_ACTION_A%TYPE
      INDEX BY VARCHAR2(20);

   TYPE POSITIONMATCHACTION IS TABLE OF MATCHACTION
      INDEX BY PLS_INTEGER;

   TYPE SEQUENCECONDITION IS TABLE OF BOOLEAN
      INDEX BY PLS_INTEGER;

   TYPE QUALITYVARIABLE IS VARRAY(11) OF VARCHAR2(1);

   TYPE QUALITYMATCH_RECORD IS RECORD
   (
      MASK_VALUE       NUMBER,
      COMPUTED_VALUE   NUMBER
   );

   TYPE V_TO_MATCH_STAGE IS VARRAY(10) OF NUMBER(7, 5);

   PROCEDURE SP_UPDATE_LAST_STARTED(P_HOSTID_I         IN     VARCHAR2,
                                    P_ENTITYID_I       IN     VARCHAR2,
                                    P_CURRENCYCODE_I   IN     VARCHAR2,
                                    P_ROWUPDATE_O         OUT NUMBER);

   PROCEDURE SP_POPULATE_ACC_LINK_ACC_ID(L_HOSTID IN VARCHAR2, L_ENTITYID IN VARCHAR2, L_CURRENCYCODE IN VARCHAR2);

   TYPE QM_MATRIX1 IS TABLE OF QUALITYMATCH_RECORD
      INDEX BY PLS_INTEGER;

   TYPE QM_MATRIX IS TABLE OF QM_MATRIX1
      INDEX BY PLS_INTEGER;

   TYPE R_AMOUNTTOTAL IS RECORD(VALUE CHAR(1));

   TYPE T_AMTTOTAL1 IS TABLE OF R_AMOUNTTOTAL
      INDEX BY VARCHAR2(1);

   TYPE T_AMTTOTAL IS TABLE OF T_AMTTOTAL1
      INDEX BY PLS_INTEGER;

   TYPE LINKED_RECORD IS RECORD(EXIST CHAR(1));

   TYPE LA_MATRIX1 IS TABLE OF LINKED_RECORD
      INDEX BY P_ACCOUNT.LINK_ACCOUNT_ID%TYPE;

   TYPE LA_MATRIX IS TABLE OF LA_MATRIX1
      INDEX BY P_ACCOUNT.ACCOUNT_ID%TYPE;

   TYPE TDELSUPPORTREC IS RECORD
   (
      HOSTID         P_MOVEMENT.HOST_ID%TYPE,
      ENTITYID       P_MOVEMENT.ENTITY_ID%TYPE,
      MOVEMENTID     P_MOVEMENT.MOVEMENT_ID%TYPE,
      CURRENCYCODE   P_MOVEMENT.CURRENCY_CODE%TYPE
   );

   TYPE TDELSUPPORTTAB IS TABLE OF TDELSUPPORTREC
      INDEX BY PLS_INTEGER;

   VDELSUPPORTTAB           TDELSUPPORTTAB;

   FUNCTION FN_UPDATE_P_B_MATQUAL_POSLEVEL(P_SRC_HOST_ID             IN VARCHAR2,
                                           P_SRC_ENTITY_ID           IN VARCHAR2,
                                           P_SRC_CURRENCY_CODE       IN VARCHAR2,
                                           P_SRC_VALUE_DATE          IN DATE,
                                           P_TGT_VALUE_DATE          IN DATE,
                                           P_SRC_AMOUNT              IN NUMBER,
                                           P_TGT_AMOUNT              IN NUMBER,
                                           P_TOLERANCE               IN NUMBER,
                                           P_SRC_ACCOUNT_ID          IN VARCHAR2,
                                           P_TGT_ACCOUNT_ID          IN VARCHAR2,
                                           P_SRC_COUNTERPARTY_ID     IN VARCHAR2,
                                           P_TGT_COUNTERPARTY_ID     IN VARCHAR2,
                                           P_SRC_BENEFICIARY_ID      IN VARCHAR2,
                                           P_TGT_BENEFICIARY_ID      IN VARCHAR2,
                                           P_SRC_CUSTODIAN_ID        IN VARCHAR2,
                                           P_TGT_CUSTODIAN_ID        IN VARCHAR2,
                                           P_SRC_BOOKCODE            IN VARCHAR2,
                                           P_TGT_BOOKCODE            IN VARCHAR2,
                                           P_HIGHESTPOSLEVEL_LEVEL   IN NUMBER,
                                           P_SRC_MOVEMENT_ID         IN NUMBER,
                                           P_TGT_MOVEMENT_ID         IN NUMBER,
                                           P_TGT_OPEN                IN VARCHAR2,
                                           P_SRC_MATCHINGPARTY       IN VARCHAR2,
                                           P_TGT_MATCHINGPARTY       IN VARCHAR2,
                                           P_TGT_POS_LEVEL           IN NUMBER,
                                           P_POS_THRESHOLD           IN NUMBER)
      RETURN NUMBER;

   FUNCTION FN_GET_LINTQTYMATCH(P_POSITION_LEVEL    IN NUMBER,
                                P_HOST_ID           IN VARCHAR2,
                                P_ENTITY_ID         IN VARCHAR2,
                                P_MATCH_ID          IN NUMBER,
                                P_CURRENCY_CODE     IN VARCHAR2,
                                P_SRC_MOVEMENT_ID   IN NUMBER,
                                P_TGT_MOVEMENT_ID   IN NUMBER)
      RETURN VARCHAR2;

   PROCEDURE MATCHING_PROC_DRIVER_PARALLEL(L_HOSTID             VARCHAR2,
                                           L_ENTITYID           VARCHAR2,
                                           L_CURRENCYCODE       VARCHAR2,
                                           L_VALUEDATE          DATE,
                                           L_RESULT         OUT VARCHAR2);

   FUNCTION FN_MATCHING_AMOUNT_TOTAL_FLAG(P_QUALITY_VAR IN VARCHAR2, P_SOURCE_POSITION_LEVEL IN NUMBER)
      RETURN VARCHAR2;

   PROCEDURE SP_MATCHING_AMOUNT_TOTAL(P_SOURCE_POSITION_LEVEL IN NUMBER, PN_TOLERANCE_IN IN NUMBER);

   PROCEDURE SP_MATCHING_PROCEDURE(L_HOSTID                         VARCHAR2,
                                   L_ENTITYID                       VARCHAR2,
                                   L_CURRENCYCODE                   VARCHAR,
                                   L_CASH_FILTER_THRSH              NUMBER,
                                   L_SECURITY_FILTER_THRSH          NUMBER,
                                   L_TOLERANCE                      NUMBER,
                                   L_EXCHANGERATE                   NUMBER,
                                   L_EXCHANGERATE_FORMAT            NUMBER,
                                   L_VALUEDATE                      DATE,
                                   L_RESULT                     OUT VARCHAR2,
                                   PV_SUPPLEMENT_FLAG_IN     IN     VARCHAR2 DEFAULT 'N');

   FUNCTION FN_RETURN_LINKED_TO_ACCOUNT_ID(PV_TGT_ACCOUNT_ID_IN IN VARCHAR2, PV_SRC_ACCOUNT_ID_IN IN VARCHAR2)
      RETURN VARCHAR2;

   FUNCTION FN_IS_SRC_LINK_TO_TGT_REC(P_HOST_ID          IN P_ACCOUNT.HOST_ID%TYPE,
                                      P_SRC_ACCOUNT_ID   IN P_ACCOUNT.ACCOUNT_ID%TYPE,
                                      P_TGT_ACCOUNT_ID   IN P_ACCOUNT.LINK_ACCOUNT_ID%TYPE)
      RETURN VARCHAR2;

   FUNCTION FN_GET_VALID_REFERENCE(PV_REFERENCE_IN IN VARCHAR2)
      RETURN VARCHAR2;

   PROCEDURE SP_SET_AMOUNT_TOTAL_FLAG(P_HOST_ID IN VARCHAR2, P_ENTITY_ID IN VARCHAR2, P_CURRENCY_CODE VARCHAR2);

   FUNCTION FN_REF_CHK_OTHERS(P_HOSTID               P_MOVEMENT.HOST_ID%TYPE,
                              P_ENTITYID             P_MOVEMENT.ENTITY_ID%TYPE,
                              P_CURRENCYCODE         P_MOVEMENT.CURRENCY_CODE%TYPE,
                              P_SOURCE_VALUE_DATE    P_MOVEMENT.VALUE_DATE%TYPE,
                              P_SOURCE_SIGN          P_MOVEMENT.SIGN%TYPE,
                              P_SOURCE_AMOUNT        P_MOVEMENT.AMOUNT%TYPE,
                              P_TOLERANCE            NUMBER)
      RETURN VARCHAR2;

   FUNCTION FN_PROCESS_REFERENCE(INPUT_STRING             VARCHAR2,
                                 REMOVE_NON_ALPHANUM      VARCHAR2 DEFAULT 'Y',
                                 REMOVE_SPACES            VARCHAR2 DEFAULT 'Y',
                                 REMOVE_LEADING_ZEROES    VARCHAR2 DEFAULT 'Y')
      RETURN VARCHAR2;

   FUNCTION FNISCROSSREFEXISTS(PHOSTID              P_MOVEMENT.HOST_ID%TYPE,
                               PENTITYID            P_MOVEMENT.ENTITY_ID%TYPE,
                               PCURRENCYCODE        P_MOVEMENT.CURRENCY_CODE%TYPE,
                               PSOURCEMOVEMENTID    P_MOVEMENT.MOVEMENT_ID%TYPE,
                               PTARGETMOVEMENTID    P_MOVEMENT.MOVEMENT_ID%TYPE)
      RETURN VARCHAR2;

   PROCEDURE SPREMOVESUPPORTTABLESREC(PDELSUPPORTTAB IN TDELSUPPORTTAB);
END PK_MATCHING_PROCESS;
/

CREATE OR REPLACE PACKAGE BODY PK_MATCHING_PROCESS
AS
   /*
   Modification History
   ****************************************************************************************************
   Release  Who          Date     Description
   -------- ------------ -------- ---------------------------------------------------------------------
   1069.3   Rchatbouri   27/04/23 Mantis 6573: Matching process: Improve performance of a query
   1068.1   Andrew       03/06/22 Mantis 6067: Matching process: Matching process: Improve matching performance related to P_REFERENCE_XREF table.
   1068.1   Rchatbouri   03/06/22 Mantis 6018: Matching process: Add a parameter to control whether sources revert to initial predict status

   ...
   ... Three most recent entries only; see package spec for full history
   ****************************************************************************************************
   */

   -- PL/SQL Table declarations
   TYPE T_HOST_ID IS TABLE OF P_ACCOUNT.HOST_ID%TYPE
      INDEX BY BINARY_INTEGER;

   TYPE T_ENTITY_ID IS TABLE OF P_ACCOUNT.ENTITY_ID%TYPE
      INDEX BY BINARY_INTEGER;

   TYPE T_CURRENCY_CODE IS TABLE OF P_ACCOUNT.CURRENCY_CODE%TYPE
      INDEX BY BINARY_INTEGER;

   TYPE T_ACCOUNT_ID IS TABLE OF P_ACCOUNT.ACCOUNT_ID%TYPE
      INDEX BY BINARY_INTEGER;

   TYPE T_LINK_ACCOUNT_ID IS TABLE OF P_ACCOUNT.LINK_ACCOUNT_ID%TYPE
      INDEX BY BINARY_INTEGER;

   -- PL/SQL Table variables
   V_ENTITY_ID                  T_ENTITY_ID;
   V_CURRENCY_CODE              T_CURRENCY_CODE;
   LA_MAT                       LA_MATRIX;
   V_REC_ACTION                 POSITIONMATCHACTION;
   V_QUALITY_VARIABLE           QUALITYVARIABLE
                                   := QUALITYVARIABLE('E',
                                                      'D',
                                                      'C',
                                                      'B',
                                                      'A',
                                                      '',
                                                      '',
                                                      '',
                                                      '',
                                                      '',
                                                      '');
   QM_MAT                       QM_MATRIX;
   V_AMTTOTAL                   T_AMTTOTAL;

   -- match stage time intervals
   VA_TO_MATCH_STAGE            V_TO_MATCH_STAGE
                                   := V_TO_MATCH_STAGE(10 / (60 * 24),
                                                       20 / (60 * 24),
                                                       30 / (60 * 24),
                                                       1 / 24,
                                                       2 / 24,
                                                       2 / 24,
                                                       4 / 24,
                                                       4 / 24,
                                                       8 / 24,
                                                       8 / 24);

   -- Numeric Variables
   GN_MAX_INTERNAL_POS          NUMBER(1);
   GN_PRE_ADVICE_POS            NUMBER(1);
   GN_ACC_LINK_EXEMTION_LEVEL   NUMBER(1);
   GN_MAX_POS_OTHER_STAGE       P_MOVEMENT.POSITION_LEVEL%TYPE;

   -- String Variables
   VV_PREADVICE_EXPECTED        CHAR(1);

   -- Stored procedures and functions


   -- ----------------------------------------------------------------------------------------------
   -- Used to get the existence of target cross reference for the source movement
   -- and target movement supplied
   -- ----------------------------------------------------------------------------------------------
   FUNCTION FNISCROSSREFEXISTS(PHOSTID              P_MOVEMENT.HOST_ID%TYPE,
                               PENTITYID            P_MOVEMENT.ENTITY_ID%TYPE,
                               PCURRENCYCODE        P_MOVEMENT.CURRENCY_CODE%TYPE,
                               PSOURCEMOVEMENTID    P_MOVEMENT.MOVEMENT_ID%TYPE,
                               PTARGETMOVEMENTID    P_MOVEMENT.MOVEMENT_ID%TYPE)
      RETURN VARCHAR2
   IS
      VXREFEXISTS   VARCHAR2(1) := 'N';
   BEGIN
      SELECT 'Y'
        INTO VXREFEXISTS
        FROM DUAL
       WHERE EXISTS
                (SELECT XO.CROSS_REFERENCE
                   FROM P_REFERENCE_XREF XO
                  WHERE XO.MOVEMENT_ID = PTARGETMOVEMENTID
                    AND XO.ENTITY_ID = PENTITYID
                    AND XO.CURRENCY_CODE = PCURRENCYCODE
                 INTERSECT
                 SELECT XI.CROSS_REFERENCE
                   FROM P_REFERENCE_XREF XI
                  WHERE XI.MOVEMENT_ID = PSOURCEMOVEMENTID
                    AND XI.ENTITY_ID = PENTITYID
                    AND XI.CURRENCY_CODE = PCURRENCYCODE);

      RETURN VXREFEXISTS;
   EXCEPTION
      WHEN NO_DATA_FOUND
      THEN
         RETURN VXREFEXISTS;
   END;


   -- ----------------------------------------------------------------------------------------------
   -- Used to update the last_started column in p_match_driver table
   -- ----------------------------------------------------------------------------------------------
   PROCEDURE SP_UPDATE_LAST_STARTED(P_HOSTID_I         IN     VARCHAR2,
                                    P_ENTITYID_I       IN     VARCHAR2,
                                    P_CURRENCYCODE_I   IN     VARCHAR2,
                                    P_ROWUPDATE_O         OUT NUMBER)
   AS
      PRAGMA AUTONOMOUS_TRANSACTION;
      V_SYSDATE   DATE := GLOBAL_VAR.SYS_DATE;
   BEGIN
      UPDATE P_MATCH_DRIVER
         SET NEW_MOVE_FLAG     = 'N',
             PROCESSING_FLAG   = 'Y',
             UPDATE_USER       = 'SYSTEM',
             UPDATE_DATE       = V_SYSDATE,
             LAST_STARTED      = V_SYSDATE
       WHERE HOST_ID = P_HOSTID_I
         AND ENTITY_ID = P_ENTITYID_I
         AND CURRENCY_CODE = P_CURRENCYCODE_I
         AND NEW_MOVE_FLAG = 'Y'
         AND PROCESSING_FLAG = 'N';

      P_ROWUPDATE_O   := SQL%ROWCOUNT;

      COMMIT;
   END SP_UPDATE_LAST_STARTED;


   -- ----------------------------------------------------------------------------------------------
   -- Used to update the p_match_driver table.
   -- To avoid row locking between transactions (movement insertion and matching process)
   -- this has been declared as autonomous.
   -- It's used within this package and it will call end of the matching process of each currency
   -- ----------------------------------------------------------------------------------------------
   PROCEDURE SPUPDATEMATCHEND(PHOSTID         IN P_MOVEMENT.HOST_ID%TYPE,
                              PENTITYID       IN P_MOVEMENT.ENTITY_ID%TYPE,
                              PCURRENCYCODE   IN P_MOVEMENT.CURRENCY_CODE%TYPE,
                              PSTATUSFLAG     IN VARCHAR2,
                              PNEWMOVEFLAG    IN VARCHAR2)
   AS
      PRAGMA AUTONOMOUS_TRANSACTION;
   BEGIN
      -- update processing flag with 'N' and
      -- regarding new move flag the update has been done when the pStatusFlag is
      -- 'E' - 'end of the current currency'
      -- then udpate the new move flag with new move flag (i.e don't disturb the existing status it may 'Y' or 'N')
      -- 'D' - Disabled or 'X' - Error then update the new flag as 'Y' for that currency

      UPDATE P_MATCH_DRIVER
         SET PROCESSING_FLAG   = 'N',
             NEW_MOVE_FLAG     = DECODE(PSTATUSFLAG, 'E', NEW_MOVE_FLAG, PNEWMOVEFLAG),
             UPDATE_USER       = 'SYSTEM',
             UPDATE_DATE       = GLOBAL_VAR.SYS_DATE
       WHERE HOST_ID = PHOSTID
         AND ENTITY_ID = PENTITYID
         AND CURRENCY_CODE = PCURRENCYCODE;

      COMMIT;
   EXCEPTION
      WHEN OTHERS
      THEN
         NULL;
   END SPUPDATEMATCHEND;


   -- ----------------------------------------------------------------------------------------------
   -- Return the source account when source and target account are exists as a linked account
   -- in PL/SQL table [la_mat] if not found then return the target account as output.
   -- ----------------------------------------------------------------------------------------------
   FUNCTION FN_RETURN_LINKED_TO_ACCOUNT_ID(PV_TGT_ACCOUNT_ID_IN IN VARCHAR2, PV_SRC_ACCOUNT_ID_IN IN VARCHAR2)
      RETURN VARCHAR2
   IS
      VV_LINKED   VARCHAR2(35);
   BEGIN
      -- Initialize the the linked flag as not linked
      VV_LINKED   := 'N';

      -- Check target account is linked with source then set the linked flag
      BEGIN
         IF (LA_MAT(PV_TGT_ACCOUNT_ID_IN)(PV_SRC_ACCOUNT_ID_IN).EXIST = 'Y')
         THEN
            VV_LINKED   := 'Y';
         END IF;
      EXCEPTION
         WHEN NO_DATA_FOUND
         THEN
            NULL;
      END;

      -- Check source account is linked with target then set the linked flag
      BEGIN
         IF (LA_MAT(PV_SRC_ACCOUNT_ID_IN)(PV_TGT_ACCOUNT_ID_IN).EXIST = 'Y')
         THEN
            VV_LINKED   := 'Y';
         END IF;
      EXCEPTION
         WHEN NO_DATA_FOUND
         THEN
            NULL;
      END;

      -- When linked flag = 'Y' then return source account otherwise return target account
      IF VV_LINKED = 'Y'
      THEN
         RETURN PV_SRC_ACCOUNT_ID_IN;
      ELSE
         RETURN PV_TGT_ACCOUNT_ID_IN;
      END IF;
   EXCEPTION
      WHEN NO_DATA_FOUND
      THEN
         -- return target account as output
         RETURN PV_TGT_ACCOUNT_ID_IN;
   END FN_RETURN_LINKED_TO_ACCOUNT_ID;


   -- ----------------------------------------------------------------------------------------------
   -- Clear out supporting tables
   -- ----------------------------------------------------------------------------------------------
   PROCEDURE SPREMOVESUPPORTTABLESREC(PDELSUPPORTTAB IN TDELSUPPORTTAB)
   IS
   BEGIN
      IF PDELSUPPORTTAB.COUNT > 0
      THEN
         FOR I IN PDELSUPPORTTAB.FIRST .. PDELSUPPORTTAB.LAST
         LOOP
            DELETE FROM P_B_TARGET_CROSS_REF
                  WHERE HOST_ID = PDELSUPPORTTAB(I).HOSTID
                    AND ENTITY_ID = PDELSUPPORTTAB(I).ENTITYID
                    AND MOVEMENT_ID = PDELSUPPORTTAB(I).MOVEMENTID;

            DELETE FROM P_MOVEMENT_LOCK
                  WHERE HOST_ID = PDELSUPPORTTAB(I).HOSTID
                    AND ENTITY_ID = PDELSUPPORTTAB(I).ENTITYID
                    AND CURRENCY_CODE = PDELSUPPORTTAB(I).CURRENCYCODE
                    AND MOVEMENT_ID = PDELSUPPORTTAB(I).MOVEMENTID
                    AND UPDATE_USER = 'SYSTEM';
         END LOOP;

         COMMIT;
      END IF;
   END SPREMOVESUPPORTTABLESREC;


   -- ----------------------------------------------------------------------------------------------
   -- Used to update the p_match_driver status, delete the records from
   -- P_MOVEMENT_LOCK and P_B_TARGET_MOVEMENTS table and insert en entry to system log
   -- for status of matching process for each host/entity/currency. [BEGIN/DISABLE/END]
   -- ----------------------------------------------------------------------------------------------
   PROCEDURE SP_REMOVE_P_B_TARGETS(PV_HOST_ID_IN         IN VARCHAR2,
                                   PV_ENTITY_ID_IN       IN VARCHAR2,
                                   PV_CURRENCY_CODE_IN   IN VARCHAR2,
                                   PV_FLAG_DEL_ALL       IN VARCHAR2 DEFAULT 'N',
                                   PN_MOVEMENT_ID_IN     IN NUMBER DEFAULT 0,
                                   -- pv_status_flag_in values:
                                   --   'B'   -- Begin
                                   --   'D'   -- Disabled
                                   --   'E'   -- End
                                   PV_STATUS_FLAG_IN     IN VARCHAR2 DEFAULT NULL)
   IS
      -- String Variables
      VV_MATCH_RUN_STATUS   VARCHAR2(10);
      VV_NEW_MOVE_FLAG      CHAR(1);
   BEGIN
      IF PV_FLAG_DEL_ALL = 'Y'
      THEN
         IF PV_STATUS_FLAG_IN != 'B'
         THEN
            -- P_Match_Driver new_move_flag status update depending on the input status flag
            IF PV_STATUS_FLAG_IN = 'E'
            THEN
               NULL;
            -- vv_new_move_flag := 'new_move_flag';
            ELSE
               VV_NEW_MOVE_FLAG   := 'Y';
            END IF;

            IF PV_STATUS_FLAG_IN != 'S' -- Source cursor end
            THEN
               PK_MATCHING_PROCESS.SPUPDATEMATCHEND(PV_HOST_ID_IN,
                                                    PV_ENTITY_ID_IN,
                                                    PV_CURRENCY_CODE_IN,
                                                    PV_STATUS_FLAG_IN,
                                                    VV_NEW_MOVE_FLAG);
            END IF;

            -- Delete all the movements
            DELETE FROM P_B_TARGET_MOVEMENTS;

            DELETE FROM P_B_TARGET_CROSS_REF;

            DELETE FROM P_MOVEMENT_LOCK
                  WHERE HOST_ID = PV_HOST_ID_IN
                    AND ENTITY_ID = PV_ENTITY_ID_IN
                    AND CURRENCY_CODE = PV_CURRENCY_CODE_IN
                    AND UPDATE_USER = 'SYSTEM';
         END IF;

         -- status flag values for logging message to s_system_log
         IF PV_STATUS_FLAG_IN = 'B'
         THEN
            VV_MATCH_RUN_STATUS   := 'Start';
         ELSIF PV_STATUS_FLAG_IN = 'D'
         THEN
            VV_MATCH_RUN_STATUS   := 'Disabled';
         ELSIF PV_STATUS_FLAG_IN = 'E'
         THEN
            VV_MATCH_RUN_STATUS   := 'End';
         END IF;

         -- Logs a message to S_system_log
         IF PV_STATUS_FLAG_IN = 'B'
         OR PV_STATUS_FLAG_IN = 'D'
         OR PV_STATUS_FLAG_IN = 'E'
         THEN
            INSERT INTO S_SYSTEM_LOG(SYSTEM_SEQ_NO,
                                     HOST_ID,
                                     LOG_DATE,
                                     USER_ID,
                                     IP_ADDRESS,
                                     PROCESS,
                                     ACTION,
                                     UPDATE_DATE,
                                     UPDATE_USER)
                 VALUES (S_SYSTEM_LOG_SEQUENCE.NEXTVAL,
                         PV_HOST_ID_IN,
                         GLOBAL_VAR.SYS_DATE,
                         'SYSTEM',
                         'DB_SERVER',
                         'Matching Process: ' || PV_ENTITY_ID_IN || '/' || PV_CURRENCY_CODE_IN,
                         VV_MATCH_RUN_STATUS,
                         GLOBAL_VAR.SYS_DATE,
                         'SYSTEM');
         END IF;
      ELSE
         -- Delete the input movement_id
         DELETE FROM P_B_TARGET_MOVEMENTS
               WHERE MOVEMENT_ID = PN_MOVEMENT_ID_IN;

         DELETE FROM P_B_TARGET_CROSS_REF
               WHERE MOVEMENT_ID = PN_MOVEMENT_ID_IN;

         DELETE FROM P_MOVEMENT_LOCK
               WHERE HOST_ID = PV_HOST_ID_IN
                 AND ENTITY_ID = PV_ENTITY_ID_IN
                 AND CURRENCY_CODE = PV_CURRENCY_CODE_IN
                 AND MOVEMENT_ID = PN_MOVEMENT_ID_IN
                 AND UPDATE_USER = 'SYSTEM';
      END IF;

      COMMIT;
   EXCEPTION
      WHEN OTHERS
      THEN
         SP_ERROR_LOG(PV_HOST_ID_IN,
                      'SYSTEM',
                      'DB-SERVER',
                      'PK_MATCHING_PROCESS.SP_REMOVE_P_B_TARGETS Error for ' || PV_ENTITY_ID_IN || '/' || PV_CURRENCY_CODE_IN,
                      SQLCODE,
                      SQLERRM);
   END SP_REMOVE_P_B_TARGETS;


   -- ----------------------------------------------------------------------------------------------
   -- Used to add the movement details (related to matching)
   -- into p_b_target_movements table for further processing of those movements
   -- ----------------------------------------------------------------------------------------------
   PROCEDURE SP_ADD_P_B_TARGETS(PN_MOVEMENT_ID_IN           IN NUMBER,
                                PV_HOST_ID_IN               IN VARCHAR2,
                                PV_ENTITY_ID_IN             IN VARCHAR2,
                                PV_CURRENCY_CODE_IN         IN VARCHAR2,
                                PV_MATCH_ID_IN              IN NUMBER,
                                PV_MATCH_STATUS_IN          IN VARCHAR2,
                                PV_PREDICT_STATUS_IN        IN VARCHAR2,
                                PN_POS_LEVEL_IN             IN NUMBER,
                                PV_VALUE_DATE_IN            IN VARCHAR2,
                                PV_COUNTERPARTY_ID_IN       IN VARCHAR2,
                                PV_BENEFICIARY_ID_IN        IN VARCHAR2,
                                PV_CUSTODIAN_ID_IN          IN VARCHAR2,
                                PV_BOOKCODE_IN              IN VARCHAR2,
                                PN_AMOUNT_IN                IN NUMBER,
                                PV_VALID_IN                 IN VARCHAR2,
                                PN_QUALITYFLAG_IN           IN NUMBER,
                                PV_ACCOUNT_ID_IN            IN VARCHAR2,
                                PV_INSERT_LOCK              IN VARCHAR2,
                                PV_INSERT_TARGET            IN VARCHAR2,
                                PV_MATCHINGPARTY            IN P_MOVEMENT.MATCHING_PARTY%TYPE,
                                PV_INITIAL_PREDICT_STATUS   IN VARCHAR2)
   IS
   BEGIN
      IF PV_INSERT_LOCK = 'Y'
      THEN
         -- insert into p_movement_lock for implementing soft locking to prevent others
         -- to modify the details
         INSERT INTO P_MOVEMENT_LOCK(MOVEMENT_ID,
                                     UPDATE_USER,
                                     UPDATE_DATE,
                                     HOST_ID,
                                     ENTITY_ID,
                                     CURRENCY_CODE)
              VALUES (PN_MOVEMENT_ID_IN,
                      'SYSTEM',
                      GLOBAL_VAR.SYS_DATE,
                      PV_HOST_ID_IN,
                      PV_ENTITY_ID_IN,
                      PV_CURRENCY_CODE_IN);
      END IF;

      IF PV_INSERT_TARGET = 'Y'
      THEN
         -- Insert cross references from cross reference table
         -- to temporary cross reference table for the movement supplied
         -- This will be useful future checks

         INSERT INTO P_B_TARGET_CROSS_REF(HOST_ID,
                                          ENTITY_ID,
                                          MOVEMENT_ID,
                                          CURRENCY_CODE,
                                          CROSS_REFERENCE)
            SELECT XR.HOST_ID, XR.ENTITY_ID, XR.MOVEMENT_ID,
                   XR.CURRENCY_CODE, XR.CROSS_REFERENCE
              FROM P_REFERENCE_XREF XR
             WHERE XR.HOST_ID = PV_HOST_ID_IN
               AND XR.ENTITY_ID = PV_ENTITY_ID_IN
               AND XR.MOVEMENT_ID = PN_MOVEMENT_ID_IN;

         -- insert into p_b_target_movements for processing in matching
         INSERT INTO P_B_TARGET_MOVEMENTS(MOVEMENT_ID,
                                          HOST_ID,
                                          ENTITY_ID,
                                          CURRENCY_CODE,
                                          MATCH_ID,
                                          MATCH_STATUS,
                                          PREDICT_STATUS,
                                          POS_LEVEL,
                                          VALUE_DATE,
                                          COUNTERPARTY_ID,
                                          BENEFICIARY_ID,
                                          CUSTODIAN_ID,
                                          BOOKCODE,
                                          AMOUNT,
                                          VALID,
                                          QUALITYFLAG,
                                          ACCOUNT_ID,
                                          MATCHING_PARTY,
                                          INITIAL_PREDICT_STATUS)
              VALUES (PN_MOVEMENT_ID_IN,
                      PV_HOST_ID_IN,
                      PV_ENTITY_ID_IN,
                      PV_CURRENCY_CODE_IN,
                      PV_MATCH_ID_IN,
                      PV_MATCH_STATUS_IN,
                      PV_PREDICT_STATUS_IN,
                      PN_POS_LEVEL_IN,
                      PV_VALUE_DATE_IN,
                      PV_COUNTERPARTY_ID_IN,
                      PV_BENEFICIARY_ID_IN,
                      PV_CUSTODIAN_ID_IN,
                      PV_BOOKCODE_IN,
                      PN_AMOUNT_IN,
                      PV_VALID_IN,
                      PN_QUALITYFLAG_IN,
                      PV_ACCOUNT_ID_IN,
                      PV_MATCHINGPARTY,
                      PV_INITIAL_PREDICT_STATUS);
      END IF;

      COMMIT;
   END SP_ADD_P_B_TARGETS;


   -- ----------------------------------------------------------------------------------------------
   -- Used to identify whether input reference is a valid reference or not
   -- (A valid reference must contain at least one numeric character, and must be longer
   -- than 4 characters)
   -- ----------------------------------------------------------------------------------------------
   FUNCTION FN_GET_VALID_REFERENCE(PV_REFERENCE_IN IN VARCHAR2)
      RETURN VARCHAR2
   IS
      VV_RETURN   VARCHAR2(35);
   BEGIN
      -- check the reference has any numeric and length >= 4
      -- then return reference otherwise return null
      IF REGEXP_INSTR(PV_REFERENCE_IN, '[0-9]') > 0
     AND LENGTH(PV_REFERENCE_IN) >= 4
      THEN
         VV_RETURN   := PV_REFERENCE_IN;
      ELSE
         VV_RETURN   := NULL;
      END IF;

      RETURN VV_RETURN;
   END FN_GET_VALID_REFERENCE;


   -- ----------------------------------------------------------------------------------------------
   -- Take a string of up to 80 characters and perform the following actions:
   -- Remove all non-alphanumeric characters (when remove_non_alphanum = 'Y')
   -- Remove spaces (when remove_spaces = 'Y')
   -- Strip any leading zeroes from the left of the string (when remove_leading_zeroes  = 'Y')
   -- Convert the result to uppercase
   -- ----------------------------------------------------------------------------------------------
   FUNCTION FN_PROCESS_REFERENCE(INPUT_STRING             VARCHAR2,
                                 REMOVE_NON_ALPHANUM      VARCHAR2 DEFAULT 'Y',
                                 REMOVE_SPACES            VARCHAR2 DEFAULT 'Y',
                                 REMOVE_LEADING_ZEROES    VARCHAR2 DEFAULT 'Y')
      RETURN VARCHAR2
   IS
      RESULT_STRING   VARCHAR2(80);
   BEGIN
      RESULT_STRING   := INPUT_STRING;

      -- Remove special characters
      IF REMOVE_NON_ALPHANUM = 'Y'
      THEN
         RESULT_STRING   := REGEXP_REPLACE(RESULT_STRING, '[[:punct:]]');
      END IF;

      -- Remove spaces
      IF REMOVE_SPACES = 'Y'
      THEN
         RESULT_STRING   := REGEXP_REPLACE(RESULT_STRING, ' ');
      END IF;

      -- Remove leading zeroes
      IF REMOVE_LEADING_ZEROES = 'Y'
      THEN
         RESULT_STRING   := LTRIM(RESULT_STRING, '0');
      END IF;

      -- convert the result to uppercase
      RESULT_STRING   := UPPER(RESULT_STRING);

      RETURN RESULT_STRING;
   END FN_PROCESS_REFERENCE;


   -- ----------------------------------------------------------------------------------------------
   -- Used to enrich the beneficiary_id of the highest internal position when
   -- beneficiary is null and all the internal position movements reference same.
   -- ----------------------------------------------------------------------------------------------
   FUNCTION FN_ENRICH_BENEFICIARY(PV_HOST_ID_IN         IN VARCHAR2,
                                  PV_ENTITY_ID_IN       IN VARCHAR2,
                                  PV_CURRENCY_CODE_IN   IN VARCHAR2,
                                  PN_HIGH_INT_POS_IN    IN NUMBER,
                                  PN_POS_THRESHOLD      IN NUMBER)
      RETURN VARCHAR2
   IS
      -- Numeric Variables
      VERRORCODE          NUMBER;

      -- String Variables
      VERRORDESC          S_ERROR_LOG.ERROR_DESC%TYPE;
      VUSERID             S_ERROR_LOG.USER_ID%TYPE := 'SYSTEM';
      VIPADDRESS          S_ERROR_LOG.IP_ADDRESS%TYPE := 'DB SERVER';
      VSOURCE             S_ERROR_LOG.SOURCE%TYPE := 'Auto Matching - fn_enrich_beneficiary ';
      VV_ERR_LOC          VARCHAR2(10) := 0;
      V_CURRENCY_CODE     P_MOVEMENT.CURRENCY_CODE%TYPE;
      VV_BENEFICIARY_ID   P_B_TARGET_MOVEMENTS.BENEFICIARY_ID%TYPE;

      -- Cursor Declarations
      -- Source Cursor used to get the highest internal position movement
      CURSOR CR_SOURCE_MOVEMENT
      IS
         SELECT HOST_ID, ENTITY_ID, CURRENCY_CODE,
                VALUE_DATE, POS_LEVEL, AMOUNT,
                ACCOUNT_ID, COUNTERPARTY_ID, BENEFICIARY_ID,
                CUSTODIAN_ID, BOOKCODE, MOVEMENT_ID,
                MATCHING_PARTY
           FROM P_B_TARGET_MOVEMENTS A
          WHERE HOST_ID = PV_HOST_ID_IN
            AND ENTITY_ID = PV_ENTITY_ID_IN
            AND CURRENCY_CODE = PV_CURRENCY_CODE_IN
            AND POS_LEVEL = (SELECT MAX(POS_LEVEL)
                               FROM P_B_TARGET_MOVEMENTS
                              WHERE HOST_ID = A.HOST_ID
                                AND ENTITY_ID = A.ENTITY_ID
                                AND CURRENCY_CODE = A.CURRENCY_CODE
                                AND POS_LEVEL <= PN_HIGH_INT_POS_IN);

      -- Target Cursor which get the lower position movements with their quality
      CURSOR CR_TARGET_MOVEMENT(SOURCE_VALUE_DATE         DATE,
                                SOURCE_AMOUNT             NUMBER,
                                SOURCE_ACCOUNT_ID         VARCHAR2,
                                SOURCE_COUNTERPARTY_ID    VARCHAR2,
                                SOURCE_BENEFICIARY_ID     VARCHAR2,
                                SOURCE_CUSTODIAN_ID       VARCHAR2,
                                SOURCE_BOOKCODE           VARCHAR2,
                                SOURCE_POSLEVEL           NUMBER,
                                SOURCE_MOVEMENT_ID        NUMBER,
                                CV_CURRENCY_CODE          VARCHAR2,
                                SOURCE_MATCHINGPARTY      P_MOVEMENT.MATCHING_PARTY%TYPE)
      IS
         SELECT MOVEMENT_ID,
                PK_MATCHING_PROCESS.FN_UPDATE_P_B_MATQUAL_POSLEVEL(HOST_ID,
                                                                   ENTITY_ID,
                                                                   CV_CURRENCY_CODE,
                                                                   SOURCE_VALUE_DATE,
                                                                   VALUE_DATE,
                                                                   SOURCE_AMOUNT,
                                                                   AMOUNT,
                                                                   0,
                                                                   SOURCE_ACCOUNT_ID,
                                                                   ACCOUNT_ID,
                                                                   SOURCE_COUNTERPARTY_ID,
                                                                   COUNTERPARTY_ID,
                                                                   SOURCE_BENEFICIARY_ID,
                                                                   BENEFICIARY_ID,
                                                                   SOURCE_CUSTODIAN_ID,
                                                                   CUSTODIAN_ID,
                                                                   SOURCE_BOOKCODE,
                                                                   BOOKCODE,
                                                                   SOURCE_POSLEVEL,
                                                                   SOURCE_MOVEMENT_ID,
                                                                   MOVEMENT_ID,
                                                                   'N',
                                                                   SOURCE_MATCHINGPARTY,
                                                                   MATCHING_PARTY,
                                                                   POS_LEVEL,
                                                                   PN_POS_THRESHOLD)
                   T_QUALITY
           FROM P_B_TARGET_MOVEMENTS
          WHERE HOST_ID = PV_HOST_ID_IN
            AND ENTITY_ID = PV_ENTITY_ID_IN
            AND CURRENCY_CODE = PV_CURRENCY_CODE_IN
            AND POS_LEVEL < SOURCE_POSLEVEL;
   BEGIN
      -- Initialize beneficiary to 0 to indicate beneficiary is not enriched.
      VV_BENEFICIARY_ID   := '0';
      VV_ERR_LOC          := '10';
      -- Check the quality settings are exists for current currency otherwise set the
      -- currency_code to all currency for quality selection
      V_CURRENCY_CODE     := PV_CURRENCY_CODE_IN;

      -- Open and loop through source cursor
      FOR R_SOURCE_MOVEMENT IN CR_SOURCE_MOVEMENT
      LOOP
         VV_ERR_LOC   := '20';

         -- check the source movements references are valid
         -- if valid use same reference else return null

         -- Open and loop though target cursor with parameters values as source cursor values
         FOR R_TARGET_MOVEMENT IN CR_TARGET_MOVEMENT(R_SOURCE_MOVEMENT.VALUE_DATE,
                                                     R_SOURCE_MOVEMENT.AMOUNT,
                                                     R_SOURCE_MOVEMENT.ACCOUNT_ID,
                                                     R_SOURCE_MOVEMENT.COUNTERPARTY_ID,
                                                     R_SOURCE_MOVEMENT.BENEFICIARY_ID,
                                                     R_SOURCE_MOVEMENT.CUSTODIAN_ID,
                                                     R_SOURCE_MOVEMENT.BOOKCODE,
                                                     R_SOURCE_MOVEMENT.POS_LEVEL,
                                                     R_SOURCE_MOVEMENT.MOVEMENT_ID,
                                                     V_CURRENCY_CODE,
                                                     R_SOURCE_MOVEMENT.MATCHING_PARTY)
         LOOP
            VV_ERR_LOC   := '30';

            -- Check if the target quality is 5 or 4 then select the beneficiary
            -- from lower position movements and update the beneficiary where it is null
            -- for the positions <= highest internal position.
            IF R_TARGET_MOVEMENT.T_QUALITY = 5
            OR R_TARGET_MOVEMENT.T_QUALITY = 4
            THEN
               -- select the beneficiary id from p_b_target_movements
               -- for position lower than highest internal position
               SELECT DISTINCT B.BENEFICIARY_ID
                 INTO VV_BENEFICIARY_ID
                 FROM P_B_TARGET_MOVEMENTS B
                WHERE B.HOST_ID = PV_HOST_ID_IN
                  AND B.ENTITY_ID = PV_ENTITY_ID_IN
                  AND B.CURRENCY_CODE = PV_CURRENCY_CODE_IN
                  AND B.POS_LEVEL < PN_HIGH_INT_POS_IN
                  AND B.BENEFICIARY_ID IS NOT NULL;

               VV_ERR_LOC   := '40';

               -- Apply the selected beneficiary id for position lower than equal
               -- to highest internal position and beneficiary id is null
               UPDATE P_B_TARGET_MOVEMENTS A
                  SET BENEFICIARY_ID   = VV_BENEFICIARY_ID
                WHERE A.HOST_ID = PV_HOST_ID_IN
                  AND A.ENTITY_ID = PV_ENTITY_ID_IN
                  AND A.CURRENCY_CODE = PV_CURRENCY_CODE_IN
                  AND A.POS_LEVEL <= PN_HIGH_INT_POS_IN
                  AND A.BENEFICIARY_ID IS NULL
                  AND MOVEMENT_ID = R_SOURCE_MOVEMENT.MOVEMENT_ID;

               COMMIT;
            END IF;
         END LOOP;
      END LOOP;

      RETURN VV_BENEFICIARY_ID;
   EXCEPTION
      WHEN OTHERS
      THEN
         VERRORCODE   := SQLCODE;
         VERRORDESC   := SQLERRM;
         SP_ERROR_LOG(PV_HOST_ID_IN,
                      VUSERID,
                      VIPADDRESS,
                      VSOURCE || ' ERROR @ Location - ' || VV_ERR_LOC,
                      VERRORCODE,
                      VERRORDESC);
         RETURN VV_BENEFICIARY_ID;
   END FN_ENRICH_BENEFICIARY;


   -- ----------------------------------------------------------------------------------------------
   -- Used to re-calculate quality of each movement to highest position in P_B_TARGET_MOVEMENTS.
   -- When source movement is the highest position in the current set.
   -- Then movement_id of the source position is taken to re-calculate the quality.
   -- ----------------------------------------------------------------------------------------------
   PROCEDURE SP_INNER_MATCHING(PV_HOST_ID_IN         IN VARCHAR2,
                               PV_ENTITY_ID_IN       IN VARCHAR2,
                               PV_CURRENCY_CODE_IN   IN VARCHAR2,
                               PN_POS_LVL_IN         IN NUMBER,
                               PN_SRC_POS_LVL_IN     IN NUMBER,
                               PN_POS_THRESHOLD      IN NUMBER,
                               PN_TOLERANCE_IN       IN NUMBER,
                               PN_MOVEMENT_ID_IN     IN NUMBER DEFAULT 0,
                               PN_MIN_POS_LEVEL_IN   IN NUMBER DEFAULT 0)
   IS
      -- Numeric Variables
      VERRORCODE            NUMBER;
      VN_MAX_QUALITYFLAG    NUMBER(1);
      VN_PROCESS_POSITION   NUMBER(1);

      -- String Variables
      VERRORDESC            S_ERROR_LOG.ERROR_DESC%TYPE;
      VUSERID               S_ERROR_LOG.USER_ID%TYPE := 'SYSTEM';
      VIPADDRESS            S_ERROR_LOG.IP_ADDRESS%TYPE := 'DB SERVER';
      VSOURCE               S_ERROR_LOG.SOURCE%TYPE := 'Auto Matching - sp_inner_matching ';
      VV_ERR_LOC            VARCHAR2(10) := 0;
      V_CURRENCY_CODE       P_MOVEMENT.CURRENCY_CODE%TYPE;

      -- Source movement cursor selected when highest position > current source movement position
      CURSOR CR_SOURCE_MOVEMENT
      IS
         SELECT HOST_ID, ENTITY_ID, CURRENCY_CODE,
                VALUE_DATE, POS_LEVEL, AMOUNT,
                ACCOUNT_ID, COUNTERPARTY_ID, BENEFICIARY_ID,
                CUSTODIAN_ID, BOOKCODE, MOVEMENT_ID,
                MATCHING_PARTY
           FROM P_B_TARGET_MOVEMENTS A
          WHERE HOST_ID = PV_HOST_ID_IN
            AND ENTITY_ID = PV_ENTITY_ID_IN
            AND CURRENCY_CODE = PV_CURRENCY_CODE_IN
            AND POS_LEVEL = PN_POS_LVL_IN;

      R_SOURCE_MOVEMENT     CR_SOURCE_MOVEMENT%ROWTYPE;

      -- Source movement cursor max selected when current source movement position is the highest position
      CURSOR CR_SOURCE_MOVEMENT_MAX
      IS
         SELECT HOST_ID, ENTITY_ID, CURRENCY_CODE,
                VALUE_DATE, POS_LEVEL, AMOUNT,
                ACCOUNT_ID, COUNTERPARTY_ID, BENEFICIARY_ID,
                CUSTODIAN_ID, BOOKCODE, MOVEMENT_ID,
                MATCHING_PARTY
           FROM P_B_TARGET_MOVEMENTS A
          WHERE HOST_ID = PV_HOST_ID_IN
            AND ENTITY_ID = PV_ENTITY_ID_IN
            AND CURRENCY_CODE = PV_CURRENCY_CODE_IN
            AND POS_LEVEL = PN_POS_LVL_IN
            AND MOVEMENT_ID = PN_MOVEMENT_ID_IN;

      -- Target Cursor which get the lower position movements with their quality
      CURSOR CR_TARGET_MOVEMENT(SOURCE_VALUE_DATE         DATE,
                                SOURCE_AMOUNT             NUMBER,
                                SOURCE_ACCOUNT_ID         VARCHAR2,
                                SOURCE_COUNTERPARTY_ID    VARCHAR2,
                                SOURCE_BENEFICIARY_ID     VARCHAR2,
                                SOURCE_CUSTODIAN_ID       VARCHAR2,
                                SOURCE_BOOKCODE           VARCHAR2,
                                SOURCE_POSLEVEL           NUMBER,
                                SOURCE_MOVEMENT_ID        NUMBER,
                                CV_CURRENCY_CODE          VARCHAR2,
                                SOURCE_MATCHINGPARTY      VARCHAR2)
      IS
         SELECT MOVEMENT_ID,
                PK_MATCHING_PROCESS.FN_UPDATE_P_B_MATQUAL_POSLEVEL(HOST_ID,
                                                                   ENTITY_ID,
                                                                   CV_CURRENCY_CODE,
                                                                   SOURCE_VALUE_DATE,
                                                                   VALUE_DATE,
                                                                   SOURCE_AMOUNT,
                                                                   AMOUNT,
                                                                   PN_TOLERANCE_IN,
                                                                   SOURCE_ACCOUNT_ID,
                                                                   ACCOUNT_ID,
                                                                   SOURCE_COUNTERPARTY_ID,
                                                                   COUNTERPARTY_ID,
                                                                   SOURCE_BENEFICIARY_ID,
                                                                   BENEFICIARY_ID,
                                                                   SOURCE_CUSTODIAN_ID,
                                                                   CUSTODIAN_ID,
                                                                   SOURCE_BOOKCODE,
                                                                   BOOKCODE,
                                                                   SOURCE_POSLEVEL,
                                                                   SOURCE_MOVEMENT_ID,
                                                                   MOVEMENT_ID,
                                                                   'N',
                                                                   SOURCE_MATCHINGPARTY,
                                                                   MATCHING_PARTY,
                                                                   POS_LEVEL,
                                                                   PN_POS_THRESHOLD)
                   T_QUALITY
           FROM P_B_TARGET_MOVEMENTS
          WHERE HOST_ID = PV_HOST_ID_IN
            AND ENTITY_ID = PV_ENTITY_ID_IN
            AND CURRENCY_CODE = PV_CURRENCY_CODE_IN
            AND MOVEMENT_ID != SOURCE_MOVEMENT_ID;
   BEGIN
      VV_ERR_LOC        := '10';
      -- Check the quality settings are exists for current currency otherwise
      -- set the currency_code to all currency for quality selection
      V_CURRENCY_CODE   := PV_CURRENCY_CODE_IN;

      -- When the quality is not found for the highest position then
      -- use the source position for quality selection
      -- Also log the warning as quality is not based on the highest
      -- position as in the current match.
      DECLARE
         VN_QUAL_EXISTS   NUMBER;
      BEGIN
         VN_QUAL_EXISTS        := QM_MAT(PN_POS_LVL_IN)(5).MASK_VALUE;
         VN_PROCESS_POSITION   := PN_POS_LVL_IN;
      EXCEPTION
         WHEN NO_DATA_FOUND
         THEN
            IF GN_WARNING_MSG = 0
            THEN
               GN_WARNING_MSG   := 1;
               SP_ERROR_LOG(
                  PV_HOST_ID_IN,
                  VUSERID,
                  VIPADDRESS,
                  VSOURCE,
                  1001,
                     'WARNING: Quality settings not present for ENTITY/CURRENCY/POSITION - '
                  || PV_ENTITY_ID_IN
                  || '/'
                  || PV_CURRENCY_CODE_IN
                  || '/'
                  || PN_POS_LVL_IN
                  || '.  Continuing using quality parameters for source position level '
                  || PN_SRC_POS_LVL_IN
                  || '.  ACTION: Ensure quality parameters are present for all position levels expected to match.');
            END IF;

            VN_PROCESS_POSITION   := PN_SRC_POS_LVL_IN;
      END;

      -- Check if the input movement id is 0 (i.e source position < highest position)
      -- open the source cursor otherwise open the source cursor (max) and fetch the data
      IF PN_MOVEMENT_ID_IN = 0
      THEN
         OPEN CR_SOURCE_MOVEMENT;
      ELSE
         OPEN CR_SOURCE_MOVEMENT_MAX;
      END IF;

      LOOP
         IF PN_MOVEMENT_ID_IN = 0
         THEN
            FETCH CR_SOURCE_MOVEMENT INTO R_SOURCE_MOVEMENT;

            EXIT WHEN CR_SOURCE_MOVEMENT%NOTFOUND;
         ELSE
            FETCH CR_SOURCE_MOVEMENT_MAX INTO R_SOURCE_MOVEMENT;

            EXIT WHEN CR_SOURCE_MOVEMENT_MAX%NOTFOUND;
         END IF;

         VV_ERR_LOC   := '20';

         -- Open and loop though target cursor with parameters values as source cursor values
         FOR R_TARGET_MOVEMENT IN CR_TARGET_MOVEMENT(R_SOURCE_MOVEMENT.VALUE_DATE,
                                                     R_SOURCE_MOVEMENT.AMOUNT,
                                                     R_SOURCE_MOVEMENT.ACCOUNT_ID,
                                                     R_SOURCE_MOVEMENT.COUNTERPARTY_ID,
                                                     R_SOURCE_MOVEMENT.BENEFICIARY_ID,
                                                     R_SOURCE_MOVEMENT.CUSTODIAN_ID,
                                                     R_SOURCE_MOVEMENT.BOOKCODE,
                                                     VN_PROCESS_POSITION,
                                                     R_SOURCE_MOVEMENT.MOVEMENT_ID,
                                                     V_CURRENCY_CODE,
                                                     R_SOURCE_MOVEMENT.MATCHING_PARTY)
         LOOP
            VV_ERR_LOC   := '30';

            -- Update movements that selected in target cursor with their quality
            UPDATE P_B_TARGET_MOVEMENTS A
               SET QUALITYFLAG   = R_TARGET_MOVEMENT.T_QUALITY
             WHERE A.HOST_ID = PV_HOST_ID_IN
               AND A.ENTITY_ID = PV_ENTITY_ID_IN
               AND A.CURRENCY_CODE = PV_CURRENCY_CODE_IN
               AND A.MOVEMENT_ID = R_TARGET_MOVEMENT.MOVEMENT_ID;

            COMMIT;
         END LOOP;

         VV_ERR_LOC   := '40';

         -- When min position level parameter is not 0 then
         IF PN_MIN_POS_LEVEL_IN != 0
         THEN
            VV_ERR_LOC   := '50';

            DECLARE
               VD_MIN_VALUE_DATE       DATE;
               VV_MIN_ACCOUNT_ID       P_B_TARGET_MOVEMENTS.ACCOUNT_ID%TYPE;
               VV_MIN_BENEFICIARY_ID   P_B_TARGET_MOVEMENTS.BENEFICIARY_ID%TYPE;
               VN_MIN_AMOUNT           NUMBER(22, 4);
               VN_MIN_QUALITYFLAG      NUMBER;
               VV_MIN_MATCHINGPARTY    P_B_TARGET_MOVEMENTS.MATCHING_PARTY%TYPE;
            BEGIN
                 -- Select the value_date account_id, beneficiary_id and sum of amount
                 -- whose quality is and position level = min position level
                 SELECT A.VALUE_DATE, FN_RETURN_LINKED_TO_ACCOUNT_ID(A.ACCOUNT_ID, R_SOURCE_MOVEMENT.ACCOUNT_ID),
                        NVL(A.BENEFICIARY_ID, '@@~~##@@~~##'), SUM(A.AMOUNT),
                        A.MATCHING_PARTY
                   INTO VD_MIN_VALUE_DATE, VV_MIN_ACCOUNT_ID, VV_MIN_BENEFICIARY_ID,
                        VN_MIN_AMOUNT, VV_MIN_MATCHINGPARTY
                   FROM P_B_TARGET_MOVEMENTS A
                  WHERE A.HOST_ID = PV_HOST_ID_IN
                    AND A.ENTITY_ID = PV_ENTITY_ID_IN
                    AND A.CURRENCY_CODE = PV_CURRENCY_CODE_IN
                    AND A.QUALITYFLAG = 0
                    AND A.POS_LEVEL = PN_MIN_POS_LEVEL_IN
               GROUP BY A.VALUE_DATE,
                        FN_RETURN_LINKED_TO_ACCOUNT_ID(A.ACCOUNT_ID, R_SOURCE_MOVEMENT.ACCOUNT_ID),
                        A.BENEFICIARY_ID,
                        A.MATCHING_PARTY;

               -- Calculated the quality with source movement
               VV_ERR_LOC   := '60';
               VN_MIN_QUALITYFLAG      :=
                  PK_MATCHING_PROCESS.FN_UPDATE_P_B_MATQUAL_POSLEVEL(PV_HOST_ID_IN,
                                                                     PV_ENTITY_ID_IN,
                                                                     V_CURRENCY_CODE,
                                                                     R_SOURCE_MOVEMENT.VALUE_DATE,
                                                                     VD_MIN_VALUE_DATE,
                                                                     R_SOURCE_MOVEMENT.AMOUNT,
                                                                     VN_MIN_AMOUNT,
                                                                     PN_TOLERANCE_IN,
                                                                     R_SOURCE_MOVEMENT.ACCOUNT_ID,
                                                                     VV_MIN_ACCOUNT_ID,
                                                                     R_SOURCE_MOVEMENT.COUNTERPARTY_ID,
                                                                     '#~#~#~#~#~#~',
                                                                     R_SOURCE_MOVEMENT.BENEFICIARY_ID,
                                                                     VV_MIN_BENEFICIARY_ID,
                                                                     R_SOURCE_MOVEMENT.CUSTODIAN_ID,
                                                                     '#~#~#~#~#~#~',
                                                                     R_SOURCE_MOVEMENT.BOOKCODE,
                                                                     '#~#~#~#~#~#~',
                                                                     R_SOURCE_MOVEMENT.POS_LEVEL,
                                                                     R_SOURCE_MOVEMENT.MOVEMENT_ID,
                                                                     -1,
                                                                     'N',
                                                                     VV_MIN_MATCHINGPARTY,
                                                                     R_SOURCE_MOVEMENT.MATCHING_PARTY,
                                                                     PN_MIN_POS_LEVEL_IN,
                                                                     PN_POS_THRESHOLD);

               VV_ERR_LOC   := '70';

               -- Update the quality selected where the existing quality flag is 0
               UPDATE P_B_TARGET_MOVEMENTS
                  SET QUALITYFLAG   = VN_MIN_QUALITYFLAG
                WHERE HOST_ID = PV_HOST_ID_IN
                  AND ENTITY_ID = PV_ENTITY_ID_IN
                  AND CURRENCY_CODE = PV_CURRENCY_CODE_IN
                  AND QUALITYFLAG = 0
                  AND POS_LEVEL = PN_MIN_POS_LEVEL_IN;

               VV_ERR_LOC   := '80';
            EXCEPTION
               WHEN NO_DATA_FOUND
               THEN
                  NULL;
               WHEN DUP_VAL_ON_INDEX
               THEN
                  NULL;
               WHEN OTHERS
               THEN
                  NULL;
            END;
         END IF;

         VV_ERR_LOC   := '90';
      END LOOP;

      VV_ERR_LOC        := '100';

      IF PN_MOVEMENT_ID_IN = 0
      THEN
         CLOSE CR_SOURCE_MOVEMENT;

         -- When the movement_id input is 0 then
         -- Select the max of quality flag from other positions and
         -- Update the source position quality
         SELECT MAX(QUALITYFLAG)
           INTO VN_MAX_QUALITYFLAG
           FROM P_B_TARGET_MOVEMENTS
          WHERE HOST_ID = PV_HOST_ID_IN
            AND ENTITY_ID = PV_ENTITY_ID_IN
            AND CURRENCY_CODE = PV_CURRENCY_CODE_IN
            AND POS_LEVEL != PN_POS_LVL_IN;

         UPDATE P_B_TARGET_MOVEMENTS
            SET QUALITYFLAG   = VN_MAX_QUALITYFLAG
          WHERE HOST_ID = PV_HOST_ID_IN
            AND ENTITY_ID = PV_ENTITY_ID_IN
            AND CURRENCY_CODE = PV_CURRENCY_CODE_IN
            AND POS_LEVEL = PN_POS_LVL_IN;
      ELSE
         VV_ERR_LOC   := '110';

         CLOSE CR_SOURCE_MOVEMENT_MAX;

         -- When the movement_id input is not 0 then
         -- Select the max of quality flag from other movements and
         -- Update the source movement quality
         SELECT MAX(QUALITYFLAG)
           INTO VN_MAX_QUALITYFLAG
           FROM P_B_TARGET_MOVEMENTS
          WHERE HOST_ID = PV_HOST_ID_IN
            AND ENTITY_ID = PV_ENTITY_ID_IN
            AND CURRENCY_CODE = PV_CURRENCY_CODE_IN
            AND MOVEMENT_ID != PN_MOVEMENT_ID_IN;

         UPDATE P_B_TARGET_MOVEMENTS
            SET QUALITYFLAG   = VN_MAX_QUALITYFLAG
          WHERE HOST_ID = PV_HOST_ID_IN
            AND ENTITY_ID = PV_ENTITY_ID_IN
            AND CURRENCY_CODE = PV_CURRENCY_CODE_IN
            AND MOVEMENT_ID = PN_MOVEMENT_ID_IN;
      END IF;

      VV_ERR_LOC        := '120';
      COMMIT;
   EXCEPTION
      WHEN OTHERS
      THEN
         IF CR_SOURCE_MOVEMENT%ISOPEN
         THEN
            CLOSE CR_SOURCE_MOVEMENT;
         END IF;

         IF CR_SOURCE_MOVEMENT_MAX%ISOPEN
         THEN
            CLOSE CR_SOURCE_MOVEMENT_MAX;
         END IF;

         VERRORCODE   := SQLCODE;
         VERRORDESC   := SQLERRM;
         SP_ERROR_LOG(PV_HOST_ID_IN,
                      VUSERID,
                      VIPADDRESS,
                      VSOURCE || ' ERROR @ Location - ' || VV_ERR_LOC,
                      VERRORCODE,
                      VERRORDESC);
   END SP_INNER_MATCHING;


   -- ----------------------------------------------------------------------------------------------
   -- Gets the list of accounts and their linked account from P_ACCOUNT table
   -- and stores them in a PL/SQL table.
   -- ----------------------------------------------------------------------------------------------
   PROCEDURE SP_POPULATE_ACC_LINK_ACC_ID(L_HOSTID IN VARCHAR2, L_ENTITYID IN VARCHAR2, L_CURRENCYCODE IN VARCHAR2)
   IS
      -- Cursor will select the linked account from P_ACCOUNT table whose value is not null.
      CURSOR CR_GET_ACC_LINK_ACC_ID
      IS
         SELECT LINK_ACCOUNT_ID
           FROM P_ACCOUNT
          WHERE HOST_ID = L_HOSTID
            AND ENTITY_ID = L_ENTITYID
            AND CURRENCY_CODE = L_CURRENCYCODE
            AND LINK_ACCOUNT_ID IS NOT NULL;

      R_GET_ACC_LINK_ACC_ID   CR_GET_ACC_LINK_ACC_ID%ROWTYPE;
      VERRORCODE              NUMBER;
      VERRORDESC              S_ERROR_LOG.ERROR_DESC%TYPE;
      VUSERID                 S_ERROR_LOG.USER_ID%TYPE := 'SYSTEM';
      VIPADDRESS              S_ERROR_LOG.IP_ADDRESS%TYPE := 'DB SERVER';
      VSOURCE                 S_ERROR_LOG.SOURCE%TYPE := 'Auto Matching - SP_POPULATE_ACC_LINK_ACC_ID';
      VN_LNK_ACC_DEPTH        NUMBER(2);
   BEGIN
      -- Get the linked account depth details from the parameter value LINKED_ACCOUNT_DEPTH. [default 0]
      VN_LNK_ACC_DEPTH      :=
         TO_NUMBER(PK_APPLICATION.FN_GET_PARAMETER_VALUE(L_ENTITYID,
                                                         'MATCHING',
                                                         'LINKED_ACCOUNT_DEPTH',
                                                         '0'));

      LA_MAT.DELETE; -- refresh pl/sql table

      -- Open the linked account selection cursor and insert into PL/SQL table [la_mat]
      -- When LINKED_ACCOUNT_DEPTH = 0 then directly linked accounts will be stored in the PL/SQL table.
      -- When LINKED_ACCOUNT_DEPTH > 0 then directly linked and indirectly linked to the level
      -- specified will be stored in the PL/SQL table
      OPEN CR_GET_ACC_LINK_ACC_ID;

      IF VN_LNK_ACC_DEPTH = 0
      THEN
         LOOP
            FETCH CR_GET_ACC_LINK_ACC_ID INTO R_GET_ACC_LINK_ACC_ID;

            EXIT WHEN CR_GET_ACC_LINK_ACC_ID%NOTFOUND;

            FOR R_GET_LINKED_TO_ACC_ID IN (    SELECT ACCOUNT_ID
                                                 FROM P_ACCOUNT
                                                WHERE ENTITY_ID = L_ENTITYID
                                           START WITH LINK_ACCOUNT_ID = R_GET_ACC_LINK_ACC_ID.LINK_ACCOUNT_ID
                                           CONNECT BY PRIOR ACCOUNT_ID = LINK_ACCOUNT_ID
                                                  AND PRIOR ENTITY_ID = L_ENTITYID)
            LOOP
               LA_MAT(R_GET_ACC_LINK_ACC_ID.LINK_ACCOUNT_ID)(R_GET_LINKED_TO_ACC_ID.ACCOUNT_ID).EXIST   := 'Y';
            END LOOP;
         END LOOP;
      ELSE
         LOOP
            FETCH CR_GET_ACC_LINK_ACC_ID INTO R_GET_ACC_LINK_ACC_ID;

            EXIT WHEN CR_GET_ACC_LINK_ACC_ID%NOTFOUND;

            FOR R_GET_LINKED_TO_ACC_ID IN (    SELECT ACCOUNT_ID
                                                 FROM P_ACCOUNT
                                                WHERE ENTITY_ID = L_ENTITYID
                                                  AND LEVEL <= VN_LNK_ACC_DEPTH
                                           START WITH LINK_ACCOUNT_ID = R_GET_ACC_LINK_ACC_ID.LINK_ACCOUNT_ID
                                           CONNECT BY PRIOR ACCOUNT_ID = LINK_ACCOUNT_ID
                                                  AND PRIOR ENTITY_ID = L_ENTITYID)
            LOOP
               LA_MAT(R_GET_ACC_LINK_ACC_ID.LINK_ACCOUNT_ID)(R_GET_LINKED_TO_ACC_ID.ACCOUNT_ID).EXIST   := 'Y';
            END LOOP;
         END LOOP;
      END IF;

      CLOSE CR_GET_ACC_LINK_ACC_ID;
   EXCEPTION
      WHEN OTHERS
      THEN
         IF CR_GET_ACC_LINK_ACC_ID%ISOPEN
         THEN
            CLOSE CR_GET_ACC_LINK_ACC_ID;
         END IF;

         VERRORCODE   := SQLCODE;
         VERRORDESC   := SQLERRM;
         SP_ERROR_LOG(L_HOSTID,
                      VUSERID,
                      VIPADDRESS,
                      VSOURCE || ' ERROR in fetching records',
                      VERRORCODE,
                      VERRORDESC);
   END SP_POPULATE_ACC_LINK_ACC_ID;


   -- ----------------------------------------------------------------------------------------------
   -- Identifies whether source and target accounts linked to each other
   -- by searching in list of linked accounts (in PL/SQL table [la_mat]).
   -- ----------------------------------------------------------------------------------------------
   FUNCTION FN_IS_SRC_LINK_TO_TGT_REC(P_HOST_ID          IN P_ACCOUNT.HOST_ID%TYPE,
                                      P_SRC_ACCOUNT_ID   IN P_ACCOUNT.ACCOUNT_ID%TYPE,
                                      P_TGT_ACCOUNT_ID   IN P_ACCOUNT.LINK_ACCOUNT_ID%TYPE)
      RETURN VARCHAR2
   IS
      CUR_VAR      PLS_INTEGER;
      VERRORCODE   NUMBER;
      VERRORDESC   S_ERROR_LOG.ERROR_DESC%TYPE;
      VUSERID      S_ERROR_LOG.USER_ID%TYPE := 'SYSTEM';
      VIPADDRESS   S_ERROR_LOG.IP_ADDRESS%TYPE := 'DB SERVER';
      VSOURCE      S_ERROR_LOG.SOURCE%TYPE := 'Auto Matching - FN_IS_SRC_LINK_TO_TGT_REC';
      VV_ERR_LOC   VARCHAR2(10) := '0';
   BEGIN
      -- check source account linked to target
      BEGIN
         IF (LA_MAT(P_SRC_ACCOUNT_ID)(P_TGT_ACCOUNT_ID).EXIST = 'Y')
         THEN
            RETURN 'Y';
         END IF;
      EXCEPTION
         WHEN NO_DATA_FOUND
         THEN
            NULL;
      END;

      -- check target account linked to source account
      BEGIN
         IF (LA_MAT(P_TGT_ACCOUNT_ID)(P_SRC_ACCOUNT_ID).EXIST = 'Y')
         THEN
            RETURN 'Y';
         END IF;
      EXCEPTION
         WHEN NO_DATA_FOUND
         THEN
            NULL;
      END;

      RETURN 'N';
   EXCEPTION
      WHEN OTHERS
      THEN
         VERRORCODE   := SQLCODE;
         VERRORDESC   := SQLERRM;
         SP_ERROR_LOG(P_HOST_ID,
                      VUSERID,
                      VIPADDRESS,
                      VSOURCE || ' Error for ' || V_ENTITY_ID(CUR_VAR) || '/' || V_CURRENCY_CODE(CUR_VAR) || ' at Location: ' || VV_ERR_LOC,
                      VERRORCODE,
                      VERRORDESC);
   END FN_IS_SRC_LINK_TO_TGT_REC;


   -- ----------------------------------------------------------------------------------------------
   -- Stores the input match quality flag into a PL/SQL table
   -- ----------------------------------------------------------------------------------------------
   PROCEDURE INSERTNEWMATCHQUALITY(P_HOST_ID          IN VARCHAR2,
                                   P_ENTITY_ID        IN VARCHAR2,
                                   P_CURRENCY_CODE       VARCHAR2,
                                   L_POSITION_LEVEL      NUMBER,
                                   P_MASK                NUMBER,
                                   P_VALUE               NUMBER,
                                   P_QUALITY_FLAG        NUMBER)
   IS
      VV_ERR_LOC   VARCHAR2(10) := 0;
      VERRORCODE   NUMBER;
      VERRORDESC   S_ERROR_LOG.ERROR_DESC%TYPE;
      VUSERID      S_ERROR_LOG.USER_ID%TYPE := 'SYSTEM';
      VIPADDRESS   S_ERROR_LOG.IP_ADDRESS%TYPE := 'DB SERVER';
      VSOURCE      S_ERROR_LOG.SOURCE%TYPE := 'Auto Matching - InsertNewMatchQuality';
   BEGIN
      VV_ERR_LOC                                                := '10';
      -- add the mask value to the associative array qm_mat
      QM_MAT(L_POSITION_LEVEL)(P_QUALITY_FLAG).MASK_VALUE       := P_MASK;
      VV_ERR_LOC                                                := '20';
      -- add the computed value to the associative array qm_mat
      QM_MAT(L_POSITION_LEVEL)(P_QUALITY_FLAG).COMPUTED_VALUE   := P_VALUE;
   EXCEPTION
      WHEN OTHERS
      THEN
         VERRORCODE   := SQLCODE;
         VERRORDESC   := SQLERRM;
         SP_ERROR_LOG(P_HOST_ID,
                      VUSERID,
                      VIPADDRESS,
                      VSOURCE || ' Error for ' || P_ENTITY_ID || '/' || P_CURRENCY_CODE || ' at Location: ' || VV_ERR_LOC,
                      VERRORCODE,
                      VERRORDESC);
   END INSERTNEWMATCHQUALITY;


   -- ----------------------------------------------------------------------------------------------
   -- Selects the Amount total value from P_MATCH quality and stores it in PL/SQL table
   -- ----------------------------------------------------------------------------------------------
   PROCEDURE SP_SET_AMOUNT_TOTAL_FLAG(P_HOST_ID IN VARCHAR2, P_ENTITY_ID IN VARCHAR2, P_CURRENCY_CODE VARCHAR2)
   IS
      -- Numeric Variables
      VERRORCODE   NUMBER;
      -- String Variables
      VV_ERR_LOC   VARCHAR2(10) := 0;
      VERRORDESC   S_ERROR_LOG.ERROR_DESC%TYPE;
      VUSERID      S_ERROR_LOG.USER_ID%TYPE := 'SYSTEM';
      VIPADDRESS   S_ERROR_LOG.IP_ADDRESS%TYPE := 'DB SERVER';
      VSOURCE      S_ERROR_LOG.SOURCE%TYPE := 'Auto Matching - sp_set_amount_total_flag';

      -- Cursor Declarations
      -- Cursor fetches the position_level and each quality value for the input currency.
      -- when the input currency for a position do not exists, it will get all currency (*) if exists.
      CURSOR CR_AMOUNT_TOTAL_FLAG
      IS
         SELECT POSITION_LEVEL, NVL(MATCH_QUALITY_A, 'N') A, NVL(MATCH_QUALITY_B, 'N') B,
                NVL(MATCH_QUALITY_C, 'N') C, NVL(MATCH_QUALITY_D, 'N') D, NVL(MATCH_QUALITY_E, 'N') E
           FROM P_MATCH_QUALITY A
          WHERE HOST_ID = P_HOST_ID
            AND ENTITY_ID = P_ENTITY_ID
            AND CURRENCY_CODE = P_CURRENCY_CODE
            AND PARAMETER_ID = 15
         UNION
         SELECT POSITION_LEVEL, NVL(MATCH_QUALITY_A, 'N') A, NVL(MATCH_QUALITY_B, 'N') B,
                NVL(MATCH_QUALITY_C, 'N') C, NVL(MATCH_QUALITY_D, 'N') D, NVL(MATCH_QUALITY_E, 'N') E
           FROM P_MATCH_QUALITY A
          WHERE HOST_ID = P_HOST_ID
            AND ENTITY_ID = P_ENTITY_ID
            AND CURRENCY_CODE = '*'
            AND PARAMETER_ID = 15
            AND POSITION_LEVEL NOT IN (SELECT POSITION_LEVEL
                                         FROM P_MATCH_QUALITY B
                                        WHERE HOST_ID = P_HOST_ID
                                          AND ENTITY_ID = P_ENTITY_ID
                                          AND CURRENCY_CODE = P_CURRENCY_CODE);
   BEGIN
      VV_ERR_LOC   := '10';

      -- open and fetch data from the cursor cr_amount_total_flag
      -- assign it to the v_amttotal associative array
      FOR VR_DATA IN CR_AMOUNT_TOTAL_FLAG
      LOOP
         V_AMTTOTAL(VR_DATA.POSITION_LEVEL)('A').VALUE   := VR_DATA.A;
         V_AMTTOTAL(VR_DATA.POSITION_LEVEL)('B').VALUE   := VR_DATA.B;
         V_AMTTOTAL(VR_DATA.POSITION_LEVEL)('C').VALUE   := VR_DATA.C;
         V_AMTTOTAL(VR_DATA.POSITION_LEVEL)('D').VALUE   := VR_DATA.D;
         V_AMTTOTAL(VR_DATA.POSITION_LEVEL)('E').VALUE   := VR_DATA.E;
      END LOOP;
   EXCEPTION
      WHEN OTHERS
      THEN
         VERRORCODE   := SQLCODE;
         VERRORDESC   := SQLERRM;
         SP_ERROR_LOG(P_HOST_ID,
                      VUSERID,
                      VIPADDRESS,
                      VSOURCE || ' Error for ' || P_ENTITY_ID || '/' || P_CURRENCY_CODE || ' at Location: ' || VV_ERR_LOC,
                      VERRORCODE,
                      VERRORDESC);
   END SP_SET_AMOUNT_TOTAL_FLAG;


   -- ----------------------------------------------------------------------------------------------
   -- Selects the all the match quality parameter values other than amount total value
   -- from P_MATCH quality and stores it in PL/SQL table
   -- ----------------------------------------------------------------------------------------------
   PROCEDURE SP_CREATE_NEW_MATCH_QUALITY(P_HOST_ID IN VARCHAR2, P_ENTITY_ID IN VARCHAR2, P_CURRENCY_CODE VARCHAR2)
   IS
      -- Numeric Variables
      VERRORCODE   NUMBER;
      -- String Variables
      VV_ERR_LOC   VARCHAR2(10) := 0;
      VERRORDESC   S_ERROR_LOG.ERROR_DESC%TYPE;
      VUSERID      S_ERROR_LOG.USER_ID%TYPE := 'SYSTEM';
      VIPADDRESS   S_ERROR_LOG.IP_ADDRESS%TYPE := 'DB SERVER';
      VSOURCE      S_ERROR_LOG.SOURCE%TYPE := 'Auto Matching - sp_create_new_match_quality';

      -- Cursor fetches the position_level and each quality value for the input currency.
      -- When the input currency for a position do not exists, it will get all currency (*) if exists.
      CURSOR CR_MATCHQUALITY_ALL
      IS
           SELECT POSITION_LEVEL,
                  SUM(DECODE(MATCH_QUALITY_A, 'Y', POWER(2, PARAMETER_ID - 1), 0)) VALUE_A,
                  SUM(DECODE(MATCH_QUALITY_A, NULL, 0, POWER(2, PARAMETER_ID - 1))) MASK_A,
                  SUM(DECODE(MATCH_QUALITY_B, 'Y', POWER(2, PARAMETER_ID - 1), 0)) VALUE_B,
                  SUM(DECODE(MATCH_QUALITY_B, NULL, 0, POWER(2, PARAMETER_ID - 1))) MASK_B,
                  SUM(DECODE(MATCH_QUALITY_C, 'Y', POWER(2, PARAMETER_ID - 1), 0)) VALUE_C,
                  SUM(DECODE(MATCH_QUALITY_C, NULL, 0, POWER(2, PARAMETER_ID - 1))) MASK_C,
                  SUM(DECODE(MATCH_QUALITY_D, 'Y', POWER(2, PARAMETER_ID - 1), 0)) VALUE_D,
                  SUM(DECODE(MATCH_QUALITY_D, NULL, 0, POWER(2, PARAMETER_ID - 1))) MASK_D,
                  SUM(DECODE(MATCH_QUALITY_E, 'Y', POWER(2, PARAMETER_ID - 1), 0)) VALUE_E,
                  SUM(DECODE(MATCH_QUALITY_E, NULL, 0, POWER(2, PARAMETER_ID - 1))) MASK_E
             FROM P_MATCH_QUALITY A
            WHERE HOST_ID = P_HOST_ID
              AND ENTITY_ID = P_ENTITY_ID
              AND CURRENCY_CODE = P_CURRENCY_CODE
              AND PARAMETER_ID <= 10
         GROUP BY POSITION_LEVEL
         UNION
           SELECT POSITION_LEVEL,
                  SUM(DECODE(MATCH_QUALITY_A, 'Y', POWER(2, PARAMETER_ID - 1), 0)) VALUE_A,
                  SUM(DECODE(MATCH_QUALITY_A, NULL, 0, POWER(2, PARAMETER_ID - 1))) MASK_A,
                  SUM(DECODE(MATCH_QUALITY_B, 'Y', POWER(2, PARAMETER_ID - 1), 0)) VALUE_B,
                  SUM(DECODE(MATCH_QUALITY_B, NULL, 0, POWER(2, PARAMETER_ID - 1))) MASK_B,
                  SUM(DECODE(MATCH_QUALITY_C, 'Y', POWER(2, PARAMETER_ID - 1), 0)) VALUE_C,
                  SUM(DECODE(MATCH_QUALITY_C, NULL, 0, POWER(2, PARAMETER_ID - 1))) MASK_C,
                  SUM(DECODE(MATCH_QUALITY_D, 'Y', POWER(2, PARAMETER_ID - 1), 0)) VALUE_D,
                  SUM(DECODE(MATCH_QUALITY_D, NULL, 0, POWER(2, PARAMETER_ID - 1))) MASK_D,
                  SUM(DECODE(MATCH_QUALITY_E, 'Y', POWER(2, PARAMETER_ID - 1), 0)) VALUE_E,
                  SUM(DECODE(MATCH_QUALITY_E, NULL, 0, POWER(2, PARAMETER_ID - 1))) MASK_E
             FROM P_MATCH_QUALITY A
            WHERE HOST_ID = P_HOST_ID
              AND ENTITY_ID = P_ENTITY_ID
              AND CURRENCY_CODE = '*'
              AND PARAMETER_ID <= 10
              AND POSITION_LEVEL NOT IN (SELECT POSITION_LEVEL
                                           FROM P_MATCH_QUALITY B
                                          WHERE HOST_ID = P_HOST_ID
                                            AND ENTITY_ID = P_ENTITY_ID
                                            AND CURRENCY_CODE = P_CURRENCY_CODE)
         GROUP BY POSITION_LEVEL;
   BEGIN
      VV_ERR_LOC   := '10';

      -- open the match quality cursor and insert into associative arrays
      -- using function call insertnewmatchquality
      FOR VR_DATA IN CR_MATCHQUALITY_ALL
      LOOP
         VV_ERR_LOC   := '20';
         INSERTNEWMATCHQUALITY(P_HOST_ID,
                               P_ENTITY_ID,
                               P_CURRENCY_CODE,
                               VR_DATA.POSITION_LEVEL,
                               VR_DATA.MASK_A,
                               VR_DATA.VALUE_A,
                               5);

         VV_ERR_LOC   := '30';
         INSERTNEWMATCHQUALITY(P_HOST_ID,
                               P_ENTITY_ID,
                               P_CURRENCY_CODE,
                               VR_DATA.POSITION_LEVEL,
                               VR_DATA.MASK_B,
                               VR_DATA.VALUE_B,
                               4);

         VV_ERR_LOC   := '40';
         INSERTNEWMATCHQUALITY(P_HOST_ID,
                               P_ENTITY_ID,
                               P_CURRENCY_CODE,
                               VR_DATA.POSITION_LEVEL,
                               VR_DATA.MASK_C,
                               VR_DATA.VALUE_C,
                               3);

         VV_ERR_LOC   := '50';
         INSERTNEWMATCHQUALITY(P_HOST_ID,
                               P_ENTITY_ID,
                               P_CURRENCY_CODE,
                               VR_DATA.POSITION_LEVEL,
                               VR_DATA.MASK_D,
                               VR_DATA.VALUE_D,
                               2);

         VV_ERR_LOC   := '60';
         INSERTNEWMATCHQUALITY(P_HOST_ID,
                               P_ENTITY_ID,
                               P_CURRENCY_CODE,
                               VR_DATA.POSITION_LEVEL,
                               VR_DATA.MASK_E,
                               VR_DATA.VALUE_E,
                               1);
      END LOOP;
   EXCEPTION
      WHEN OTHERS
      THEN
         VERRORCODE   := SQLCODE;
         VERRORDESC   := SQLERRM;
         SP_ERROR_LOG(P_HOST_ID,
                      VUSERID,
                      VIPADDRESS,
                      VSOURCE || ' Error for ' || P_ENTITY_ID || '/' || P_CURRENCY_CODE || ' at Location: ' || VV_ERR_LOC,
                      VERRORCODE,
                      VERRORDESC);
   END SP_CREATE_NEW_MATCH_QUALITY;


   -- ----------------------------------------------------------------------------------------------
   -- Return the match action for the input position and quality
   -- ----------------------------------------------------------------------------------------------
   PROCEDURE SP_SET_MATCH_ACTION(P_HOST_ID IN VARCHAR2, P_ENTITY_ID IN VARCHAR2, P_CURRENCY_CODE IN VARCHAR2)
   IS
      -- Numeric variables
      VERRORCODE   NUMBER;

      -- String variables
      VV_ERR_LOC   VARCHAR2(10) := 0;
      VERRORDESC   S_ERROR_LOG.ERROR_DESC%TYPE;
      VUSERID      S_ERROR_LOG.USER_ID%TYPE := 'SYSTEM';
      VIPADDRESS   S_ERROR_LOG.IP_ADDRESS%TYPE := 'DB SERVER';
      VSOURCE      S_ERROR_LOG.SOURCE%TYPE := 'Auto Matching - FN_GET_MATCH_ACTION';

      -- Cursor Declarations
      -- Cursor select the match action for all the match qualities
      -- for the input currency and store it in a PL/SQL table [v_rec_action]
      -- when match action does not exist the store the all currency data (*) if exists.
      CURSOR CR_MATCH_ACTION
      IS
         SELECT POSITION_LEVEL POS_LEVEL, MATCH_ACTION_A, MATCH_ACTION_B,
                MATCH_ACTION_C, MATCH_ACTION_D, MATCH_ACTION_E
           FROM P_MATCH_ACTION
          WHERE HOST_ID = P_HOST_ID
            AND ENTITY_ID = P_ENTITY_ID
            AND CURRENCY_CODE = P_CURRENCY_CODE
         UNION
         SELECT POSITION_LEVEL POS_LEVEL, MATCH_ACTION_A, MATCH_ACTION_B,
                MATCH_ACTION_C, MATCH_ACTION_D, MATCH_ACTION_E
           FROM P_MATCH_ACTION
          WHERE HOST_ID = P_HOST_ID
            AND ENTITY_ID = P_ENTITY_ID
            AND CURRENCY_CODE = '*'
            AND POSITION_LEVEL NOT IN (SELECT POSITION_LEVEL
                                         FROM P_MATCH_ACTION
                                        WHERE HOST_ID = P_HOST_ID
                                          AND ENTITY_ID = P_ENTITY_ID
                                          AND CURRENCY_CODE = P_CURRENCY_CODE);
   BEGIN
      VV_ERR_LOC   := '10';

      -- open and fetch data for the match action cursor
      -- and load it into the match action associative arrays
      FOR R_MATCH_ACTION IN CR_MATCH_ACTION
      LOOP
         CASE R_MATCH_ACTION.POS_LEVEL
            WHEN 1
            THEN
               V_REC_ACTION(1)('A')   := R_MATCH_ACTION.MATCH_ACTION_A;
               V_REC_ACTION(1)('B')   := R_MATCH_ACTION.MATCH_ACTION_B;
               V_REC_ACTION(1)('C')   := R_MATCH_ACTION.MATCH_ACTION_C;
               V_REC_ACTION(1)('D')   := R_MATCH_ACTION.MATCH_ACTION_D;
               V_REC_ACTION(1)('E')   := R_MATCH_ACTION.MATCH_ACTION_E;
            WHEN 2
            THEN
               V_REC_ACTION(2)('A')   := R_MATCH_ACTION.MATCH_ACTION_A;
               V_REC_ACTION(2)('B')   := R_MATCH_ACTION.MATCH_ACTION_B;
               V_REC_ACTION(2)('C')   := R_MATCH_ACTION.MATCH_ACTION_C;
               V_REC_ACTION(2)('D')   := R_MATCH_ACTION.MATCH_ACTION_D;
               V_REC_ACTION(2)('E')   := R_MATCH_ACTION.MATCH_ACTION_E;
            WHEN 3
            THEN
               V_REC_ACTION(3)('A')   := R_MATCH_ACTION.MATCH_ACTION_A;
               V_REC_ACTION(3)('B')   := R_MATCH_ACTION.MATCH_ACTION_B;
               V_REC_ACTION(3)('C')   := R_MATCH_ACTION.MATCH_ACTION_C;
               V_REC_ACTION(3)('D')   := R_MATCH_ACTION.MATCH_ACTION_D;
               V_REC_ACTION(3)('E')   := R_MATCH_ACTION.MATCH_ACTION_E;
            WHEN 4
            THEN
               V_REC_ACTION(4)('A')   := R_MATCH_ACTION.MATCH_ACTION_A;
               V_REC_ACTION(4)('B')   := R_MATCH_ACTION.MATCH_ACTION_B;
               V_REC_ACTION(4)('C')   := R_MATCH_ACTION.MATCH_ACTION_C;
               V_REC_ACTION(4)('D')   := R_MATCH_ACTION.MATCH_ACTION_D;
               V_REC_ACTION(4)('E')   := R_MATCH_ACTION.MATCH_ACTION_E;
            WHEN 5
            THEN
               V_REC_ACTION(5)('A')   := R_MATCH_ACTION.MATCH_ACTION_A;
               V_REC_ACTION(5)('B')   := R_MATCH_ACTION.MATCH_ACTION_B;
               V_REC_ACTION(5)('C')   := R_MATCH_ACTION.MATCH_ACTION_C;
               V_REC_ACTION(5)('D')   := R_MATCH_ACTION.MATCH_ACTION_D;
               V_REC_ACTION(5)('E')   := R_MATCH_ACTION.MATCH_ACTION_E;
            WHEN 6
            THEN
               V_REC_ACTION(6)('A')   := R_MATCH_ACTION.MATCH_ACTION_A;
               V_REC_ACTION(6)('B')   := R_MATCH_ACTION.MATCH_ACTION_B;
               V_REC_ACTION(6)('C')   := R_MATCH_ACTION.MATCH_ACTION_C;
               V_REC_ACTION(6)('D')   := R_MATCH_ACTION.MATCH_ACTION_D;
               V_REC_ACTION(6)('E')   := R_MATCH_ACTION.MATCH_ACTION_E;
            WHEN 7
            THEN
               V_REC_ACTION(7)('A')   := R_MATCH_ACTION.MATCH_ACTION_A;
               V_REC_ACTION(7)('B')   := R_MATCH_ACTION.MATCH_ACTION_B;
               V_REC_ACTION(7)('C')   := R_MATCH_ACTION.MATCH_ACTION_C;
               V_REC_ACTION(7)('D')   := R_MATCH_ACTION.MATCH_ACTION_D;
               V_REC_ACTION(7)('E')   := R_MATCH_ACTION.MATCH_ACTION_E;
            WHEN 8
            THEN
               V_REC_ACTION(8)('A')   := R_MATCH_ACTION.MATCH_ACTION_A;
               V_REC_ACTION(8)('B')   := R_MATCH_ACTION.MATCH_ACTION_B;
               V_REC_ACTION(8)('C')   := R_MATCH_ACTION.MATCH_ACTION_C;
               V_REC_ACTION(8)('D')   := R_MATCH_ACTION.MATCH_ACTION_D;
               V_REC_ACTION(8)('E')   := R_MATCH_ACTION.MATCH_ACTION_E;
            WHEN 9
            THEN
               V_REC_ACTION(9)('A')   := R_MATCH_ACTION.MATCH_ACTION_A;
               V_REC_ACTION(9)('B')   := R_MATCH_ACTION.MATCH_ACTION_B;
               V_REC_ACTION(9)('C')   := R_MATCH_ACTION.MATCH_ACTION_C;
               V_REC_ACTION(9)('D')   := R_MATCH_ACTION.MATCH_ACTION_D;
               V_REC_ACTION(9)('E')   := R_MATCH_ACTION.MATCH_ACTION_E;
            ELSE
               NULL;
         END CASE;
      END LOOP;
   EXCEPTION
      WHEN NO_DATA_FOUND
      THEN
         NULL;
      WHEN OTHERS
      THEN
         VERRORCODE   := SQLCODE;
         VERRORDESC   := SQLERRM;
         SP_ERROR_LOG(P_HOST_ID,
                      VUSERID,
                      VIPADDRESS,
                      VSOURCE || ' Error for ' || P_ENTITY_ID || '/' || P_CURRENCY_CODE || ' at Location: ' || VV_ERR_LOC,
                      VERRORCODE,
                      VERRORDESC);
   END SP_SET_MATCH_ACTION;


   -- ----------------------------------------------------------------------------------------------
   -- Return the match action for the input position and quality id from match action
   -- associative arrays
   -- ----------------------------------------------------------------------------------------------
   FUNCTION FN_GET_MATCH_ACTION(P_POS_LEVEL IN NUMBER, P_QUALITY_ID IN VARCHAR2)
      RETURN VARCHAR2
   IS
   BEGIN
      RETURN V_REC_ACTION(P_POS_LEVEL)(P_QUALITY_ID);
   END FN_GET_MATCH_ACTION;

   -- ----------------------------------------------------------------------------------------------
   -- Clean up temporary table P_MVT_MATCH_TEMP used for propagation
   -- ----------------------------------------------------------------------------------------------
   PROCEDURE SP_CLEAN_UP_P_MVT_MATCH_TEMP
   IS
   BEGIN
      DELETE P_MVT_MATCH_TEMP;
   END;

   -- ----------------------------------------------------------------------------------------------
   -- Used to initiate matching process from the scheduler.
   -- So it will interface to outside subprograms.
   -- ----------------------------------------------------------------------------------------------
   PROCEDURE MATCHING_PROC_DRIVER_PARALLEL(L_HOSTID             VARCHAR2,
                                           L_ENTITYID           VARCHAR2,
                                           L_CURRENCYCODE       VARCHAR2,
                                           L_VALUEDATE          DATE,
                                           L_RESULT         OUT VARCHAR2)
   IS
      -- Date variables
      VD_MATCH_PROCESS_DATE     DATE;
      -- Numeric variables
      L_TOLERANCE               NUMBER(7);
      L_CASH_FILTER_THRSH       NUMBER(20, 2);
      L_SECURITY_FILTER_THRSH   NUMBER(20, 2);
      L_EXCHANGERATE_FORMAT     NUMBER;
      L_EXCHANGERATE            NUMBER(20, 6) := 1;
      V_ROWUPDATE               INTEGER := 0;
      VERRORCODE                NUMBER;
      -- String variables
      L_AVPASS                  VARCHAR2(1) := 'N';
      L_DOMESTICCURRENCY        S_ENTITY.DOMESTIC_CURRENCY%TYPE;
      VV_ERR_LOC                VARCHAR2(10) := 0;
      VERRORDESC                S_ERROR_LOG.ERROR_DESC%TYPE;
      VUSERID                   S_ERROR_LOG.USER_ID%TYPE := 'SYSTEM';
      VIPADDRESS                S_ERROR_LOG.IP_ADDRESS%TYPE := 'DB SERVER';
      VSOURCE                   S_ERROR_LOG.SOURCE%TYPE := 'Auto Matching - Matching_Proc_Driver_Parallel';
   BEGIN
      -- Clean up p_mvt_match_temp table
      SP_CLEAN_UP_P_MVT_MATCH_TEMP;

      GV_GLOBAL_VAR_SYS_DATE   := L_VALUEDATE;
      GN_WARNING_MSG           := 0;

      -- get the linked accounts and store it in associative arrays
      -- using function call sp_populate_acc_link_acc_id
      SP_POPULATE_ACC_LINK_ACC_ID(L_HOSTID, L_ENTITYID, L_CURRENCYCODE);

      VV_ERR_LOC               := '10';
      -- get the match quality definitions and store it in associative arrays
      -- using function call sp_create_new_match_quality
      SP_CREATE_NEW_MATCH_QUALITY(L_HOSTID, L_ENTITYID, L_CURRENCYCODE);

      VV_ERR_LOC               := '20';
      -- get the match action definitions and store it in associative arrays
      -- using function call sp_set_match_action
      SP_SET_MATCH_ACTION(L_HOSTID, L_ENTITYID, L_CURRENCYCODE);
      -- get the amount total flag definitions and store it in associative arrays
      -- using function call sp_set_amount_total_flag
      SP_SET_AMOUNT_TOTAL_FLAG(L_HOSTID, L_ENTITYID, L_CURRENCYCODE);
      L_RESULT                 := 0;

      VV_ERR_LOC               := '30';
      -- Call function for update of the p_match_driver table status
      PK_MATCHING_PROCESS.SP_UPDATE_LAST_STARTED(L_HOSTID,
                                                 L_ENTITYID,
                                                 L_CURRENCYCODE,
                                                 V_ROWUPDATE);

      VV_ERR_LOC               := '40';

      -- when update operation is succeeded
      IF (V_ROWUPDATE > 0)
      THEN
         BEGIN
            VV_ERR_LOC   := '50';

            -- get the cash and securities filer, domestic currency and exchange rate format
            -- for input entity
            SELECT CASH_FILTER, SECURITIES_FILTER, DOMESTIC_CURRENCY,
                   EXCHANGE_RATE_FORMAT
              INTO L_CASH_FILTER_THRSH, L_SECURITY_FILTER_THRSH, L_DOMESTICCURRENCY,
                   L_EXCHANGERATE_FORMAT
              FROM S_ENTITY
             WHERE HOST_ID = L_HOSTID
               AND ENTITY_ID = L_ENTITYID;
         EXCEPTION
            WHEN OTHERS
            THEN
               VV_ERR_LOC   := '60';

               -- get the cash and securities filer, domestic currency and exchange rate format
               -- for all entity
               SELECT CASH_FILTER, SECURITIES_FILTER, DOMESTIC_CURRENCY,
                      EXCHANGE_RATE_FORMAT
                 INTO L_CASH_FILTER_THRSH, L_SECURITY_FILTER_THRSH, L_DOMESTICCURRENCY,
                      L_EXCHANGERATE_FORMAT
                 FROM S_ENTITY
                WHERE HOST_ID = L_HOSTID
                  AND ENTITY_ID = '*';
         END;

         VV_ERR_LOC                := '70';

         -- select tolerance value for the input currency
         BEGIN
            SELECT NVL(TOLERANCE, 0)
              INTO L_TOLERANCE
              FROM S_CURRENCY
             WHERE HOST_ID = L_HOSTID
               AND ENTITY_ID = L_ENTITYID
               AND CURRENCY_CODE = L_CURRENCYCODE;
         EXCEPTION
            WHEN OTHERS
            THEN
               VV_ERR_LOC   := '80';

               -- select tolerance value for the all currency
               SELECT NVL(TOLERANCE, 0)
                 INTO L_TOLERANCE
                 FROM S_CURRENCY
                WHERE HOST_ID = L_HOSTID
                  AND ENTITY_ID = L_ENTITYID
                  AND CURRENCY_CODE = '*';
         END;

         VV_ERR_LOC                := '90';

         -- get the exchange rate when input currency is not a domestic currency
         BEGIN
            IF (L_DOMESTICCURRENCY != L_CURRENCYCODE)
            THEN
               BEGIN
                  SELECT EXCHANGE_RATE
                    INTO L_EXCHANGERATE
                    FROM S_CURRENCY_EXCHANGE_RATE A
                   WHERE A.HOST_ID = L_HOSTID
                     AND A.ENTITY_ID = L_ENTITYID
                     AND A.CURRENCY_CODE = L_CURRENCYCODE
                     AND A.EXCHANGE_RATE_DATE = (SELECT MAX(EXCHANGE_RATE_DATE)
                                                   FROM S_CURRENCY_EXCHANGE_RATE B
                                                  WHERE B.HOST_ID = A.HOST_ID
                                                    AND B.ENTITY_ID = A.ENTITY_ID
                                                    AND B.CURRENCY_CODE = A.CURRENCY_CODE);
               EXCEPTION
                  WHEN NO_DATA_FOUND
                  THEN
                     VV_ERR_LOC       := '100';
                     L_EXCHANGERATE   := 1;
               END;
            END IF;
         EXCEPTION
            WHEN OTHERS
            THEN
               RAISE_APPLICATION_ERROR(-20900, SQLERRM);
         END;

         -- set the case and security filter threshold
         VV_ERR_LOC                := '120';
         L_CASH_FILTER_THRSH       := NVL(L_CASH_FILTER_THRSH, 0);
         L_SECURITY_FILTER_THRSH   := NVL(L_SECURITY_FILTER_THRSH, 0);

         VV_ERR_LOC                := '130';
         -- remove any entries in p_b_target_movements for the input currency
         SP_REMOVE_P_B_TARGETS(L_HOSTID,
                               L_ENTITYID,
                               L_CURRENCYCODE,
                               'Y',
                               0,
                               'B' -- Beginning of current currency
                                  );
         COMMIT;

         VV_ERR_LOC                := '140';
         L_AVPASS                  := 'Y';
         -- process date used to select source movement for input processing currency.
         -- movements <= this date will be selected as source.
         VD_MATCH_PROCESS_DATE     := GLOBAL_VAR.SYS_DATE;

         -- Mantis 5940: Matching process: Add a parameter to control whether sources revert to initial predict status
         GV_REVERT_PRED_STATUS      :=
            PK_APPLICATION.FN_GET_PARAMETER_VALUE(L_ENTITYID,
                                                  'MATCHING',
                                                  'REVERT_PREDICT_STATUS',
                                                  'N');

         -- stored procedure that makes matches for current input currency
         SP_MATCHING_PROCEDURE(L_HOSTID,
                               L_ENTITYID,
                               L_CURRENCYCODE,
                               L_CASH_FILTER_THRSH,
                               L_SECURITY_FILTER_THRSH,
                               L_TOLERANCE,
                               L_EXCHANGERATE,
                               L_EXCHANGERATE_FORMAT,
                               VD_MATCH_PROCESS_DATE,
                               L_RESULT);

         VV_ERR_LOC                := '170';
         -- Clearing the locked and processing movements and updating status as closed
         SP_REMOVE_P_B_TARGETS(L_HOSTID,
                               L_ENTITYID,
                               L_CURRENCYCODE,
                               'Y',
                               0,
                               'E' -- End of current currency
                                  );
         L_RESULT                  := 0;
      END IF; -- IF(v_RowUpdate>0) THEN

      -- Clean up p_mvt_match_temp table
      SP_CLEAN_UP_P_MVT_MATCH_TEMP;
   EXCEPTION
      WHEN OTHERS
      THEN
         VERRORCODE   := SQLCODE;
         VERRORDESC   := SQLERRM;
         SP_ERROR_LOG(L_HOSTID,
                      VUSERID,
                      VIPADDRESS,
                      VSOURCE || ' Error for ' || L_ENTITYID || '/' || L_CURRENCYCODE || ' at Location: ' || VV_ERR_LOC,
                      VERRORCODE,
                      VERRORDESC);
   END MATCHING_PROC_DRIVER_PARALLEL;


   -- ----------------------------------------------------------------------------------------------
   -- Returns the equivalent string qualityflag to the numeric input parameter
   -- ----------------------------------------------------------------------------------------------
   FUNCTION FN_GET_L_INT_QTY_VAR(P_L_INT_QTY_VAR IN NUMBER)
      RETURN VARCHAR2
   IS
   BEGIN
      RETURN V_QUALITY_VARIABLE(P_L_INT_QTY_VAR);
   END FN_GET_L_INT_QTY_VAR;


   -- ----------------------------------------------------------------------------------------------
   -- Return the internal quality from p_match table for the input match_id and position level
   -- FUNCTION IS NOT BEING USED ANY MORE!
   -- ----------------------------------------------------------------------------------------------
   FUNCTION FN_GET_LINTQTYMATCH(P_POSITION_LEVEL    IN NUMBER,
                                P_HOST_ID           IN VARCHAR2,
                                P_ENTITY_ID         IN VARCHAR2,
                                P_MATCH_ID          IN NUMBER,
                                P_CURRENCY_CODE     IN VARCHAR2,
                                P_SRC_MOVEMENT_ID   IN NUMBER,
                                P_TGT_MOVEMENT_ID   IN NUMBER)
      RETURN VARCHAR2
   IS
      -- Numeric Variables
      VERRORCODE     NUMBER;
      -- String Variables
      LINTQTYMATCH   VARCHAR2(1);
      VV_ERR_LOC     VARCHAR2(10) := 0;
      VERRORDESC     S_ERROR_LOG.ERROR_DESC%TYPE;
      VUSERID        S_ERROR_LOG.USER_ID%TYPE := 'SYSTEM';
      VIPADDRESS     S_ERROR_LOG.IP_ADDRESS%TYPE := 'DB SERVER';
      VSOURCE        S_ERROR_LOG.SOURCE%TYPE := 'Auto Matching - FN_GET_lIntqtyMatch';
   BEGIN
      VV_ERR_LOC   := '10';

      -- select the previously matched quality of the input match id for the input position level
      SELECT NVL(DECODE(P_POSITION_LEVEL,  1, INTERNAL_QUALITY1,  2, INTERNAL_QUALITY2,  3, INTERNAL_QUALITY3,  4, INTERNAL_QUALITY4,  5, INTERNAL_QUALITY5,  6, INTERNAL_QUALITY6,  7, INTERNAL_QUALITY7,  8, INTERNAL_QUALITY8,  9, INTERNAL_QUALITY9), 'U')
        INTO LINTQTYMATCH
        FROM P_MATCH
       WHERE HOST_ID = P_HOST_ID
         AND ENTITY_ID = P_ENTITY_ID
         AND MATCH_ID = P_MATCH_ID
         AND CURRENCY_CODE = P_CURRENCY_CODE;

      VV_ERR_LOC   := '110';
      RETURN LINTQTYMATCH;
   EXCEPTION
      WHEN OTHERS
      THEN
         VERRORCODE   := SQLCODE;
         VERRORDESC   := SQLERRM;
         SP_ERROR_LOG(
            P_HOST_ID,
            VUSERID,
            VIPADDRESS,
               VSOURCE
            || ' Error for '
            || P_ENTITY_ID
            || '/'
            || P_CURRENCY_CODE
            || '/'
            || P_SRC_MOVEMENT_ID
            || '/'
            || P_TGT_MOVEMENT_ID
            || ' at Location: '
            || VV_ERR_LOC,
            VERRORCODE,
            VERRORDESC);
   END FN_GET_LINTQTYMATCH;


   -- ----------------------------------------------------------------------------------------------
   -- Update the book code when any of the movements in the match has null book code
   -- with book code in other movements
   -- ----------------------------------------------------------------------------------------------
   PROCEDURE SP_UPDATE_BOOKCODE(P_HOST_ID           IN VARCHAR2,
                                P_ENTITY_ID         IN VARCHAR2,
                                P_CURRENCY_CODE     IN VARCHAR2,
                                P_MATCH_ID          IN NUMBER,
                                P_SRC_MOVEMENT_ID   IN NUMBER)
   IS
      -- Numeric Variables
      VERRORCODE   NUMBER;
      -- String Variables
      VV_ERR_LOC   VARCHAR2(10) := 0;
      VERRORDESC   S_ERROR_LOG.ERROR_DESC%TYPE;
      VUSERID      S_ERROR_LOG.USER_ID%TYPE := 'SYSTEM';
      VIPADDRESS   S_ERROR_LOG.IP_ADDRESS%TYPE := 'DB SERVER';
      VSOURCE      S_ERROR_LOG.SOURCE%TYPE := 'Auto Matching - SP_UPDATE_BOOKCODE';

      -- Cursor Declarations
      -- Get the bookcode and position level from movements that has book code
      -- in the current match.
      CURSOR BOOKCODE_CUR
      IS
           SELECT BOOKCODE, POS_LEVEL
             FROM P_B_TARGET_MOVEMENTS
            WHERE HOST_ID = P_HOST_ID
              AND ENTITY_ID = P_ENTITY_ID
              AND CURRENCY_CODE = P_CURRENCY_CODE
              AND MATCH_ID = P_MATCH_ID
              AND BOOKCODE IS NOT NULL
         ORDER BY POS_LEVEL DESC;
   BEGIN
      VV_ERR_LOC   := '10';

      -- Open and loop the bookcode cursor
      FOR BOOKCODE_REC IN BOOKCODE_CUR
      LOOP
         BEGIN
            VV_ERR_LOC   := '31';

            -- update the bookcode in p_movement
            UPDATE P_MOVEMENT
               SET BOOKCODE = BOOKCODE_REC.BOOKCODE, UPDATE_DATE = GLOBAL_VAR.SYS_DATE, UPDATE_USER = 'SYSTEM'
             WHERE HOST_ID = P_HOST_ID
               AND ENTITY_ID = P_ENTITY_ID
               AND MOVEMENT_ID = P_SRC_MOVEMENT_ID;

            VV_ERR_LOC   := '41';
            EXIT;
         EXCEPTION
            WHEN OTHERS
            THEN
               VERRORCODE   := SQLCODE;
               VERRORDESC   := SQLERRM;
               SP_ERROR_LOG(
                  P_HOST_ID,
                  VUSERID,
                  VIPADDRESS,
                  VSOURCE || ' Error for ' || P_ENTITY_ID || '/' || P_CURRENCY_CODE || '/' || P_SRC_MOVEMENT_ID || ' at Location: ' || VV_ERR_LOC,
                  VERRORCODE,
                  VERRORDESC);
         END;
      END LOOP;
   EXCEPTION
      WHEN OTHERS
      THEN
         VERRORCODE   := SQLCODE;
         VERRORDESC   := SQLERRM;
         SP_ERROR_LOG(
            P_HOST_ID,
            VUSERID,
            VIPADDRESS,
            VSOURCE || ' Error for ' || P_ENTITY_ID || '/' || P_CURRENCY_CODE || '/' || P_SRC_MOVEMENT_ID || ' at Location: ' || VV_ERR_LOC,
            VERRORCODE,
            VERRORDESC);
   END SP_UPDATE_BOOKCODE;


   -- ----------------------------------------------------------------------------------------------
   -- Identify the quality of the target movement with various input matching parameters
   -- of source and target movements
   -- ----------------------------------------------------------------------------------------------
   FUNCTION FN_UPDATE_P_B_MATQUAL_POSLEVEL(P_SRC_HOST_ID             IN VARCHAR2,
                                           P_SRC_ENTITY_ID           IN VARCHAR2,
                                           P_SRC_CURRENCY_CODE       IN VARCHAR2,
                                           P_SRC_VALUE_DATE          IN DATE,
                                           P_TGT_VALUE_DATE          IN DATE,
                                           P_SRC_AMOUNT              IN NUMBER,
                                           P_TGT_AMOUNT              IN NUMBER,
                                           P_TOLERANCE               IN NUMBER,
                                           P_SRC_ACCOUNT_ID          IN VARCHAR2,
                                           P_TGT_ACCOUNT_ID          IN VARCHAR2,
                                           P_SRC_COUNTERPARTY_ID     IN VARCHAR2,
                                           P_TGT_COUNTERPARTY_ID     IN VARCHAR2,
                                           P_SRC_BENEFICIARY_ID      IN VARCHAR2,
                                           P_TGT_BENEFICIARY_ID      IN VARCHAR2,
                                           P_SRC_CUSTODIAN_ID        IN VARCHAR2,
                                           P_TGT_CUSTODIAN_ID        IN VARCHAR2,
                                           P_SRC_BOOKCODE            IN VARCHAR2,
                                           P_TGT_BOOKCODE            IN VARCHAR2,
                                           P_HIGHESTPOSLEVEL_LEVEL   IN NUMBER,
                                           P_SRC_MOVEMENT_ID         IN NUMBER,
                                           P_TGT_MOVEMENT_ID         IN NUMBER,
                                           P_TGT_OPEN                IN VARCHAR2,
                                           P_SRC_MATCHINGPARTY       IN VARCHAR2,
                                           P_TGT_MATCHINGPARTY       IN VARCHAR2,
                                           P_TGT_POS_LEVEL           IN NUMBER,
                                           P_POS_THRESHOLD           IN NUMBER)
      RETURN NUMBER
   IS
      -- Numeric Variables
      L_TM_QUALITYFLAG    NUMBER := 0;
      VERRORCODE          NUMBER;
      V_RETURN_VAL        NUMBER := 0;
      -- String Variables
      VV_ERR_LOC          VARCHAR2(10) := 0;
      VERRORDESC          S_ERROR_LOG.ERROR_DESC%TYPE;
      VUSERID             S_ERROR_LOG.USER_ID%TYPE := 'SYSTEM';
      VIPADDRESS          S_ERROR_LOG.IP_ADDRESS%TYPE := 'DB SERVER';
      VSOURCE             S_ERROR_LOG.SOURCE%TYPE := 'Auto Matching - FN_UPDATE_P_B_MATQUAL_POSLEVEL';
      V_ACC_LINK_ACC_ID   CHAR(1) := 'N';
      VN_REC_COUNT        PLS_INTEGER := 0;
   BEGIN
      VV_ERR_LOC         := '10';
      L_TM_QUALITYFLAG   := 0;
      VV_ERR_LOC         := '20';

      -- check the existence of cross reference from cross reference table
      -- for the supplied target and source movement
      -- src movements and target movements need to be different. ALB
      IF P_SRC_MOVEMENT_ID != P_TGT_MOVEMENT_ID
      THEN
         SELECT COUNT(1)
           INTO VN_REC_COUNT
           FROM (SELECT CR_O.CROSS_REFERENCE
                   FROM P_REFERENCE_XREF CR_O
                  WHERE CR_O.MOVEMENT_ID = P_SRC_MOVEMENT_ID
                    AND CR_O.ENTITY_ID = P_SRC_ENTITY_ID
                    AND CR_O.CURRENCY_CODE = P_SRC_CURRENCY_CODE
                 INTERSECT
                 SELECT CR_I.CROSS_REFERENCE
                   FROM P_REFERENCE_XREF CR_I
                  WHERE CR_I.MOVEMENT_ID = P_TGT_MOVEMENT_ID
                    AND CR_I.ENTITY_ID = P_SRC_ENTITY_ID
                    AND CR_I.CURRENCY_CODE = P_SRC_CURRENCY_CODE);
      END IF;

      IF VN_REC_COUNT > 0
      THEN
         VV_ERR_LOC         := '30';
         L_TM_QUALITYFLAG   := 1;
      ELSE
         VV_ERR_LOC         := '40';
         L_TM_QUALITYFLAG   := L_TM_QUALITYFLAG;
      END IF;

      VV_ERR_LOC         := '50';

      -- value date validations
      IF (P_SRC_VALUE_DATE = P_TGT_VALUE_DATE)
      THEN
         L_TM_QUALITYFLAG   := L_TM_QUALITYFLAG + 2;
      ELSIF P_TGT_OPEN = 'Y'
      THEN
         L_TM_QUALITYFLAG   := L_TM_QUALITYFLAG + 2;
      END IF;

      --    END;
      VV_ERR_LOC         := '70';

      -- amount validations
      IF ((P_SRC_AMOUNT > 0
       AND P_TGT_AMOUNT > 0)
       OR (P_SRC_AMOUNT < 0
       AND P_TGT_AMOUNT < 0))
      THEN
         IF (ABS(NVL(P_SRC_AMOUNT, 0) - NVL(P_TGT_AMOUNT, 0)) <= P_TOLERANCE)
         THEN
            L_TM_QUALITYFLAG   := L_TM_QUALITYFLAG + 4;
         END IF;
      END IF;

      VV_ERR_LOC         := '90';

      -- account or linked account validations
      CASE
         WHEN P_SRC_ACCOUNT_ID = P_TGT_ACCOUNT_ID
         THEN
            L_TM_QUALITYFLAG   := L_TM_QUALITYFLAG + 8;
         WHEN P_SRC_ACCOUNT_ID != P_TGT_ACCOUNT_ID
         THEN
            IF P_HIGHESTPOSLEVEL_LEVEL > P_POS_THRESHOLD
           AND P_TGT_POS_LEVEL > P_POS_THRESHOLD
            THEN
               L_TM_QUALITYFLAG   := L_TM_QUALITYFLAG;
            ELSE
               V_ACC_LINK_ACC_ID   := FN_IS_SRC_LINK_TO_TGT_REC(P_SRC_HOST_ID, P_SRC_ACCOUNT_ID, P_TGT_ACCOUNT_ID);
            END IF;

            IF V_ACC_LINK_ACC_ID = 'Y'
            THEN
               L_TM_QUALITYFLAG   := L_TM_QUALITYFLAG + 8;
            END IF;
         ELSE
            VV_ERR_LOC         := '130';
            L_TM_QUALITYFLAG   := L_TM_QUALITYFLAG;
      END CASE;

      VV_ERR_LOC         := '140';

      -- counterparty id validations
      IF P_SRC_COUNTERPARTY_ID = P_TGT_COUNTERPARTY_ID
      THEN
         L_TM_QUALITYFLAG   := L_TM_QUALITYFLAG + 16;
      END IF;

      VV_ERR_LOC         := '150';

      -- beneficiary id validations
      IF P_SRC_BENEFICIARY_ID = P_TGT_BENEFICIARY_ID
      THEN
         L_TM_QUALITYFLAG   := L_TM_QUALITYFLAG + 32;
      END IF;

      VV_ERR_LOC         := '160';

      -- custodian id validations
      IF P_SRC_CUSTODIAN_ID = P_TGT_CUSTODIAN_ID
      THEN
         L_TM_QUALITYFLAG   := L_TM_QUALITYFLAG + 64;
      END IF;

      VV_ERR_LOC         := '170';

      -- book code validations
      IF P_SRC_BOOKCODE = P_TGT_BOOKCODE
      THEN
         L_TM_QUALITYFLAG   := L_TM_QUALITYFLAG + 128;
      END IF;

      VV_ERR_LOC         := '180';

      -- matching party validations
      IF P_SRC_MATCHINGPARTY = P_TGT_MATCHINGPARTY
      THEN
         L_TM_QUALITYFLAG   := L_TM_QUALITYFLAG + 256;
      END IF;

      VV_ERR_LOC         := '190';

      -- Do Bitwise-AND operation on calculated quantity from source and target to the
      -- match quality definitions and identify the resulting matching quality
      DECLARE
         V_QM_MAT1   QM_MATRIX1 := QM_MAT(P_HIGHESTPOSLEVEL_LEVEL);
      BEGIN
         VV_ERR_LOC   := '230';

         FOR INX IN REVERSE 1 .. 5
         LOOP
            IF (V_QM_MAT1(INX).MASK_VALUE <> 0
            AND BITAND(V_QM_MAT1(INX).MASK_VALUE, L_TM_QUALITYFLAG) = V_QM_MAT1(INX).COMPUTED_VALUE)
            THEN
               VV_ERR_LOC     := '240';
               V_RETURN_VAL   := INX;
               EXIT;
            END IF;
         END LOOP;

         -- get the quality name for the resulting match quality
         DECLARE
            V_MATCH_QUALITY   VARCHAR2(1);
            V_MATCH_ACTION    VARCHAR2(1);
         BEGIN
            IF V_RETURN_VAL = 0
            THEN
               RETURN V_RETURN_VAL;
            END IF;

            VV_ERR_LOC       := '250';

            SELECT QUALITY_NAME
              INTO V_MATCH_QUALITY
              FROM P_B_QUAL_RELATION
             WHERE QUALITY_VALUE = NVL(V_RETURN_VAL, 10);

            -- get the match action for the selected match quality name using
            -- function call fn_get_match_action.
            VV_ERR_LOC       := '260';
            V_MATCH_ACTION   := FN_GET_MATCH_ACTION(P_HIGHESTPOSLEVEL_LEVEL, V_MATCH_QUALITY);

            -- when match action 'N' then return 0 else return selected match quality
            IF V_MATCH_ACTION = 'N'
            THEN
               RETURN 0;
            ELSE
               RETURN V_RETURN_VAL;
            END IF;
         END;
      END;
   EXCEPTION
      WHEN OTHERS
      THEN
         VERRORCODE   := SQLCODE;
         VERRORDESC   := SQLERRM;
         SP_ERROR_LOG(
            P_SRC_HOST_ID,
            VUSERID,
            VIPADDRESS,
               VSOURCE
            || ' Error for '
            || P_SRC_ENTITY_ID
            || '/'
            || P_SRC_CURRENCY_CODE
            || '/'
            || P_SRC_MOVEMENT_ID
            || '/'
            || P_TGT_MOVEMENT_ID
            || ' at Location: '
            || VV_ERR_LOC,
            VERRORCODE,
            VERRORDESC);
   END FN_UPDATE_P_B_MATQUAL_POSLEVEL;


   -- ----------------------------------------------------------------------------------------------
   -- Validate the amount total matching quality with source and target movements
   -- ----------------------------------------------------------------------------------------------
   PROCEDURE SP_MATCHING_AMOUNT_TOTAL(P_SOURCE_POSITION_LEVEL IN NUMBER, PN_TOLERANCE_IN IN NUMBER)
   IS
      -- Numeric Variables
      V_SRC_AMT   NUMBER(22, 4) := 0;
   BEGIN
      -- target movements validation
      DECLARE
         VN_LOWER_LIMIT   NUMBER;
         VN_UPPER_LIMIT   NUMBER;

         CURSOR CR_MARK_MOVEMENTS
         IS
              SELECT DISTINCT POS_LEVEL, SUM(AMOUNT) AMT
                FROM P_B_TARGET_MOVEMENTS
               WHERE POS_LEVEL <> P_SOURCE_POSITION_LEVEL
            GROUP BY POS_LEVEL;
      BEGIN
         SELECT SUM(AMOUNT)
           INTO V_SRC_AMT
           FROM P_B_TARGET_MOVEMENTS
          WHERE POS_LEVEL = P_SOURCE_POSITION_LEVEL;

         FOR R_MARK_MOVEMENTS IN CR_MARK_MOVEMENTS
         LOOP
            -- set valid flag = 'T' as target movement has valid source amount total
            IF R_MARK_MOVEMENTS.AMT = V_SRC_AMT
            THEN
               UPDATE P_B_TARGET_MOVEMENTS
                  SET VALID   = 'T'
                WHERE POS_LEVEL = R_MARK_MOVEMENTS.POS_LEVEL;
            ELSE
               VN_LOWER_LIMIT   := R_MARK_MOVEMENTS.AMT - PN_TOLERANCE_IN;
               VN_UPPER_LIMIT   := R_MARK_MOVEMENTS.AMT + PN_TOLERANCE_IN;

               IF V_SRC_AMT BETWEEN VN_LOWER_LIMIT AND VN_UPPER_LIMIT
               THEN
                  UPDATE P_B_TARGET_MOVEMENTS
                     SET VALID   = 'T'
                   WHERE POS_LEVEL = R_MARK_MOVEMENTS.POS_LEVEL;
               END IF;
            END IF;
         END LOOP;
      END;

      COMMIT;

      -- source movement validation
      DECLARE
         V_CNT   NUMBER;
      BEGIN
         SELECT COUNT(1)
           INTO V_CNT
           FROM P_B_TARGET_MOVEMENTS
          WHERE VALID = 'T'
            AND POS_LEVEL <> P_SOURCE_POSITION_LEVEL;

         IF V_CNT > 0
         THEN
            -- set valid flag = 'T' as source movement has valid target amount total
            UPDATE P_B_TARGET_MOVEMENTS
               SET VALID   = 'T'
             WHERE POS_LEVEL = P_SOURCE_POSITION_LEVEL;
         END IF;
      END;
   END SP_MATCHING_AMOUNT_TOTAL;


   -- ----------------------------------------------------------------------------------------------
   -- Checks the amount total quality to be check for the current source position or not
   -- ----------------------------------------------------------------------------------------------
   FUNCTION FN_MATCHING_AMOUNT_TOTAL_FLAG(P_QUALITY_VAR IN VARCHAR2, P_SOURCE_POSITION_LEVEL IN NUMBER)
      RETURN VARCHAR2
   IS
      V_AMT_TOTAL   CHAR(1) := 'N';
   BEGIN
      BEGIN
         V_AMT_TOTAL   := V_AMTTOTAL(P_SOURCE_POSITION_LEVEL)(P_QUALITY_VAR).VALUE;
      EXCEPTION
         WHEN NO_DATA_FOUND
         THEN
            V_AMT_TOTAL   := 'N';
         WHEN TOO_MANY_ROWS
         THEN
            V_AMT_TOTAL   := 'Y';
      END;

      RETURN V_AMT_TOTAL;
   END FN_MATCHING_AMOUNT_TOTAL_FLAG;


   -- ----------------------------------------------------------------------------------------------
   -- 1. Used to rest the movements that are unmatched by the current match from P_MOVEMENT table
   -- 2. Delete the matched id from P_MATCH table
   -- 3. resetting of movements determined by input parameter pn_match_id_prev_in
   -- ----------------------------------------------------------------------------------------------
   PROCEDURE SP_MATCH_DELETE_ACTIONS(PV_HOST_ID_IN         IN VARCHAR2,
                                     PV_ENTITY_ID_IN       IN VARCHAR2,
                                     PV_CURRENCY_CODE_IN   IN VARCHAR2,
                                     PN_MATCHID_PREV_IN    IN NUMBER)
   IS
      -- Numeric Variables
      VN_ERRORCODE   NUMBER;
      -- String Variables
      VV_ERRORDESC   S_ERROR_LOG.ERROR_DESC%TYPE;
      VV_USERID      S_ERROR_LOG.USER_ID%TYPE := 'SYSTEM';
      VV_IPADDRESS   S_ERROR_LOG.IP_ADDRESS%TYPE := 'DB SERVER';
      VV_SOURCE      S_ERROR_LOG.SOURCE%TYPE := 'Auto Matching - sp_match_delete_actions ';
      VV_ERR_LOC     VARCHAR2(10) := 0;
   BEGIN
      -- Reset the movements that have match_id = previous match_id [Broken Movement]
      UPDATE P_MOVEMENT
         SET MATCH_STATUS     = 'L',
             MATCH_ID         = NULL,
             PREDICT_STATUS   = INITIAL_PREDICT_STATUS,
             EXTRACT_STATUS   = 'E',
             UPDATE_USER      = 'SYSTEM',
             BOOKCODE         = DECODE(BOOKCODE_AVAIL,  'N', '',  'Y', BOOKCODE),
             UPDATE_DATE      = GLOBAL_VAR.SYS_DATE
       WHERE HOST_ID = PV_HOST_ID_IN
         AND ENTITY_ID = PV_ENTITY_ID_IN
         AND CURRENCY_CODE = PV_CURRENCY_CODE_IN
         AND MATCH_ID = PN_MATCHID_PREV_IN;

      -- Delete the match From P_movement table where match_id = previous match_id
      DELETE FROM P_MATCH
            WHERE HOST_ID = PV_HOST_ID_IN
              AND ENTITY_ID = PV_ENTITY_ID_IN
              AND CURRENCY_CODE = PV_CURRENCY_CODE_IN
              AND MATCH_ID = PN_MATCHID_PREV_IN;

      COMMIT;
   EXCEPTION
      WHEN OTHERS
      THEN
         VN_ERRORCODE   := SQLCODE;
         VV_ERRORDESC   := SQLERRM;
         SP_ERROR_LOG(PV_HOST_ID_IN,
                      VV_USERID,
                      VV_IPADDRESS,
                      VV_SOURCE || ' ERROR @ Location - ' || VV_ERR_LOC,
                      VN_ERRORCODE,
                      VV_ERRORDESC);
   END SP_MATCH_DELETE_ACTIONS;


   -- ----------------------------------------------------------------------------------------------
   -- Used to create and update matches in P_MATCH table.
   -- Update the movements and their match_status and match_id in P_MOVEMENT table
   -- call sp_update_bookcode and SP_UPDATE_LORO_TO_PREDICT procedure to update
   -- the bookcode and open flag of the source movement.
   -- Movements in the current match and their quality will be identified using
   -- P_B_TARGET_MOVEMENTS table.
   -- ----------------------------------------------------------------------------------------------
   PROCEDURE SP_MATCH_UPDATE_ACTIONS(PV_HOST_ID_IN           IN     VARCHAR2,
                                     PV_ENTITY_ID_IN         IN     VARCHAR2,
                                     PV_CURRENCY_CODE_IN     IN     VARCHAR2,
                                     PN_MATCH_ID_IN          IN     NUMBER,
                                     PN_SRC_MOVEMENT_ID_IN   IN     NUMBER,
                                     PN_RETN_MATCHID_OUT        OUT NUMBER,
                                     PV_RETN_STATUS_OUT         OUT VARCHAR2,
                                     PV_RETN_QUALITY_OUT        OUT VARCHAR2,
                                     PN_RETN_HI_POS_OUT         OUT NUMBER,
                                     PN_RETN_LOW_POS_OUT        OUT NUMBER,
                                     PN_STATUS_POSITION      IN     NUMBER,
                                     PN_SRC_POSITION_IN      IN     NUMBER)
   IS
      -- Numeric Variables
      VN_ERRORCODE           NUMBER;
      VN_MATCHID             NUMBER;
      VN_QTY                 NUMBER;
      VN_HIGH_POS_LVL        NUMBER;
      VN_LOW_POS_LVL         NUMBER;
      VN_HI_AMOUNT           NUMBER;
      VN_INT_QTY1            NUMBER(2) := 0;
      VN_INT_QTY2            NUMBER(2) := 0;
      VN_INT_QTY3            NUMBER(2) := 0;
      VN_INT_QTY4            NUMBER(2) := 0;
      VN_INT_QTY5            NUMBER(2) := 0;
      VN_INT_QTY6            NUMBER(2) := 0;
      VN_INT_QTY7            NUMBER(2) := 0;
      VN_INT_QTY8            NUMBER(2) := 0;
      VN_INT_QTY9            NUMBER(2) := 0;
      -- Date Variables
      VD_HI_VALUE_DATE       DATE;
      -- String Variables
      VV_ERRORDESC           S_ERROR_LOG.ERROR_DESC%TYPE;
      VV_USERID              S_ERROR_LOG.USER_ID%TYPE := 'SYSTEM';
      VV_IPADDRESS           S_ERROR_LOG.IP_ADDRESS%TYPE := 'DB SERVER';
      VV_SOURCE              S_ERROR_LOG.SOURCE%TYPE := 'Auto Matching - sp_post_match_actions ';
      VV_ERR_LOC             VARCHAR2(10) := 0;
      VV_MATCH_QTY           VARCHAR2(1);
      VV_MATCH_STATUS        VARCHAR2(1);
      VV_INTERNAL_FLAG       VARCHAR2(1);
      VV_INT_QTY1_VAR        VARCHAR2(1) := NULL;
      VV_INT_QTY2_VAR        VARCHAR2(1) := NULL;
      VV_INT_QTY3_VAR        VARCHAR2(1) := NULL;
      VV_INT_QTY4_VAR        VARCHAR2(1) := NULL;
      VV_INT_QTY5_VAR        VARCHAR2(1) := NULL;
      VV_INT_QTY6_VAR        VARCHAR2(1) := NULL;
      VV_INT_QTY7_VAR        VARCHAR2(1) := NULL;
      VV_INT_QTY8_VAR        VARCHAR2(1) := NULL;
      VV_INT_QTY9_VAR        VARCHAR2(1) := NULL;

      -- Cursor fetches the value_date, movement_id, beneficiary_id
      CURSOR CR_MOVEMENT_UPDATE
      IS
         SELECT VALUE_DATE, MOVEMENT_ID, BENEFICIARY_ID FROM P_B_TARGET_MOVEMENTS;

      -- Cursor fetches the quality of each position levels
      CURSOR CR_MATCH_UPDATE
      IS
           SELECT DISTINCT POS_LEVEL, QUALITYFLAG
             FROM P_B_TARGET_MOVEMENTS
         ORDER BY POS_LEVEL DESC;

      INTEGRITY_CONSTRAINT   EXCEPTION;
      PRAGMA EXCEPTION_INIT(INTEGRITY_CONSTRAINT, -2291);
   BEGIN
      VV_ERR_LOC        := '10';

      -- set internal match quality variables with cr_match_update cursor
      FOR R_MATCH_UPDATE IN CR_MATCH_UPDATE
      LOOP
         CASE R_MATCH_UPDATE.POS_LEVEL
            WHEN 1
            THEN
               VN_INT_QTY1       := R_MATCH_UPDATE.QUALITYFLAG;
               VV_INT_QTY1_VAR   := V_QUALITY_VARIABLE(VN_INT_QTY1);
            WHEN 2
            THEN
               VN_INT_QTY2       := R_MATCH_UPDATE.QUALITYFLAG;
               VV_INT_QTY2_VAR   := V_QUALITY_VARIABLE(VN_INT_QTY2);
            WHEN 3
            THEN
               VN_INT_QTY3       := R_MATCH_UPDATE.QUALITYFLAG;
               VV_INT_QTY3_VAR   := V_QUALITY_VARIABLE(VN_INT_QTY3);
            WHEN 4
            THEN
               VN_INT_QTY4       := R_MATCH_UPDATE.QUALITYFLAG;
               VV_INT_QTY4_VAR   := V_QUALITY_VARIABLE(VN_INT_QTY4);
            WHEN 5
            THEN
               VN_INT_QTY5       := R_MATCH_UPDATE.QUALITYFLAG;
               VV_INT_QTY5_VAR   := V_QUALITY_VARIABLE(VN_INT_QTY5);
            WHEN 6
            THEN
               VN_INT_QTY6       := R_MATCH_UPDATE.QUALITYFLAG;
               VV_INT_QTY6_VAR   := V_QUALITY_VARIABLE(VN_INT_QTY6);
            WHEN 7
            THEN
               VN_INT_QTY7       := R_MATCH_UPDATE.QUALITYFLAG;
               VV_INT_QTY7_VAR   := V_QUALITY_VARIABLE(VN_INT_QTY7);
            WHEN 8
            THEN
               VN_INT_QTY8       := R_MATCH_UPDATE.QUALITYFLAG;
               VV_INT_QTY8_VAR   := V_QUALITY_VARIABLE(VN_INT_QTY8);
            WHEN 9
            THEN
               VN_INT_QTY9       := R_MATCH_UPDATE.QUALITYFLAG;
               VV_INT_QTY9_VAR   := V_QUALITY_VARIABLE(VN_INT_QTY9);
            ELSE
               NULL;
         END CASE;
      END LOOP;

      VV_ERR_LOC        := '20';

      -- select the min of match quality
      --            min of position level
      --            max of position level
      --            max of amount
      --            max of value date
      SELECT MIN(QUALITYFLAG), MIN(POS_LEVEL), MAX(POS_LEVEL),
             MAX(ABS(AMOUNT)), MAX(VALUE_DATE)
        INTO VN_QTY, VN_LOW_POS_LVL, VN_HIGH_POS_LVL,
             VN_HI_AMOUNT, VD_HI_VALUE_DATE
        FROM P_B_TARGET_MOVEMENTS;

      VV_ERR_LOC        := '30';

      -- Check if any movements with predict_status 'I'
      BEGIN
         SELECT DISTINCT 'Y'
           INTO VV_INTERNAL_FLAG
           FROM P_B_TARGET_MOVEMENTS M
          WHERE EXISTS
                   (SELECT NULL
                      FROM P_B_TARGET_MOVEMENTS O
                     WHERE O.MOVEMENT_ID = M.MOVEMENT_ID
                       AND ((O.PREDICT_STATUS = 'I'
                         AND O.MATCH_ID IS NULL)
                         OR (O.INITIAL_PREDICT_STATUS = 'I'
                         AND O.MATCH_ID IS NOT NULL)));
      EXCEPTION
         WHEN NO_DATA_FOUND
         THEN
            VV_INTERNAL_FLAG   := 'N';
      END;

      VV_ERR_LOC        := '40';
      -- get the matquality name and match action
      VV_MATCH_QTY      := V_QUALITY_VARIABLE(VN_QTY);
      VV_MATCH_STATUS   := FN_GET_MATCH_ACTION(PN_STATUS_POSITION, VV_MATCH_QTY);

      VV_ERR_LOC        := '50';

      -- check lowest position level = pre-advice position and match status = 'M' {Offer}
      -- then set match quality = 'E'
      IF VN_LOW_POS_LVL = GN_PRE_ADVICE_POS
     AND VV_MATCH_STATUS = 'M'
      THEN
         VV_MATCH_QTY   := 'E';

         IF GN_PRE_ADVICE_POS = 1
         THEN
            VV_INT_QTY1_VAR   := 'E';
         ELSIF GN_PRE_ADVICE_POS = 2
         THEN
            VV_INT_QTY2_VAR   := 'E';
         END IF;
      END IF;

      VV_ERR_LOC        := '60';

      -- do following functionality when input match status = 'M' and match id != 0
      IF PN_MATCH_ID_IN != 0
     AND VV_MATCH_STATUS = 'M'
      THEN
         -- IF vn_low_pos_lvl != gn_pre_advice_pos
         -- THEN
         DECLARE
            VV_ACTION        CHAR(1);
            VV_MOVEMENT      P_MOVEMENT.MOVEMENT_ID%TYPE;
            VV_UPDATE_USER   P_MOVEMENT.UPDATE_USER%TYPE;
            VN_MIN_QUALITY   NUMBER(1);
         BEGIN
            VV_ERR_LOC   := '70';

            SELECT DISTINCT MOVEMENT_ID, MATCH_STATUS
              INTO VV_MOVEMENT, VV_ACTION
              FROM P_B_TARGET_MOVEMENTS
             WHERE MATCH_ID = PN_MATCH_ID_IN
               AND POS_LEVEL > GN_MAX_INTERNAL_POS
               AND ROWNUM = 1;

            IF VN_LOW_POS_LVL != GN_PRE_ADVICE_POS
           AND PN_SRC_POSITION_IN > GN_MAX_INTERNAL_POS
           AND VV_ACTION != 'M'
            THEN
               VV_ERR_LOC   := '80';

               SELECT UPDATE_USER
                 INTO VV_UPDATE_USER
                 FROM P_MOVEMENT
                WHERE HOST_ID = PV_HOST_ID_IN
                  AND ENTITY_ID = PV_ENTITY_ID_IN
                  AND MOVEMENT_ID = VV_MOVEMENT;

               IF VV_UPDATE_USER != 'SYSTEM'
               THEN
                  VV_MATCH_STATUS   := VV_ACTION;
               END IF;
            ELSE
               VV_ERR_LOC   := '90';

               SELECT MIN(DISTINCT (QUALITYFLAG))
                 INTO VN_MIN_QUALITY
                 FROM P_B_TARGET_MOVEMENTS
                WHERE POS_LEVEL != GN_PRE_ADVICE_POS;

               IF VN_MIN_QUALITY = 5
               THEN
                  VV_MATCH_STATUS   := VV_ACTION;
               END IF;
            END IF;
         EXCEPTION
            WHEN NO_DATA_FOUND
            THEN
               VV_ERR_LOC   := '100';
               NULL;
         END;
      -- END IF;
      END IF;

      VV_ERR_LOC        := '110';

      -- check if the match action is confirm, offer or reconcile
      IF VV_MATCH_STATUS IN ('C', 'M', 'E')
      THEN
         -- Existing Match?????
         IF PN_MATCH_ID_IN = 0
         THEN
            VV_ERR_LOC   := '120';
            -- Create new match
            VN_MATCHID   := PK_UTILITY.FN_GET_SEQUENCE_NUMBER('match');
            VV_ERR_LOC   := '130';

            INSERT INTO P_MATCH(HOST_ID,
                                ENTITY_ID,
                                MATCH_ID,
                                CURRENCY_CODE,
                                HIGHEST_POSITION_LEVEL,
                                LOWEST_POSITION_LEVEL,
                                MATCH_QUALITY,
                                STATUS,
                                INTERNAL_QUALITY1,
                                INTERNAL_QUALITY2,
                                INTERNAL_QUALITY3,
                                INTERNAL_QUALITY4,
                                INTERNAL_QUALITY5,
                                INTERNAL_QUALITY6,
                                INTERNAL_QUALITY7,
                                INTERNAL_QUALITY8,
                                INTERNAL_QUALITY9,
                                STATUS_USER,
                                UPDATE_USER,
                                STATUS_DATE,
                                CONFIRMED_DATE,
                                ORIG_CONFIRMED_DATE,
                                UPDATE_DATE,
                                PREDICT_STATUS_INTERNAL_FLAG,
                                MATCH_HI_AMOUNT,
                                MATCH_HI_VALUE_DATE)
                 VALUES (PV_HOST_ID_IN,
                         PV_ENTITY_ID_IN,
                         VN_MATCHID,
                         PV_CURRENCY_CODE_IN,
                         VN_HIGH_POS_LVL,
                         VN_LOW_POS_LVL,
                         VV_MATCH_QTY,
                         VV_MATCH_STATUS,
                         VV_INT_QTY1_VAR,
                         VV_INT_QTY2_VAR,
                         VV_INT_QTY3_VAR,
                         VV_INT_QTY4_VAR,
                         VV_INT_QTY5_VAR,
                         VV_INT_QTY6_VAR,
                         VV_INT_QTY7_VAR,
                         VV_INT_QTY8_VAR,
                         VV_INT_QTY9_VAR,
                         'SYSTEM',
                         'SYSTEM',
                         GLOBAL_VAR.SYS_DATE,
                         GLOBAL_VAR.SYS_DATE,
                         GLOBAL_VAR.SYS_DATE,
                         GLOBAL_VAR.SYS_DATE,
                         VV_INTERNAL_FLAG,
                         VN_HI_AMOUNT,
                         VD_HI_VALUE_DATE);

            COMMIT;
         ELSE
            -- Update Existing match
            VN_MATCHID   := PN_MATCH_ID_IN;
            VV_ERR_LOC   := '140';

            UPDATE P_MATCH
               SET HIGHEST_POSITION_LEVEL         = VN_HIGH_POS_LVL,
                   LOWEST_POSITION_LEVEL          = VN_LOW_POS_LVL,
                   MATCH_QUALITY                  = VV_MATCH_QTY,
                   STATUS                         = VV_MATCH_STATUS,
                   PREDICT_STATUS_INTERNAL_FLAG   = VV_INTERNAL_FLAG,
                   MATCH_HI_AMOUNT                = VN_HI_AMOUNT,
                   MATCH_HI_VALUE_DATE            = VD_HI_VALUE_DATE,
                   INTERNAL_QUALITY1              = VV_INT_QTY1_VAR,
                   INTERNAL_QUALITY2              = VV_INT_QTY2_VAR,
                   INTERNAL_QUALITY3              = VV_INT_QTY3_VAR,
                   INTERNAL_QUALITY4              = VV_INT_QTY4_VAR,
                   INTERNAL_QUALITY5              = VV_INT_QTY5_VAR,
                   INTERNAL_QUALITY6              = VV_INT_QTY6_VAR,
                   INTERNAL_QUALITY7              = VV_INT_QTY7_VAR,
                   INTERNAL_QUALITY8              = VV_INT_QTY8_VAR,
                   INTERNAL_QUALITY9              = VV_INT_QTY9_VAR,
                   STATUS_DATE                    = GLOBAL_VAR.SYS_DATE,
                   CONFIRMED_DATE                 = GLOBAL_VAR.SYS_DATE,
                   ORIG_CONFIRMED_DATE            = NVL(ORIG_CONFIRMED_DATE, GLOBAL_VAR.SYS_DATE)
             WHERE HOST_ID = PV_HOST_ID_IN
               AND ENTITY_ID = PV_ENTITY_ID_IN
               AND CURRENCY_CODE = PV_CURRENCY_CODE_IN
               AND MATCH_ID = VN_MATCHID;

            COMMIT;
         END IF;

         -- Update P_MOVEMENT for the match_id
         BEGIN
            IF VN_LOW_POS_LVL != GN_PRE_ADVICE_POS
            THEN
               FOR R_MOVEMENT_UPDATE IN CR_MOVEMENT_UPDATE
               LOOP
                  -- The below p_b_target_movements update is added for Mantis 1243 issue 1
                  VV_ERR_LOC   := '150';

                  UPDATE P_B_TARGET_MOVEMENTS
                     SET MATCH_ID = VN_MATCHID, MATCH_STATUS = VV_MATCH_STATUS
                   WHERE HOST_ID = PV_HOST_ID_IN
                     AND ENTITY_ID = PV_ENTITY_ID_IN
                     AND CURRENCY_CODE = PV_CURRENCY_CODE_IN
                     AND VALUE_DATE = R_MOVEMENT_UPDATE.VALUE_DATE
                     AND MOVEMENT_ID = R_MOVEMENT_UPDATE.MOVEMENT_ID;

                  VV_ERR_LOC   := '160';

                  UPDATE P_MOVEMENT
                     SET MATCH_ID         = VN_MATCHID,
                         MATCH_STATUS     = VV_MATCH_STATUS,
                         UPDATE_DATE      = GLOBAL_VAR.SYS_DATE,
                         UPDATE_USER      = 'SYSTEM',
                         PREDICT_STATUS      =
                            DECODE(
                               VV_INTERNAL_FLAG,
                               'Y', DECODE(VV_MATCH_STATUS,
                                           'C', DECODE(POSITION_LEVEL, VN_HIGH_POS_LVL, 'I', 'E'),
                                           'E', DECODE(POSITION_LEVEL, VN_HIGH_POS_LVL, 'I', 'E'),
                                           INITIAL_PREDICT_STATUS),
                               INITIAL_PREDICT_STATUS),
                         BENEFICIARY_ID   = R_MOVEMENT_UPDATE.BENEFICIARY_ID,
                         EXTRACT_STATUS      =
                            DECODE(POSITION_LEVEL,
                                   VN_HIGH_POS_LVL, DECODE(INPUT_SOURCE, 'HST', 'I', EXTRACT_STATUS),
                                   DECODE(INPUT_SOURCE, 'HST', 'E', EXTRACT_STATUS))
                   WHERE HOST_ID = PV_HOST_ID_IN
                     AND ENTITY_ID = PV_ENTITY_ID_IN
                     -- AND currency_code = pv_currency_code_in
                     -- AND value_date = r_movement_update.value_date
                     AND MOVEMENT_ID = R_MOVEMENT_UPDATE.MOVEMENT_ID;

                  COMMIT;
               END LOOP;
            ELSE
               -- update p_movement for selected movements in cr_movement_update
               -- cursor
               FOR R_MOVEMENT_UPDATE IN CR_MOVEMENT_UPDATE
               LOOP
                  -- The below p_b_target_movements update is added for Mantis 1243 issue 1
                  VV_ERR_LOC   := '160';

                  UPDATE P_B_TARGET_MOVEMENTS
                     SET MATCH_ID = VN_MATCHID, MATCH_STATUS = VV_MATCH_STATUS
                   WHERE HOST_ID = PV_HOST_ID_IN
                     AND ENTITY_ID = PV_ENTITY_ID_IN
                     AND CURRENCY_CODE = PV_CURRENCY_CODE_IN
                     AND VALUE_DATE = R_MOVEMENT_UPDATE.VALUE_DATE
                     AND MOVEMENT_ID = R_MOVEMENT_UPDATE.MOVEMENT_ID;

                  VV_ERR_LOC   := '170';

                  UPDATE P_MOVEMENT
                     SET MATCH_ID         = VN_MATCHID,
                         MATCH_STATUS     = VV_MATCH_STATUS,
                         UPDATE_DATE      = GLOBAL_VAR.SYS_DATE,
                         UPDATE_USER      = 'SYSTEM',
                         PREDICT_STATUS      =
                            DECODE(
                               VV_INTERNAL_FLAG,
                               'Y', DECODE(VV_MATCH_STATUS,
                                           'C', DECODE(POSITION_LEVEL, VN_HIGH_POS_LVL, 'I', 'E'),
                                           'E', DECODE(POSITION_LEVEL, VN_HIGH_POS_LVL, 'I', 'E'),
                                           DECODE(POSITION_LEVEL, GN_MAX_INTERNAL_POS, PREDICT_STATUS, INITIAL_PREDICT_STATUS)),
                               DECODE(POSITION_LEVEL, GN_MAX_INTERNAL_POS, PREDICT_STATUS, INITIAL_PREDICT_STATUS)),
                         BENEFICIARY_ID   = R_MOVEMENT_UPDATE.BENEFICIARY_ID,
                         EXTRACT_STATUS      =
                            DECODE(POSITION_LEVEL,
                                   VN_HIGH_POS_LVL, DECODE(INPUT_SOURCE, 'HST', 'I', EXTRACT_STATUS),
                                   DECODE(INPUT_SOURCE, 'HST', 'E', EXTRACT_STATUS))
                   WHERE HOST_ID = PV_HOST_ID_IN
                     AND ENTITY_ID = PV_ENTITY_ID_IN
                     -- AND currency_code = pv_currency_code_in
                     -- AND value_date = r_movement_update.value_date
                     AND MOVEMENT_ID = R_MOVEMENT_UPDATE.MOVEMENT_ID;

                  COMMIT;
               END LOOP;
            END IF;
         END;

         VV_ERR_LOC            := '180';
         -- set the match id, match status, match quality, highest and lowest
         -- position levels to the out parameters
         PN_RETN_MATCHID_OUT   := VN_MATCHID;
         PV_RETN_STATUS_OUT    := VV_MATCH_STATUS;
         PV_RETN_QUALITY_OUT   := VV_MATCH_QTY;
         PN_RETN_HI_POS_OUT    := VN_HIGH_POS_LVL;
         PN_RETN_LOW_POS_OUT   := VN_LOW_POS_LVL;

         -- Match status 'Confirm' actions
         IF VV_MATCH_STATUS = 'C'
         THEN
            -- To update the the bookcode of the highest position_level in the match
            VV_ERR_LOC   := '190';
            SP_UPDATE_BOOKCODE(PV_HOST_ID_IN,
                               PV_ENTITY_ID_IN,
                               PV_CURRENCY_CODE_IN,
                               VN_MATCHID,
                               PN_SRC_MOVEMENT_ID_IN);
            -- To_update the open flag to 'N' of the highest position_level
            VV_ERR_LOC   := '200';
         END IF;
      END IF;
   EXCEPTION
      WHEN INTEGRITY_CONSTRAINT
      THEN
         -- Where a movement is being updated with an invalid match id
         -- Revert the movements to outstanding if it cannot be updated with a match id.
         SP_MATCH_DELETE_ACTIONS(PV_HOST_ID_IN,
                                 PV_ENTITY_ID_IN,
                                 PV_CURRENCY_CODE_IN,
                                 VN_MATCHID);

         DELETE FROM P_B_TARGET_MOVEMENTS
               WHERE MATCH_ID = VN_MATCHID;

         COMMIT;
      WHEN OTHERS
      THEN
         VN_ERRORCODE   := SQLCODE;
         VV_ERRORDESC   := SQLERRM;
         SP_ERROR_LOG(PV_HOST_ID_IN,
                      VV_USERID,
                      VV_IPADDRESS,
                      VV_SOURCE || ' ERROR @ Location - ' || VV_ERR_LOC,
                      VN_ERRORCODE,
                      VV_ERRORDESC);
   END SP_MATCH_UPDATE_ACTIONS;


   -- ----------------------------------------------------------------------------------------------
   -- Preadvice matching is used to match preadvice position to the existing match
   -- or to the source movement. It is call at the end of the source cursor loop.
   -- Since preadvice position is not having proper reference; amount, account
   -- and value_date are used as the parameters for matching the preadvice position.
   -- ----------------------------------------------------------------------------------------------
   PROCEDURE SP_PREADVICE_MATCHING(PV_HOST_ID_IN         IN     VARCHAR2,
                                   PV_ENTITY_ID_IN       IN     VARCHAR2,
                                   PV_CURRENCY_CODE_IN   IN     VARCHAR2,
                                   PD_VALUE_DATE_IN      IN     DATE,
                                   PV_ACCOUNT_ID_IN      IN     VARCHAR2,
                                   PN_POSITION_IN        IN     NUMBER,
                                   PN_MOVEMENT_ID_IN     IN     NUMBER,
                                   PV_SIGN_IN            IN     VARCHAR2,
                                   PN_AMOUNT_IN          IN     NUMBER,
                                   PN_MATCHID_ID_INOUT   IN OUT NUMBER,
                                   PN_PREADVICE_POS_IN   IN     NUMBER,
                                   PN_LOWEST_POS_OUT        OUT NUMBER,
                                   PN_HIGHEST_POS_OUT       OUT NUMBER,
                                   VV_IS_OPEN               OUT VARCHAR2)
   IS
      -- Numeric Variables
      VN_PREADVICE_MVMT_ID   NUMBER;
      VN_CNTR                NUMBER;
      VN_MATCHID             NUMBER;
      VN_MATCH_ID            NUMBER;
      VN_MATCH_HI_POS        NUMBER(1);
      VN_OPN_MOV             NUMBER := 0;
      -- String Variables
      VV_LINKED_ACCOUNT_ID   P_MOVEMENT.ACCOUNT_ID%TYPE;
      VV_STATUS              VARCHAR2(1);
      VV_DEBUG_MODE          CHAR(1);
      VV_SQL                 VARCHAR2(4000);
      VV_DATE                VARCHAR2(50);

      -- Cursor Definitions
      CURSOR CR_PRE_ADVICE_MATCHED(VV_LINKED_ACCOUNT_ID VARCHAR2, VN_POSITION NUMBER)
      IS
           SELECT A.MOVEMENT_ID, A.MATCH_STATUS, A.MATCH_ID
             FROM P_MOVEMENT A
            WHERE A.HOST_ID = PV_HOST_ID_IN
              AND A.ENTITY_ID = PV_ENTITY_ID_IN
              AND A.CURRENCY_CODE = PV_CURRENCY_CODE_IN
              AND A.VALUE_DATE = PD_VALUE_DATE_IN
              AND (A.ACCOUNT_ID = PV_ACCOUNT_ID_IN
                OR A.ACCOUNT_ID = VV_LINKED_ACCOUNT_ID)
              AND A.POSITION_LEVEL = PN_PREADVICE_POS_IN
              AND A.AMOUNT = PN_AMOUNT_IN
              AND (A.PREDICT_STATUS = 'E'
                OR A.PREDICT_STATUS = 'I')
              AND A.SIGN = PV_SIGN_IN
              AND (A.MATCH_STATUS = 'L'
                OR (A.MATCH_STATUS IN ('M', 'C')
                AND NOT EXISTS
                       (SELECT NULL
                          FROM P_MOVEMENT M
                         WHERE M.HOST_ID = A.HOST_ID
                           AND M.ENTITY_ID = A.ENTITY_ID
                           AND M.MATCH_ID = A.MATCH_ID
                           AND M.POSITION_LEVEL = VN_POSITION)))
              AND NOT EXISTS
                     (SELECT NULL
                        FROM P_MOVEMENT_LOCK L
                       WHERE L.HOST_ID = A.HOST_ID
                         AND L.ENTITY_ID = A.ENTITY_ID
                         AND L.MOVEMENT_ID = A.MOVEMENT_ID)
         ORDER BY A.MOVEMENT_ID ASC;

      CURSOR CR_PRE_ADVICE_UNMATCHED(VV_LINKED_ACCOUNT_ID VARCHAR2)
      IS
           SELECT M.MOVEMENT_ID, M.MATCH_STATUS, M.MATCH_ID
             FROM P_MOVEMENT M
            WHERE M.HOST_ID = PV_HOST_ID_IN
              AND M.ENTITY_ID = PV_ENTITY_ID_IN
              AND M.CURRENCY_CODE = PV_CURRENCY_CODE_IN
              AND M.VALUE_DATE = PD_VALUE_DATE_IN
              AND (M.ACCOUNT_ID = PV_ACCOUNT_ID_IN
                OR M.ACCOUNT_ID = VV_LINKED_ACCOUNT_ID)
              AND M.POSITION_LEVEL = PN_PREADVICE_POS_IN
              AND M.AMOUNT = PN_AMOUNT_IN
              AND M.SIGN = PV_SIGN_IN
              AND M.MATCH_STATUS = 'L'
              AND (M.PREDICT_STATUS = 'E'
                OR M.PREDICT_STATUS = 'I')
              AND NOT EXISTS
                     (SELECT NULL
                        FROM P_MOVEMENT_LOCK L
                       WHERE L.HOST_ID = M.HOST_ID
                         AND L.ENTITY_ID = M.ENTITY_ID
                         AND L.MOVEMENT_ID = M.MOVEMENT_ID)
         ORDER BY M.MOVEMENT_ID ASC;

      R_PRE_ADVICE           CR_PRE_ADVICE_MATCHED%ROWTYPE;
   BEGIN
      -- initialize variables
      VN_PREADVICE_MVMT_ID   := 0;
      VN_CNTR                := 0;
      VN_MATCHID             := 0;
      VN_MATCH_ID            := 0;
      VN_MATCH_HI_POS        := 0;
      VV_IS_OPEN             := 'N';

      -- check match is running in debug mode or not
      VV_DEBUG_MODE          :=
         PK_APPLICATION.FN_GET_PARAMETER_VALUE(PV_ENTITY_ID_IN,
                                               'MATCHING',
                                               'DEBUG',
                                               'N');

      -- when match id parameter = 0 then open the cursor
      -- cr_pre_advice_matched else open cr_pre_advice_unmatched cursor.
      IF PN_MATCHID_ID_INOUT = 0
      THEN
         OPEN CR_PRE_ADVICE_MATCHED(VV_LINKED_ACCOUNT_ID, PN_POSITION_IN);
      ELSE
         OPEN CR_PRE_ADVICE_UNMATCHED(VV_LINKED_ACCOUNT_ID);
      END IF;

      -- get the linked account for the input account
      SELECT NVL(LINK_ACCOUNT_ID, ACCOUNT_ID)
        INTO VV_LINKED_ACCOUNT_ID
        FROM P_ACCOUNT
       WHERE HOST_ID = PV_HOST_ID_IN
         AND ENTITY_ID = PV_ENTITY_ID_IN
         AND CURRENCY_CODE = PV_CURRENCY_CODE_IN
         AND ACCOUNT_ID = PV_ACCOUNT_ID_IN;

      -- fetch the rows from open pre-advice cursor
      LOOP
         IF PN_MATCHID_ID_INOUT = 0
         THEN
            FETCH CR_PRE_ADVICE_MATCHED INTO R_PRE_ADVICE;

            EXIT WHEN CR_PRE_ADVICE_MATCHED%NOTFOUND;
         ELSE
            FETCH CR_PRE_ADVICE_UNMATCHED INTO R_PRE_ADVICE;

            EXIT WHEN CR_PRE_ADVICE_UNMATCHED%NOTFOUND;
         END IF;

         IF VN_CNTR = 0
         THEN
            VN_CNTR   := VN_CNTR + 1;

            DECLARE
               REF_REC_CNT   PLS_INTEGER := 0;
            BEGIN
               -- vv_diff_ref := 'N';
               SELECT DECODE(PN_MATCHID_ID_INOUT, 0, R_PRE_ADVICE.MATCH_ID, PN_MATCHID_ID_INOUT) INTO VN_MATCH_ID FROM DUAL;

               -- check the existence of cross reference from cross reference table
               -- for the supplied movement other than pre-advice position which is already matched
               -- with other positions
               SELECT COUNT(1)
                 INTO REF_REC_CNT
                 FROM P_REFERENCE_XREF R2
                WHERE R2.MOVEMENT_ID = PN_MOVEMENT_ID_IN
                  AND R2.HOST_ID = PV_HOST_ID_IN
                  AND R2.ENTITY_ID = PV_ENTITY_ID_IN
                  AND R2.CROSS_REFERENCE IN (SELECT R1.CROSS_REFERENCE
                                               FROM P_MOVEMENT M, P_REFERENCE_XREF R1
                                              WHERE M.HOST_ID = PV_HOST_ID_IN
                                                AND M.ENTITY_ID = PV_ENTITY_ID_IN
                                                AND M.MATCH_ID = VN_MATCH_ID
                                                AND M.POSITION_LEVEL != PN_PREADVICE_POS_IN
                                                AND M.MOVEMENT_ID = R1.MOVEMENT_ID
                                                AND M.HOST_ID = R1.HOST_ID
                                                AND M.ENTITY_ID = R1.ENTITY_ID);

               -- when different reference then set status = 'M'
               IF REF_REC_CNT = 0
               THEN
                  VV_STATUS   := 'M';
               ELSE
                  -- when preadvice match status =  'L' and set status = 'M'
                  -- otherwise set status as preadvice cursor status.
                  IF R_PRE_ADVICE.MATCH_STATUS = 'L'
                  THEN
                     VV_STATUS   := 'M';
                  ELSE
                     VV_STATUS   := R_PRE_ADVICE.MATCH_STATUS;
                  END IF;
               END IF;
            END;

            -- when input match id = 0 then create new match
            -- otherwise update the existing match
            IF PN_MATCHID_ID_INOUT = 0
            THEN
               VV_DATE   := TO_CHAR(GLOBAL_VAR.SYS_DATE, 'RRRRMMDD HH24:MI:SS');

               -- preadvice cursor match status != 'L' then update the existing match
               -- otherwise create a new match
               IF R_PRE_ADVICE.MATCH_STATUS != 'L'
               THEN
                  VN_MATCHID   := R_PRE_ADVICE.MATCH_ID;

                  -- get the highest position for the selected match id
                  SELECT HIGHEST_POSITION_LEVEL
                    INTO VN_MATCH_HI_POS
                    FROM P_MATCH
                   WHERE HOST_ID = PV_HOST_ID_IN
                     AND ENTITY_ID = PV_ENTITY_ID_IN
                     AND MATCH_ID = R_PRE_ADVICE.MATCH_ID;

                  -- update p_match
                  VV_SQL       :=
                        ' UPDATE p_match '
                     || '    SET match_quality = ''E'', '
                     || '        status = '''
                     || VV_STATUS
                     || ''','
                     || '        internal_quality'
                     || PN_POSITION_IN
                     || ' = ''E'', '
                     || '        status_date = '
                     || '        TO_DATE ( '''
                     || VV_DATE
                     || ''', ''RRRRMMDD HH24:MI:SS'') '
                     || ' WHERE host_id = '''
                     || PV_HOST_ID_IN
                     || ''''
                     || '   AND entity_id = '''
                     || PV_ENTITY_ID_IN
                     || ''''
                     || '   AND currency_code = '''
                     || PV_CURRENCY_CODE_IN
                     || ''''
                     || '   AND match_id = '
                     || VN_MATCHID;

                  EXECUTE IMMEDIATE VV_SQL;
               ELSE
                  VN_MATCHID        := PK_UTILITY.FN_GET_SEQUENCE_NUMBER('match');
                  VN_MATCH_HI_POS   := PN_POSITION_IN;
                  VV_STATUS         := 'M';
                  -- create new match
                  VV_SQL            := ' INSERT INTO p_match
                                 (host_id, entity_id,
                                  match_id, currency_code,
                                  highest_position_level,
                                  lowest_position_level, match_quality,
                                  status, internal_quality1,
                                  internal_quality4, match_hi_value_date,
                                  match_hi_amount, status_user,
                                  update_user,
                                  update_date,
                                  confirmed_date
                                 )
                          VALUES (''';

                  VV_SQL            :=
                        VV_SQL
                     || PV_HOST_ID_IN
                     || ''','''
                     || PV_ENTITY_ID_IN
                     || ''','
                     || VN_MATCHID
                     || ', '''
                     || PV_CURRENCY_CODE_IN
                     || ''','
                     || PN_POSITION_IN
                     || ','
                     || PN_PREADVICE_POS_IN
                     || ',''E'','''
                     || VV_STATUS
                     || ''',''E'',''E'', TO_DATE('''
                     || PD_VALUE_DATE_IN
                     || '''),'
                     || PN_AMOUNT_IN
                     || ', ''SYSTEM'',''SYSTEM'','
                     || 'TO_DATE ( '''
                     || VV_DATE
                     || ''', ''RRRRMMDD HH24:MI:SS''), '
                     || 'TO_DATE ( '''
                     || VV_DATE
                     || ''', ''RRRRMMDD HH24:MI:SS'') '
                     || ')';

                  EXECUTE IMMEDIATE VV_SQL;
               END IF;

               IF R_PRE_ADVICE.MATCH_STATUS != 'L'
               THEN
                  IF VV_DEBUG_MODE = 'Y'
                  THEN
                     -- log matches into debug tables when debug mode is enabled
                     INSERT INTO DEBUG_MATCH_TARGETS(MOVEMENT_ID,
                                                     VALUE_DATE,
                                                     MATCH_STATUS,
                                                     MATCH_ID,
                                                     DESCRIPTION)
                          VALUES (R_PRE_ADVICE.MOVEMENT_ID,
                                  GLOBAL_VAR.SYS_DATE,
                                  R_PRE_ADVICE.MATCH_STATUS,
                                  R_PRE_ADVICE.MATCH_ID,
                                  'preadvice-a');

                     INSERT INTO DEBUG_MATCH_TARGETS(MOVEMENT_ID,
                                                     VALUE_DATE,
                                                     MATCH_STATUS,
                                                     MATCH_ID,
                                                     DESCRIPTION)
                          VALUES (PN_MOVEMENT_ID_IN,
                                  GLOBAL_VAR.SYS_DATE,
                                  NULL,
                                  PN_MATCHID_ID_INOUT,
                                  'source_movement-a');

                     COMMIT;
                  END IF;

                  -- update movements for match status, predict status, match id etc
                  UPDATE P_MOVEMENT
                     SET MATCH_ID         = VN_MATCHID,
                         MATCH_STATUS     = VV_STATUS,
                         UPDATE_DATE      = GLOBAL_VAR.SYS_DATE,
                         UPDATE_USER      = 'SYSTEM',
                         PREDICT_STATUS   = DECODE(POSITION_LEVEL, 6, PREDICT_STATUS, INITIAL_PREDICT_STATUS),
                         EXTRACT_STATUS   = 'E'
                   WHERE HOST_ID = PV_HOST_ID_IN
                     AND ENTITY_ID = PV_ENTITY_ID_IN
                     AND CURRENCY_CODE = PV_CURRENCY_CODE_IN
                     AND VALUE_DATE = PD_VALUE_DATE_IN
                     AND (MATCH_ID = R_PRE_ADVICE.MATCH_ID
                       OR MOVEMENT_ID = PN_MOVEMENT_ID_IN);
               ELSE
                  IF VV_DEBUG_MODE = 'Y'
                  THEN
                     -- log matches into debug tables when debug mode is enabled
                     INSERT INTO DEBUG_MATCH_TARGETS(MOVEMENT_ID,
                                                     VALUE_DATE,
                                                     MATCH_STATUS,
                                                     MATCH_ID,
                                                     DESCRIPTION)
                          VALUES (R_PRE_ADVICE.MOVEMENT_ID,
                                  GLOBAL_VAR.SYS_DATE,
                                  R_PRE_ADVICE.MATCH_STATUS,
                                  R_PRE_ADVICE.MATCH_ID,
                                  'preadvice-b');

                     INSERT INTO DEBUG_MATCH_TARGETS(MOVEMENT_ID,
                                                     VALUE_DATE,
                                                     MATCH_STATUS,
                                                     MATCH_ID,
                                                     DESCRIPTION)
                          VALUES (PN_MOVEMENT_ID_IN,
                                  GLOBAL_VAR.SYS_DATE,
                                  NULL,
                                  PN_MATCHID_ID_INOUT,
                                  'source_movement-b');

                     COMMIT;
                  END IF;

                  -- update movements for match status, predict status, match id etc
                  UPDATE P_MOVEMENT
                     SET MATCH_ID         = VN_MATCHID,
                         MATCH_STATUS     = VV_STATUS,
                         UPDATE_DATE      = GLOBAL_VAR.SYS_DATE,
                         UPDATE_USER      = 'SYSTEM',
                         PREDICT_STATUS   = DECODE(POSITION_LEVEL, 6, PREDICT_STATUS, INITIAL_PREDICT_STATUS),
                         EXTRACT_STATUS   = 'E'
                   WHERE HOST_ID = PV_HOST_ID_IN
                     AND ENTITY_ID = PV_ENTITY_ID_IN
                     AND CURRENCY_CODE = PV_CURRENCY_CODE_IN
                     AND VALUE_DATE = PD_VALUE_DATE_IN
                     AND MOVEMENT_ID IN (R_PRE_ADVICE.MOVEMENT_ID, PN_MOVEMENT_ID_IN);
               END IF;

               COMMIT;
            ELSE
               -- Update Existing Match
               VN_MATCHID   := PN_MATCHID_ID_INOUT;

               IF PN_PREADVICE_POS_IN = 1
               THEN
                  -- update match for internal match quality 1 {preadvice position}
                  UPDATE P_MATCH
                     SET INTERNAL_QUALITY1              = 'E',
                         MATCH_QUALITY                  = 'E',
                         STATUS                         = VV_STATUS,
                         PREDICT_STATUS_INTERNAL_FLAG   = 'Y',
                         LOWEST_POSITION_LEVEL          = PN_PREADVICE_POS_IN,
                         STATUS_USER                    = 'SYSTEM',
                         UPDATE_USER                    = 'SYSTEM',
                         STATUS_DATE                    = GLOBAL_VAR.SYS_DATE,
                         UPDATE_DATE                    = GLOBAL_VAR.SYS_DATE
                   WHERE HOST_ID = PV_HOST_ID_IN
                     AND ENTITY_ID = PV_ENTITY_ID_IN
                     AND CURRENCY_CODE = PV_CURRENCY_CODE_IN
                     AND MATCH_ID = VN_MATCHID;
               ELSE
                  -- update match for internal match quality 2 {preadvice position}
                  UPDATE P_MATCH
                     SET INTERNAL_QUALITY2              = 'E',
                         MATCH_QUALITY                  = 'E',
                         STATUS                         = VV_STATUS,
                         PREDICT_STATUS_INTERNAL_FLAG   = 'Y',
                         LOWEST_POSITION_LEVEL          = PN_PREADVICE_POS_IN,
                         STATUS_USER                    = 'SYSTEM',
                         UPDATE_USER                    = 'SYSTEM',
                         STATUS_DATE                    = GLOBAL_VAR.SYS_DATE,
                         UPDATE_DATE                    = GLOBAL_VAR.SYS_DATE
                   WHERE HOST_ID = PV_HOST_ID_IN
                     AND ENTITY_ID = PV_ENTITY_ID_IN
                     AND CURRENCY_CODE = PV_CURRENCY_CODE_IN
                     AND MATCH_ID = VN_MATCHID;
               END IF;

               IF VV_DEBUG_MODE = 'Y'
               THEN
                  -- log matches into debug tables when debug mode is enabled
                  INSERT INTO DEBUG_MATCH_TARGETS(MOVEMENT_ID,
                                                  VALUE_DATE,
                                                  MATCH_STATUS,
                                                  MATCH_ID,
                                                  DESCRIPTION)
                       VALUES (R_PRE_ADVICE.MOVEMENT_ID,
                               GLOBAL_VAR.SYS_DATE,
                               R_PRE_ADVICE.MATCH_STATUS,
                               R_PRE_ADVICE.MATCH_ID,
                               'preadvice-c');

                  INSERT INTO DEBUG_MATCH_TARGETS(MOVEMENT_ID,
                                                  VALUE_DATE,
                                                  MATCH_STATUS,
                                                  MATCH_ID,
                                                  DESCRIPTION)
                       VALUES (PN_MOVEMENT_ID_IN,
                               GLOBAL_VAR.SYS_DATE,
                               NULL,
                               PN_MATCHID_ID_INOUT,
                               'source_movement-c');

                  COMMIT;
               END IF;

               -- update movement for match status, predict status, match id
               -- The below select query checks whether the matched movements has
               -- any OPEN movements. If exists then update the match status, predict status, match id
               -- for all the movements.
               SELECT COUNT(1)
                 INTO VN_OPN_MOV
                 FROM P_B_TARGET_MOVEMENTS A
                WHERE A.MATCH_ID = VN_MATCHID
                  AND EXISTS
                         (SELECT NULL
                            FROM P_MOVEMENT B
                           WHERE B.MATCH_ID = A.MATCH_ID
                             AND B.OPEN = 'Y');

               IF VN_OPN_MOV > 0
               THEN
                  VV_IS_OPEN   := 'Y';

                  UPDATE P_MOVEMENT
                     SET MATCH_ID         = VN_MATCHID,
                         MATCH_STATUS     = VV_STATUS,
                         UPDATE_DATE      = GLOBAL_VAR.SYS_DATE,
                         UPDATE_USER      = 'SYSTEM',
                         PREDICT_STATUS   = DECODE(POSITION_LEVEL, 6, PREDICT_STATUS, INITIAL_PREDICT_STATUS),
                         EXTRACT_STATUS   = 'E',
                         BOOKCODE         = DECODE(BOOKCODE_AVAIL,  'N', '',  'Y', BOOKCODE)
                   WHERE HOST_ID = PV_HOST_ID_IN
                     AND ENTITY_ID = PV_ENTITY_ID_IN
                     AND CURRENCY_CODE = PV_CURRENCY_CODE_IN
                     AND (MOVEMENT_ID = R_PRE_ADVICE.MOVEMENT_ID
                       OR MATCH_ID = VN_MATCHID);
               ELSE
                  UPDATE P_MOVEMENT
                     SET MATCH_ID         = VN_MATCHID,
                         MATCH_STATUS     = VV_STATUS,
                         UPDATE_DATE      = GLOBAL_VAR.SYS_DATE,
                         UPDATE_USER      = 'SYSTEM',
                         PREDICT_STATUS   = DECODE(POSITION_LEVEL, 6, PREDICT_STATUS, INITIAL_PREDICT_STATUS),
                         EXTRACT_STATUS   = 'E',
                         BOOKCODE         = DECODE(BOOKCODE_AVAIL,  'N', '',  'Y', BOOKCODE)
                   WHERE HOST_ID = PV_HOST_ID_IN
                     AND ENTITY_ID = PV_ENTITY_ID_IN
                     AND CURRENCY_CODE = PV_CURRENCY_CODE_IN
                     AND VALUE_DATE = PD_VALUE_DATE_IN
                     AND (MOVEMENT_ID = R_PRE_ADVICE.MOVEMENT_ID
                       OR MATCH_ID = VN_MATCHID);
               END IF;

               COMMIT;
            END IF;
         ELSE
            EXIT;
         END IF;
      END LOOP;

      -- close opened cursors
      IF PN_MATCHID_ID_INOUT = 0
      THEN
         CLOSE CR_PRE_ADVICE_MATCHED;
      ELSE
         CLOSE CR_PRE_ADVICE_UNMATCHED;
      END IF;

      -- assign out parameter variables
      IF VN_MATCHID = 0
      THEN
         PN_LOWEST_POS_OUT   := 0;
      ELSE
         PN_LOWEST_POS_OUT     := PN_PREADVICE_POS_IN;
         PN_MATCHID_ID_INOUT   := VN_MATCHID;
      END IF;

      PN_HIGHEST_POS_OUT     := VN_MATCH_HI_POS;

      -- update p_movement for pre-advice expected movements
      IF VV_PREADVICE_EXPECTED = 'Y'
     AND VN_MATCHID = 0
      THEN
         IF GV_REVERT_PRED_STATUS = 'Y'
         THEN
            UPDATE P_MOVEMENT
               SET PREDICT_STATUS   = INITIAL_PREDICT_STATUS
             WHERE HOST_ID = PV_HOST_ID_IN
               AND ENTITY_ID = PV_ENTITY_ID_IN
               AND CURRENCY_CODE = PV_CURRENCY_CODE_IN
               AND VALUE_DATE = PD_VALUE_DATE_IN
               AND MOVEMENT_ID = PN_MOVEMENT_ID_IN;

            COMMIT;
         END IF;
      END IF;
   EXCEPTION
      WHEN NO_DATA_FOUND
      THEN
         NULL;
      WHEN OTHERS
      THEN
         IF CR_PRE_ADVICE_MATCHED%ISOPEN
         THEN
            CLOSE CR_PRE_ADVICE_MATCHED;
         END IF;

         IF CR_PRE_ADVICE_UNMATCHED%ISOPEN
         THEN
            CLOSE CR_PRE_ADVICE_UNMATCHED;
         END IF;
   END SP_PREADVICE_MATCHING;


   -- ----------------------------------------------------------------------------------------------
   -- Check the reference in p_b_target_movements table whose match id is not null.
   -- When any references matched then return 'Y' otherwise return 'N'.
   -- ----------------------------------------------------------------------------------------------
   FUNCTION FN_CHECK_INNER_TGT_REFERENCE(PHOSTID             P_MOVEMENT.HOST_ID%TYPE,
                                         PENTITYID           P_MOVEMENT.ENTITY_ID%TYPE,
                                         PPOSITIONLEVEL   IN NUMBER,
                                         PMOVEMENTID      IN P_MOVEMENT.MOVEMENT_ID%TYPE)
      RETURN VARCHAR2
   IS
      VTGTEXISTS   VARCHAR2(1) := 'N';
   BEGIN
      -- get the count from p_b_target_movements when cross reference is matched with
      -- cross reference table for the supplied movement
      SELECT 'Y'
        INTO VTGTEXISTS
        FROM DUAL
       WHERE EXISTS
                (SELECT NULL
                   FROM P_REFERENCE_XREF O
                  WHERE O.HOST_ID = PHOSTID
                    AND O.ENTITY_ID = PENTITYID
                    AND O.MOVEMENT_ID = PMOVEMENTID
                    AND O.CROSS_REFERENCE IN (SELECT TR.CROSS_REFERENCE
                                                FROM P_B_TARGET_CROSS_REF TR, P_B_TARGET_MOVEMENTS TM
                                               WHERE TM.POS_LEVEL = PPOSITIONLEVEL
                                                 AND TM.MATCH_ID IS NOT NULL
                                                 AND TM.HOST_ID = PHOSTID
                                                 AND TM.ENTITY_ID = PENTITYID
                                                 AND TM.MOVEMENT_ID = PMOVEMENTID
                                                 AND TM.HOST_ID = TR.HOST_ID
                                                 AND TM.ENTITY_ID = TR.ENTITY_ID
                                                 AND TM.MOVEMENT_ID = TR.MOVEMENT_ID));


      RETURN VTGTEXISTS;
   EXCEPTION
      WHEN NO_DATA_FOUND
      THEN
         RETURN VTGTEXISTS;
   END FN_CHECK_INNER_TGT_REFERENCE;


   -- ----------------------------------------------------------------------------------------------
   -- TO FILL IN...
   -- ----------------------------------------------------------------------------------------------
   FUNCTION FN_REF_CHK_OTHERS(P_HOSTID               P_MOVEMENT.HOST_ID%TYPE,
                              P_ENTITYID             P_MOVEMENT.ENTITY_ID%TYPE,
                              P_CURRENCYCODE         P_MOVEMENT.CURRENCY_CODE%TYPE,
                              P_SOURCE_VALUE_DATE    P_MOVEMENT.VALUE_DATE%TYPE,
                              P_SOURCE_SIGN          P_MOVEMENT.SIGN%TYPE,
                              P_SOURCE_AMOUNT        P_MOVEMENT.AMOUNT%TYPE,
                              P_TOLERANCE            NUMBER)
      RETURN VARCHAR2
   IS
      REC_FOUND   VARCHAR2(1);
   BEGIN
      SELECT 'Y'
        INTO REC_FOUND
        FROM DUAL
       WHERE EXISTS
                (SELECT NULL
                   FROM P_MOVEMENT MOV, P_MOVEMENT MOV2
                  WHERE MOV.HOST_ID = P_HOSTID
                    AND MOV.ENTITY_ID = P_ENTITYID
                    AND MOV.CURRENCY_CODE = P_CURRENCYCODE
                    AND MOV.VALUE_DATE = P_SOURCE_VALUE_DATE
                    AND MOV.MATCH_STATUS = 'L'
                    AND MOV.POSITION_LEVEL < GN_MAX_INTERNAL_POS
                    AND MOV.PREDICT_STATUS IN ('E', 'I')
                    AND (MOV.AMOUNT BETWEEN ABS(ABS(P_SOURCE_AMOUNT) - P_TOLERANCE) AND ABS(ABS(P_SOURCE_AMOUNT) + P_TOLERANCE)
                     AND MOV.SIGN = P_SOURCE_SIGN)
                    AND MOV2.HOST_ID = MOV.HOST_ID
                    AND MOV2.ENTITY_ID = MOV.ENTITY_ID
                    AND MOV2.CURRENCY_CODE = MOV.CURRENCY_CODE
                    AND MOV2.VALUE_DATE = MOV.VALUE_DATE
                    AND MOV2.POSITION_LEVEL > GN_MAX_INTERNAL_POS
                    AND PK_MATCHING_PROCESS.FNISCROSSREFEXISTS(P_HOSTID,
                                                               P_ENTITYID,
                                                               P_CURRENCYCODE,
                                                               MOV.MOVEMENT_ID,
                                                               MOV2.MOVEMENT_ID) = 'Y'
                    AND MOV2.MATCH_STATUS IN ('L', 'M', 'C')
                    AND MOV2.PREDICT_STATUS IN ('I', 'E')
                    AND MOV2.AMOUNT = MOV.AMOUNT
                    AND MOV2.SIGN = MOV.SIGN);

      IF REC_FOUND = 'Y'
      THEN
         RETURN 0;
      ELSE
         RETURN 1;
      END IF;
   EXCEPTION
      WHEN NO_DATA_FOUND
      THEN
         RETURN 1;
      WHEN OTHERS
      THEN
         RETURN 1;
   END FN_REF_CHK_OTHERS;


   -- ----------------------------------------------------------------------------------------------
   -- This stored procedure is the core of the matching process.
   -- It selects source and targets and applies matching logic in them.
   -- ----------------------------------------------------------------------------------------------
   PROCEDURE SP_MATCHING_PROCEDURE(L_HOSTID                  IN     VARCHAR2,
                                   L_ENTITYID                IN     VARCHAR2,
                                   L_CURRENCYCODE            IN     VARCHAR,
                                   L_CASH_FILTER_THRSH       IN     NUMBER,
                                   L_SECURITY_FILTER_THRSH   IN     NUMBER,
                                   L_TOLERANCE               IN     NUMBER,
                                   L_EXCHANGERATE            IN     NUMBER,
                                   L_EXCHANGERATE_FORMAT     IN     NUMBER,
                                   L_VALUEDATE               IN     DATE,
                                   L_RESULT                     OUT VARCHAR2,
                                   PV_SUPPLEMENT_FLAG_IN     IN     VARCHAR2 DEFAULT 'N')
   IS
      -- Date Variables
      V_VALUE_DATE                     DATE;
      -- Numeric Variables
      L_TM_QUALITYFLAG                 NUMBER(2);
      L_TM_QUALITYFLAG_MIN             NUMBER(2);
      L_INT_QTY_P_MATCH                NUMBER(2) := 0;
      L_MATCHID                        NUMBER(12) := 0;
      L_MATCHID_PREV                   NUMBER(12) := 0;
      FLAG_C                           NUMBER(20) := 0;
      L_COUNT                          NUMBER(20) := 0;
      LINTQTYMATCHVAL                  NUMBER(2) := -1;
      VERRORCODE                       NUMBER;
      VN_MIN_MATCHED_PRE_ADV_POS       NUMBER;
      VN_RETN_MATCHID                  NUMBER := 0;
      VN_RETN_HI_POS                   NUMBER(1) := 0;
      VN_RETN_LOW_POS                  NUMBER(1) := 0;
      VN_TGT_POS1_QTY                  NUMBER := 0;
      VN_TGT_POS2_QTY                  NUMBER := 0;
      VN_TGT_POS3_QTY                  NUMBER := 0;
      VN_TGT_POS4_QTY                  NUMBER := 0;
      VN_TGT_POS5_QTY                  NUMBER := 0;
      VN_TGT_POS6_QTY                  NUMBER := 0;
      VN_TGT_POS7_QTY                  NUMBER := 0;
      VN_TGT_POS8_QTY                  NUMBER := 0;
      VN_TGT_POS9_QTY                  NUMBER := 0;
      VN_STATUS_POSITION               NUMBER;
      VN_SRC_POS_LEVEL                 NUMBER(1);
      VN_PREADV_STRATEGY               NUMBER(1);
      VN_TO_MATCH_STAGE                NUMBER(1);
      VN_INNER_TGT_EXTERNAL            INTEGER(1);
      VN_INNER_TGT_INTERNAL            INTEGER(1);
      VN_RETN_VALUE                    VARCHAR2(1) := 'N';
      VN_DAYS_TO_MATCH_AHEAD           INTEGER := 0;
      VN_DAYS_TO_MATCH_AHEAD_DEFAULT   INTEGER := 0;
      -- String Variables
      V_END_SOURCE                     CHAR(1) := 'N';
      V_END_TARGET                     CHAR(1) := 'N';
      V_JOB_STATUS                     CHAR(1) := 'N';
      V_AMT_TOTAL                      CHAR(1) := 'N';
      VV_SOURCE_EXIST                  CHAR(1) := 'N';
      VV_ENFORCE_REFERENCE             CHAR(1);
      VV_TARGET_POS_CHK                CHAR(1);
      -- 'B' [BOTH], 'H' [HIGHER],'L' [LOWER], 'S' [SUPPLEMENTARY] positions
      VV_RETN_STATUS                   CHAR(1);
      VV_RETN_QUALITY                  CHAR(1);
      L_FORCE_REFERENCE_ONLY           CHAR(1) := 'N';
      VV_SOURCE_POS_EXISTS             CHAR(1);
      VV_DEL_TARGET                    CHAR(1);
      VV_DEBUG_MODE                    CHAR(1);
      VN_TGT_INSERT                    CHAR(1);
      V_CURRENCY_CODE                  P_MOVEMENT.CURRENCY_CODE%TYPE := '';
      VV_ERR_LOC                       VARCHAR2(10) := 0;
      VERRORDESC                       S_ERROR_LOG.ERROR_DESC%TYPE;
      VUSERID                          S_ERROR_LOG.USER_ID%TYPE := 'SYSTEM';
      VIPADDRESS                       S_ERROR_LOG.IP_ADDRESS%TYPE := 'DB SERVER';
      VSOURCE                          S_ERROR_LOG.SOURCE%TYPE := 'Auto Matching - SP_Matching_Procedure';
      VV_BEN_ENRICHED                  P_B_TARGET_MOVEMENTS.BENEFICIARY_ID%TYPE;
      VV_PREADV_SEARCH_ALWAYS          VARCHAR2(1);
      VV_PREADVICE_SEARCH_POSITIONS    VARCHAR2(20);
      -- Column Anchored Variables
      V_CASH_FILTER_THRSH              P_MOVEMENT.AMOUNT%TYPE;
      V_SECURITY_FILTER_THRSH          P_MOVEMENT.AMOUNT%TYPE;
      VV_IS_OPEN                       VARCHAR2(1) := 'N';

      -- Cursor Declarations
      -- Cursor will select the source movement details for the input position level
      -- and match stage.
      -- Other Conditions:
      --   1. host/entity/currency should match with input values
      --   2. Should be an outstanding movement
      --   3. predict status should be either 'I' or 'E'
      --   4. to match date time should <= input date time
      --   5. Amount should be greater than threshold limit
      --   6. For EUR currency select movements of today date
      --   7. For Non-EUR currency select movements today + 7 days
      --   8. Select movements that have input as today and value date older than today
      CURSOR CR_INS_SOURCE(VN_POS_LVL_IN NUMBER, VN_TO_MATCH_STAGE_IN NUMBER)
      IS
           SELECT TAB.*
             FROM (SELECT A.HOST_ID, A.ENTITY_ID, A.CURRENCY_CODE,
                          A.MOVEMENT_ID, A.MATCH_ID, A.MATCH_STATUS,
                          A.PREDICT_STATUS, A.VALUE_DATE, A.POSITION_LEVEL,
                          DECODE(A.SIGN,  'D', -A.AMOUNT,  'C', A.AMOUNT) SOURCE_AMOUNT, A.COUNTERPARTY_ID, A.BENEFICIARY_ID,
                          A.CUSTODIAN_ID, A.BOOKCODE, A.ACCOUNT_ID,
                          A.INPUT_SOURCE, A.OPEN, A.SIGN,
                          A.TO_MATCH_STAGE, A.INITIAL_PREDICT_STATUS, A.MATCHING_PARTY
                     FROM P_MOVEMENT A
                    WHERE A.HOST_ID = L_HOSTID
                      AND A.ENTITY_ID = L_ENTITYID
                      AND A.CURRENCY_CODE = L_CURRENCYCODE
                      AND A.MATCH_STATUS = 'L'
                      AND (A.PREDICT_STATUS = 'E'
                        OR A.PREDICT_STATUS = 'I')
                      AND A.TO_MATCH_DATE <= L_VALUEDATE
                      AND A.TO_MATCH_STAGE = VN_TO_MATCH_STAGE_IN
                      AND A.AMOUNT >= DECODE(A.MOVEMENT_TYPE,  'C', V_CASH_FILTER_THRSH,  'U', V_SECURITY_FILTER_THRSH)
                      AND A.VALUE_DATE >= GV_GLOBAL_VAR_SYS_DATE
                      AND A.VALUE_DATE <= GV_GLOBAL_VAR_SYS_DATE + VN_DAYS_TO_MATCH_AHEAD
                      AND A.POSITION_LEVEL = VN_POS_LVL_IN
                   UNION
                   SELECT A.HOST_ID, A.ENTITY_ID, A.CURRENCY_CODE,
                          A.MOVEMENT_ID, A.MATCH_ID, A.MATCH_STATUS,
                          A.PREDICT_STATUS, A.VALUE_DATE, A.POSITION_LEVEL,
                          DECODE(A.SIGN,  'D', -A.AMOUNT,  'C', A.AMOUNT) SOURCE_AMOUNT, A.COUNTERPARTY_ID, A.BENEFICIARY_ID,
                          A.CUSTODIAN_ID, A.BOOKCODE, A.ACCOUNT_ID,
                          A.INPUT_SOURCE, A.OPEN, A.SIGN,
                          A.TO_MATCH_STAGE, A.INITIAL_PREDICT_STATUS, A.MATCHING_PARTY
                     FROM P_MOVEMENT A
                    WHERE A.HOST_ID = L_HOSTID
                      AND A.ENTITY_ID = L_ENTITYID
                      AND A.CURRENCY_CODE = L_CURRENCYCODE
                      AND MATCH_STATUS = 'L'
                      AND (PREDICT_STATUS = 'E'
                        OR PREDICT_STATUS = 'I')
                      AND TO_MATCH_DATE <= L_VALUEDATE
                      AND TO_MATCH_STAGE = VN_TO_MATCH_STAGE_IN
                      AND AMOUNT >= DECODE(MOVEMENT_TYPE,  'C', V_CASH_FILTER_THRSH,  'U', V_SECURITY_FILTER_THRSH)
                      AND TRUNC(INPUT_DATE) = GV_GLOBAL_VAR_SYS_DATE
                      AND A.CURRENCY_CODE != 'EUR'
                      AND A.VALUE_DATE < GV_GLOBAL_VAR_SYS_DATE
                      AND POSITION_LEVEL = VN_POS_LVL_IN) TAB
         ORDER BY TAB.VALUE_DATE ASC, TAB.SOURCE_AMOUNT DESC;

      -- Target cursor for reference matching
      CURSOR CR_INS_TARGET_REF_PASS(
         SOURCE_MOVEMENT_ID        NUMBER,
         SOURCE_VALUE_DATE         DATE,
         SOURCE_HIGHESTPOSLEVEL    NUMBER,
         SOURCE_AMOUNT             NUMBER,
         SOURCE_ACCOUNT_ID         VARCHAR2,
         SOURCE_COUNTERPARTY_ID    VARCHAR2,
         SOURCE_BENEFICIARY_ID     VARCHAR2,
         SOURCE_CUSTODIAN_ID       VARCHAR2,
         SOURCE_BOOKCODE           VARCHAR2,
         SOURCE_OPEN               VARCHAR2,
         VC_TARGET_POS_CHK         CHAR,
         CN_POS_THRESHOLD          NUMBER,
         SOURCE_MATCHINGPARTY      P_MOVEMENT.MATCHING_PARTY%TYPE)
      IS
           SELECT *
             FROM (SELECT A.HOST_ID,
                          A.ENTITY_ID,
                          A.CURRENCY_CODE,
                          A.MOVEMENT_ID,
                          A.MATCH_ID,
                          A.MATCH_STATUS,
                          A.PREDICT_STATUS,
                          A.VALUE_DATE,
                          A.POSITION_LEVEL,
                          DECODE(A.SIGN, 'D', -A.AMOUNT, A.AMOUNT) TARGET_AMOUNT,
                          A.COUNTERPARTY_ID,
                          A.BENEFICIARY_ID,
                          A.CUSTODIAN_ID,
                          A.BOOKCODE,
                          A.ACCOUNT_ID,
                          'Y' VALID,
                          NULL QUALITYFLAG,
                          PK_MATCHING_PROCESS.FN_UPDATE_P_B_MATQUAL_POSLEVEL(
                             A.HOST_ID,
                             A.ENTITY_ID,
                             V_CURRENCY_CODE,
                             DECODE(SOURCE_OPEN,  'N', SOURCE_VALUE_DATE,  'Y', GV_GLOBAL_VAR_SYS_DATE),
                             DECODE(A.OPEN,  'N', A.VALUE_DATE,  'Y', GV_GLOBAL_VAR_SYS_DATE),
                             SOURCE_AMOUNT,
                             DECODE(A.SIGN,  'D', -A.AMOUNT,  'C', A.AMOUNT),
                             L_TOLERANCE,
                             SOURCE_ACCOUNT_ID,
                             A.ACCOUNT_ID,
                             SOURCE_COUNTERPARTY_ID,
                             A.COUNTERPARTY_ID,
                             SOURCE_BENEFICIARY_ID,
                             A.BENEFICIARY_ID,
                             SOURCE_CUSTODIAN_ID,
                             A.CUSTODIAN_ID,
                             SOURCE_BOOKCODE,
                             A.BOOKCODE,
                             SOURCE_HIGHESTPOSLEVEL,
                             SOURCE_MOVEMENT_ID,
                             A.MOVEMENT_ID,
                             A.OPEN,
                             SOURCE_MATCHINGPARTY,
                             A.MATCHING_PARTY,
                             A.POSITION_LEVEL,
                             CN_POS_THRESHOLD)
                             L_TM_QUALITYFLAG,
                          A.MATCHING_PARTY,
                          A.OPEN,
                          A.INITIAL_PREDICT_STATUS
                     FROM P_MOVEMENT A
                    WHERE A.HOST_ID = L_HOSTID
                      AND A.ENTITY_ID = L_ENTITYID
                      AND A.CURRENCY_CODE = L_CURRENCYCODE
                      AND (A.VALUE_DATE = SOURCE_VALUE_DATE
                        OR (A.OPEN = 'Y'
                        AND A.VALUE_DATE < SOURCE_VALUE_DATE))
                      AND (A.MOVEMENT_ID IN (SELECT /*+ INDEX (xref_o IDX_P_REFERENCE_XREF_CROSS_REF ) */
                                                    XREF_O.MOVEMENT_ID
                                               FROM P_REFERENCE_XREF XREF_O
                                              WHERE XREF_O.CROSS_REFERENCE IN (SELECT /*+ INDEX (xref_i IDX_P_REFERENCE_XREF_MOVID) */
                                                                                      XREF_I.CROSS_REFERENCE
                                                                                 FROM P_REFERENCE_XREF XREF_I
                                                                                WHERE XREF_I.HOST_ID = XREF_O.HOST_ID
                                                                                  AND XREF_I.ENTITY_ID = XREF_O.ENTITY_ID
                                                                                  AND XREF_I.MOVEMENT_ID = SOURCE_MOVEMENT_ID)
                                                AND XREF_O.HOST_ID = L_HOSTID
                                                AND XREF_O.ENTITY_ID = L_ENTITYID
                                                AND XREF_O.MOVEMENT_ID != SOURCE_MOVEMENT_ID
                                                AND XREF_O.CURRENCY_CODE = L_CURRENCYCODE)
                       AND ((A.POSITION_LEVEL < SOURCE_HIGHESTPOSLEVEL
                         AND VC_TARGET_POS_CHK = 'L')
                         OR (A.POSITION_LEVEL != SOURCE_HIGHESTPOSLEVEL
                         AND VC_TARGET_POS_CHK = 'B')))
                      AND NOT EXISTS
                             (SELECT NULL
                                FROM P_PREV_MATCH D
                               WHERE D.ENTITY_ID = L_ENTITYID
                                 AND ((D.PREV_MATCHED_WITH = SOURCE_MOVEMENT_ID
                                   AND D.MOVEMENT_ID = A.MOVEMENT_ID)
                                   OR (D.PREV_MATCHED_WITH = A.MOVEMENT_ID
                                   AND D.MOVEMENT_ID = SOURCE_MOVEMENT_ID)))
                      AND (A.PREDICT_STATUS = 'E'
                        OR A.PREDICT_STATUS = 'I')
                      AND A.MATCH_STATUS != 'S'
                      AND ((A.MATCH_STATUS IN ('L', 'M')
                         OR (A.MATCH_STATUS = 'C'
                         AND EXISTS
                                (SELECT NULL
                                   FROM P_MATCH E
                                  WHERE E.HOST_ID = L_HOSTID
                                    AND E.ENTITY_ID = L_ENTITYID
                                    AND E.MATCH_ID = A.MATCH_ID
                                    AND ((E.MATCH_QUALITY NOT IN ('A', 'B'))
                                      OR 'X' =
                                            NVL(
                                               DECODE(SOURCE_HIGHESTPOSLEVEL,
                                                      2, E.INTERNAL_QUALITY2,
                                                      3, E.INTERNAL_QUALITY3,
                                                      4, E.INTERNAL_QUALITY4,
                                                      5, E.INTERNAL_QUALITY5,
                                                      6, E.INTERNAL_QUALITY6,
                                                      7, E.INTERNAL_QUALITY7,
                                                      8, E.INTERNAL_QUALITY8,
                                                      9, E.INTERNAL_QUALITY9),
                                               'X')))))))
            WHERE L_TM_QUALITYFLAG > 0
         ORDER BY POSITION_LEVEL DESC, L_TM_QUALITYFLAG DESC;

      -- Target cursor for amount/value date/reference
      CURSOR CR_INS_TARGET_VD_AMT_PASS(
         SOURCE_MOVEMENT_ID         NUMBER,
         SOURCE_VALUE_DATE          DATE,
         SOURCE_HIGHESTPOSLEVEL     NUMBER,
         SOURCE_AMOUNT              NUMBER,
         SOURCE_ACCOUNT_ID          VARCHAR2,
         SOURCE_COUNTERPARTY_ID     VARCHAR2,
         SOURCE_BENEFICIARY_ID      VARCHAR2,
         SOURCE_CUSTODIAN_ID        VARCHAR2,
         SOURCE_BOOKCODE            VARCHAR2,
         SOURCE_OPEN                VARCHAR2,
         SOURCE_SIGN                VARCHAR2,
         VC_FORCE_REFERENCE_ONLY    CHAR,
         VN_PRE_ADV_POS             NUMBER,
         CN_POS_THRESHOLD           NUMBER,
         SOURCE_MATCHINGPARTY       P_MOVEMENT.MATCHING_PARTY%TYPE)
      IS
           SELECT *
             FROM (SELECT P_MOV.HOST_ID,
                          P_MOV.ENTITY_ID,
                          P_MOV.CURRENCY_CODE,
                          P_MOV.MOVEMENT_ID,
                          P_MOV.MATCH_ID,
                          P_MOV.MATCH_STATUS,
                          P_MOV.PREDICT_STATUS,
                          P_MOV.VALUE_DATE,
                          P_MOV.POSITION_LEVEL,
                          DECODE(P_MOV.SIGN, 'D', -P_MOV.AMOUNT, P_MOV.AMOUNT) TARGET_AMOUNT,
                          P_MOV.COUNTERPARTY_ID,
                          P_MOV.BENEFICIARY_ID,
                          P_MOV.CUSTODIAN_ID,
                          P_MOV.BOOKCODE,
                          P_MOV.ACCOUNT_ID,
                          'Y' VALID,
                          NULL QUALITYFLAG,
                          PK_MATCHING_PROCESS.FN_UPDATE_P_B_MATQUAL_POSLEVEL(
                             P_MOV.HOST_ID,
                             P_MOV.ENTITY_ID,
                             V_CURRENCY_CODE,
                             DECODE(SOURCE_OPEN,  'N', SOURCE_VALUE_DATE,  'Y', GV_GLOBAL_VAR_SYS_DATE),
                             DECODE(P_MOV.OPEN,  'N', P_MOV.VALUE_DATE,  'Y', GV_GLOBAL_VAR_SYS_DATE),
                             SOURCE_AMOUNT,
                             DECODE(P_MOV.SIGN,  'D', -P_MOV.AMOUNT,  'C', P_MOV.AMOUNT),
                             L_TOLERANCE,
                             SOURCE_ACCOUNT_ID,
                             P_MOV.ACCOUNT_ID,
                             SOURCE_COUNTERPARTY_ID,
                             P_MOV.COUNTERPARTY_ID,
                             SOURCE_BENEFICIARY_ID,
                             P_MOV.BENEFICIARY_ID,
                             SOURCE_CUSTODIAN_ID,
                             P_MOV.CUSTODIAN_ID,
                             SOURCE_BOOKCODE,
                             P_MOV.BOOKCODE,
                             SOURCE_HIGHESTPOSLEVEL,
                             SOURCE_MOVEMENT_ID,
                             P_MOV.MOVEMENT_ID,
                             P_MOV.OPEN,
                             SOURCE_MATCHINGPARTY,
                             P_MOV.MATCHING_PARTY,
                             P_MOV.POSITION_LEVEL,
                             CN_POS_THRESHOLD)
                             L_TM_QUALITYFLAG,
                          P_MOV.MATCHING_PARTY,
                          P_MOV.OPEN,
                          P_MOV.INITIAL_PREDICT_STATUS
                     FROM (SELECT A.HOST_ID, A.ENTITY_ID, A.MOVEMENT_ID
                             FROM P_MOVEMENT A
                            WHERE A.HOST_ID = L_HOSTID
                              AND A.ENTITY_ID = L_ENTITYID
                              AND A.CURRENCY_CODE = L_CURRENCYCODE
                              AND (A.VALUE_DATE = SOURCE_VALUE_DATE
                                OR (A.OPEN = 'Y'
                                AND A.VALUE_DATE < SOURCE_VALUE_DATE))
                              AND (A.AMOUNT BETWEEN ABS(ABS(SOURCE_AMOUNT) - L_TOLERANCE) AND ABS(ABS(SOURCE_AMOUNT) + L_TOLERANCE)
                               AND A.SIGN = SOURCE_SIGN
                               AND VC_FORCE_REFERENCE_ONLY = 'N'
                               AND A.POSITION_LEVEL != SOURCE_HIGHESTPOSLEVEL)
                           UNION
                           SELECT A.HOST_ID, A.ENTITY_ID, A.MOVEMENT_ID
                             FROM P_MOVEMENT A
                            WHERE A.HOST_ID = L_HOSTID
                              AND A.ENTITY_ID = L_ENTITYID
                              AND A.CURRENCY_CODE = L_CURRENCYCODE
                              AND (A.VALUE_DATE = SOURCE_VALUE_DATE
                                OR (A.OPEN = 'Y'
                                AND A.VALUE_DATE < SOURCE_VALUE_DATE))
                              AND (A.MOVEMENT_ID IN (SELECT XREF_O.MOVEMENT_ID
                                                       FROM P_REFERENCE_XREF XREF_O
                                                            INNER JOIN P_REFERENCE_XREF XREF_I
                                                               ON (XREF_O.CROSS_REFERENCE = XREF_I.CROSS_REFERENCE
                                                               AND XREF_O.ENTITY_ID = XREF_I.ENTITY_ID
                                                               AND XREF_O.CURRENCY_CODE = XREF_I.CURRENCY_CODE)
                                                      WHERE XREF_I.MOVEMENT_ID = SOURCE_MOVEMENT_ID
                                                        AND XREF_I.ENTITY_ID = L_ENTITYID
                                                        AND XREF_I.CURRENCY_CODE = L_CURRENCYCODE
                                                        AND XREF_O.MOVEMENT_ID != SOURCE_MOVEMENT_ID)
                               AND (A.POSITION_LEVEL < SOURCE_HIGHESTPOSLEVEL))) MOV,
                          P_MOVEMENT P_MOV
                    WHERE MOV.HOST_ID = P_MOV.HOST_ID
                      AND MOV.ENTITY_ID = P_MOV.ENTITY_ID
                      AND MOV.MOVEMENT_ID = P_MOV.MOVEMENT_ID
                      AND (P_MOV.PREDICT_STATUS = 'E'
                        OR P_MOV.PREDICT_STATUS = 'I')
                      AND NOT EXISTS
                             (SELECT NULL
                                FROM P_PREV_MATCH D
                               WHERE D.ENTITY_ID = L_ENTITYID
                                 AND ((D.PREV_MATCHED_WITH = SOURCE_MOVEMENT_ID
                                   AND D.MOVEMENT_ID = P_MOV.MOVEMENT_ID)
                                   OR (D.PREV_MATCHED_WITH = P_MOV.MOVEMENT_ID
                                   AND D.MOVEMENT_ID = SOURCE_MOVEMENT_ID)))
                      AND (P_MOV.MATCH_STATUS != 'S')
                      AND ((P_MOV.MATCH_STATUS = 'L')
                        OR (P_MOV.MATCH_STATUS = 'M'
                        AND P_MOV.POSITION_LEVEL != VN_PRE_ADV_POS)
                        OR ((P_MOV.MATCH_STATUS = 'C')
                        AND EXISTS
                               (SELECT NULL
                                  FROM P_MATCH E
                                 WHERE E.HOST_ID = L_HOSTID
                                   AND E.ENTITY_ID = L_ENTITYID
                                   AND E.MATCH_ID = P_MOV.MATCH_ID
                                   AND 'X' =
                                          NVL(
                                             DECODE(SOURCE_HIGHESTPOSLEVEL,
                                                    2, E.INTERNAL_QUALITY2,
                                                    3, E.INTERNAL_QUALITY3,
                                                    4, E.INTERNAL_QUALITY4,
                                                    5, E.INTERNAL_QUALITY5,
                                                    6, E.INTERNAL_QUALITY6,
                                                    7, E.INTERNAL_QUALITY7,
                                                    8, E.INTERNAL_QUALITY8,
                                                    9, E.INTERNAL_QUALITY9),
                                             'X')))))
            WHERE L_TM_QUALITYFLAG > 0
         ORDER BY POSITION_LEVEL DESC, L_TM_QUALITYFLAG DESC;

      -- Target cursor for supplementary pass
      CURSOR CR_INSERT_TARGET_SUPP_PASS(
         SOURCE_MOVEMENT_ID        NUMBER,
         SOURCE_VALUE_DATE         DATE,
         SOURCE_HIGHESTPOSLEVEL    NUMBER,
         SOURCE_AMOUNT             NUMBER,
         SOURCE_ACCOUNT_ID         VARCHAR2,
         SOURCE_COUNTERPARTY_ID    VARCHAR2,
         SOURCE_BENEFICIARY_ID     VARCHAR2,
         SOURCE_CUSTODIAN_ID       VARCHAR2,
         SOURCE_BOOKCODE           VARCHAR2,
         SOURCE_OPEN               VARCHAR2,
         CN_POS_THRESHOLD          NUMBER,
         SOURCE_MATCHINGPARTY      P_MOVEMENT.MATCHING_PARTY%TYPE)
      IS
           SELECT *
             FROM (SELECT A.HOST_ID,
                          A.ENTITY_ID,
                          A.CURRENCY_CODE,
                          A.MOVEMENT_ID,
                          A.MATCH_ID,
                          A.MATCH_STATUS,
                          A.PREDICT_STATUS,
                          A.VALUE_DATE,
                          A.POSITION_LEVEL,
                          DECODE(A.SIGN, 'D', -A.AMOUNT, A.AMOUNT) TARGET_AMOUNT,
                          A.COUNTERPARTY_ID,
                          A.BENEFICIARY_ID,
                          A.CUSTODIAN_ID,
                          A.BOOKCODE,
                          A.ACCOUNT_ID,
                          'Y' VALID,
                          NULL QUALITYFLAG,
                          PK_MATCHING_PROCESS.FN_UPDATE_P_B_MATQUAL_POSLEVEL(
                             A.HOST_ID,
                             A.ENTITY_ID,
                             V_CURRENCY_CODE,
                             DECODE(A.OPEN,  'N', A.VALUE_DATE,  'Y', GV_GLOBAL_VAR_SYS_DATE),
                             DECODE(SOURCE_OPEN,  'N', SOURCE_VALUE_DATE,  'Y', GV_GLOBAL_VAR_SYS_DATE),
                             DECODE(A.SIGN,  'D', -A.AMOUNT,  'C', A.AMOUNT),
                             SOURCE_AMOUNT,
                             L_TOLERANCE,
                             A.ACCOUNT_ID,
                             SOURCE_ACCOUNT_ID,
                             A.COUNTERPARTY_ID,
                             SOURCE_COUNTERPARTY_ID,
                             A.BENEFICIARY_ID,
                             SOURCE_BENEFICIARY_ID,
                             A.CUSTODIAN_ID,
                             SOURCE_CUSTODIAN_ID,
                             A.BOOKCODE,
                             SOURCE_BOOKCODE,
                             SOURCE_HIGHESTPOSLEVEL,
                             A.MOVEMENT_ID,
                             SOURCE_MOVEMENT_ID,
                             SOURCE_OPEN,
                             SOURCE_MATCHINGPARTY,
                             A.MATCHING_PARTY,
                             A.POSITION_LEVEL,
                             CN_POS_THRESHOLD)
                             L_TM_QUALITYFLAG,
                          A.MATCHING_PARTY,
                          A.OPEN,
                          A.INITIAL_PREDICT_STATUS
                     FROM P_MOVEMENT A
                    WHERE A.HOST_ID = L_HOSTID
                      AND A.ENTITY_ID = L_ENTITYID
                      AND A.CURRENCY_CODE = L_CURRENCYCODE
                      AND (A.VALUE_DATE = SOURCE_VALUE_DATE
                        OR (A.OPEN = 'Y'
                        AND A.VALUE_DATE < SOURCE_VALUE_DATE))
                      AND (A.MOVEMENT_ID IN (SELECT /*+ INDEX (xref_o IDX_P_REFERENCE_XREF_CROSS_REF ) */
                                                    XREF_O.MOVEMENT_ID
                                               FROM P_REFERENCE_XREF XREF_O
                                              WHERE XREF_O.CROSS_REFERENCE IN (SELECT /*+ INDEX (xref_i IDX_P_REFERENCE_XREF_MOVID) */
                                                                                      XREF_I.CROSS_REFERENCE
                                                                                 FROM P_REFERENCE_XREF XREF_I
                                                                                WHERE XREF_I.HOST_ID = XREF_O.HOST_ID
                                                                                  AND XREF_I.ENTITY_ID = XREF_O.ENTITY_ID
                                                                                  AND XREF_I.MOVEMENT_ID = SOURCE_MOVEMENT_ID)
                                                AND XREF_O.HOST_ID = L_HOSTID
                                                AND XREF_O.ENTITY_ID = L_ENTITYID
                                                AND XREF_O.MOVEMENT_ID != SOURCE_MOVEMENT_ID
                                                AND XREF_O.CURRENCY_CODE = L_CURRENCYCODE))
                      AND A.POSITION_LEVEL != SOURCE_HIGHESTPOSLEVEL
                      AND NOT EXISTS
                             (SELECT NULL
                                FROM P_PREV_MATCH D
                               WHERE D.ENTITY_ID = L_ENTITYID
                                 AND ((D.PREV_MATCHED_WITH = SOURCE_MOVEMENT_ID
                                   AND D.MOVEMENT_ID = A.MOVEMENT_ID)
                                   OR (D.PREV_MATCHED_WITH = A.MOVEMENT_ID
                                   AND D.MOVEMENT_ID = SOURCE_MOVEMENT_ID)))
                      AND (A.PREDICT_STATUS = 'E'
                        OR A.PREDICT_STATUS = 'I')
                      AND EXISTS
                             (SELECT NULL
                                FROM P_MATCH_QUALITY T
                               WHERE T.HOST_ID = A.HOST_ID
                                 AND T.ENTITY_ID = A.ENTITY_ID
                                 AND (T.CURRENCY_CODE = A.CURRENCY_CODE
                                   OR T.CURRENCY_CODE = '*')
                                 AND T.POSITION_LEVEL = A.POSITION_LEVEL)
                      AND NOT EXISTS
                             (SELECT NULL
                                FROM P_MATCH_ACTION MA
                               WHERE MA.HOST_ID = A.HOST_ID
                                 AND MA.ENTITY_ID = A.ENTITY_ID
                                 AND (MA.CURRENCY_CODE = A.CURRENCY_CODE
                                   OR MA.CURRENCY_CODE = '*')
                                 AND MA.POSITION_LEVEL = A.POSITION_LEVEL
                                 AND (MA.MATCH_ACTION_A = 'N'
                                  AND MA.MATCH_ACTION_A = MA.MATCH_ACTION_B
                                  AND MA.MATCH_ACTION_B = MA.MATCH_ACTION_C
                                  AND MA.MATCH_ACTION_C = MA.MATCH_ACTION_D
                                  AND MA.MATCH_ACTION_D = MA.MATCH_ACTION_E))
                      AND A.MATCH_STATUS != 'S'
                      AND ((A.MATCH_STATUS IN ('L', 'M')
                         OR (A.MATCH_STATUS = 'C'
                         AND EXISTS
                                (SELECT NULL
                                   FROM P_MATCH E
                                  WHERE E.HOST_ID = L_HOSTID
                                    AND E.ENTITY_ID = L_ENTITYID
                                    AND E.MATCH_ID = A.MATCH_ID
                                    AND ((E.MATCH_QUALITY NOT IN ('A', 'B'))
                                      OR 'X' =
                                            NVL(
                                               DECODE(SOURCE_HIGHESTPOSLEVEL,
                                                      2, E.INTERNAL_QUALITY2,
                                                      3, E.INTERNAL_QUALITY3,
                                                      4, E.INTERNAL_QUALITY4,
                                                      5, E.INTERNAL_QUALITY5,
                                                      6, E.INTERNAL_QUALITY6,
                                                      7, E.INTERNAL_QUALITY7,
                                                      8, E.INTERNAL_QUALITY8,
                                                      9, E.INTERNAL_QUALITY9),
                                               'X')))))))
            WHERE L_TM_QUALITYFLAG = 5
         ORDER BY MOVEMENT_ID;

      -- Target cursor for preadvice
      CURSOR CR_INS_TARGET_PREADVICE(
         SOURCE_MOVEMENT_ID        NUMBER,
         SOURCE_VALUE_DATE         DATE,
         SOURCE_HIGHESTPOSLEVEL    NUMBER,
         SOURCE_AMOUNT             NUMBER,
         SOURCE_ACCOUNT_ID         VARCHAR2,
         SOURCE_COUNTERPARTY_ID    VARCHAR2,
         SOURCE_BENEFICIARY_ID     VARCHAR2,
         SOURCE_CUSTODIAN_ID       VARCHAR2,
         SOURCE_BOOKCODE           VARCHAR2,
         SOURCE_OPEN               VARCHAR2,
         SOURCE_SIGN               VARCHAR2,
         VN_PRE_ADV_POS            NUMBER,
         CN_POS_THRESHOLD          NUMBER,
         SOURCE_MATCHINGPARTY      P_MOVEMENT.MATCHING_PARTY%TYPE)
      IS
           SELECT *
             FROM (SELECT A.HOST_ID,
                          A.ENTITY_ID,
                          A.CURRENCY_CODE,
                          A.MOVEMENT_ID,
                          A.MATCH_ID,
                          A.MATCH_STATUS,
                          A.PREDICT_STATUS,
                          A.VALUE_DATE,
                          A.POSITION_LEVEL,
                          DECODE(A.SIGN, 'D', -A.AMOUNT, A.AMOUNT) TARGET_AMOUNT,
                          A.COUNTERPARTY_ID,
                          A.BENEFICIARY_ID,
                          A.CUSTODIAN_ID,
                          A.BOOKCODE,
                          A.ACCOUNT_ID,
                          'Y' VALID,
                          NULL QUALITYFLAG,
                          PK_MATCHING_PROCESS.FN_UPDATE_P_B_MATQUAL_POSLEVEL(
                             A.HOST_ID,
                             A.ENTITY_ID,
                             V_CURRENCY_CODE,
                             DECODE(SOURCE_OPEN,  'N', SOURCE_VALUE_DATE,  'Y', GV_GLOBAL_VAR_SYS_DATE),
                             DECODE(A.OPEN,  'N', A.VALUE_DATE,  'Y', GV_GLOBAL_VAR_SYS_DATE),
                             SOURCE_AMOUNT,
                             DECODE(A.SIGN,  'D', -A.AMOUNT,  'C', A.AMOUNT),
                             L_TOLERANCE,
                             SOURCE_ACCOUNT_ID,
                             A.ACCOUNT_ID,
                             SOURCE_COUNTERPARTY_ID,
                             A.COUNTERPARTY_ID,
                             SOURCE_BENEFICIARY_ID,
                             A.BENEFICIARY_ID,
                             SOURCE_CUSTODIAN_ID,
                             A.CUSTODIAN_ID,
                             SOURCE_BOOKCODE,
                             A.BOOKCODE,
                             SOURCE_HIGHESTPOSLEVEL,
                             SOURCE_MOVEMENT_ID,
                             A.MOVEMENT_ID,
                             A.OPEN,
                             SOURCE_MATCHINGPARTY,
                             A.MATCHING_PARTY,
                             A.POSITION_LEVEL,
                             CN_POS_THRESHOLD)
                             L_TM_QUALITYFLAG,
                          A.MATCHING_PARTY,
                          A.OPEN,
                          A.INITIAL_PREDICT_STATUS
                     FROM P_MOVEMENT A
                    WHERE A.HOST_ID = L_HOSTID
                      AND A.ENTITY_ID = L_ENTITYID
                      AND A.CURRENCY_CODE = L_CURRENCYCODE
                      AND A.VALUE_DATE = SOURCE_VALUE_DATE
                      AND ((A.AMOUNT BETWEEN ABS(ABS(SOURCE_AMOUNT) - L_TOLERANCE) AND ABS(ABS(SOURCE_AMOUNT) + L_TOLERANCE)
                        AND A.SIGN = SOURCE_SIGN
                        AND A.POSITION_LEVEL != SOURCE_HIGHESTPOSLEVEL))
                      AND NOT EXISTS
                             (SELECT NULL
                                FROM P_PREV_MATCH D
                               WHERE D.ENTITY_ID = L_ENTITYID
                                 AND ((D.PREV_MATCHED_WITH = SOURCE_MOVEMENT_ID
                                   AND D.MOVEMENT_ID = A.MOVEMENT_ID)
                                   OR (D.PREV_MATCHED_WITH = A.MOVEMENT_ID
                                   AND D.MOVEMENT_ID = SOURCE_MOVEMENT_ID)))
                      AND (A.PREDICT_STATUS = 'E'
                        OR A.PREDICT_STATUS = 'I')
                      AND A.MATCH_STATUS != 'S'
                      AND ((A.MATCH_STATUS = 'L')
                        OR (A.MATCH_STATUS = 'M'
                        AND A.POSITION_LEVEL != VN_PRE_ADV_POS)
                        OR ((A.MATCH_STATUS = 'C')
                        AND EXISTS
                               (SELECT NULL
                                  FROM P_MATCH E
                                 WHERE E.HOST_ID = L_HOSTID
                                   AND E.ENTITY_ID = L_ENTITYID
                                   AND E.MATCH_ID = A.MATCH_ID
                                   AND 'X' =
                                          NVL(
                                             DECODE(SOURCE_HIGHESTPOSLEVEL,
                                                    2, E.INTERNAL_QUALITY2,
                                                    3, E.INTERNAL_QUALITY3,
                                                    4, E.INTERNAL_QUALITY4,
                                                    5, E.INTERNAL_QUALITY5,
                                                    6, E.INTERNAL_QUALITY6,
                                                    7, E.INTERNAL_QUALITY7,
                                                    8, E.INTERNAL_QUALITY8,
                                                    9, E.INTERNAL_QUALITY9),
                                             'X')))))
            WHERE L_TM_QUALITYFLAG > 0
         ORDER BY POSITION_LEVEL DESC, L_TM_QUALITYFLAG DESC;

      R_INS_SOURCE                     CR_INS_SOURCE%ROWTYPE;
   BEGIN -- BEGIN OF PROCEDURE
      VV_ERR_LOC   := '10';

      -- get cash and securities filter
      IF L_EXCHANGERATE_FORMAT = 1
      THEN
         VV_ERR_LOC                := '20';
         V_CASH_FILTER_THRSH       := L_CASH_FILTER_THRSH * L_EXCHANGERATE;
         V_SECURITY_FILTER_THRSH   := L_SECURITY_FILTER_THRSH * L_EXCHANGERATE;
      ELSIF L_EXCHANGERATE_FORMAT = 2
      THEN
         VV_ERR_LOC                := '30';
         V_CASH_FILTER_THRSH       := L_CASH_FILTER_THRSH / L_EXCHANGERATE;
         V_SECURITY_FILTER_THRSH   := L_SECURITY_FILTER_THRSH / L_EXCHANGERATE;
      END IF;

      VV_ERR_LOC   := '40';

      -- identify the pre-advice position level for the input entity
      SELECT PREADVICE_POSITION
        INTO GN_PRE_ADVICE_POS
        FROM S_ENTITY
       WHERE HOST_ID = L_HOSTID
         AND ENTITY_ID = L_ENTITYID;

      -- get the max internal position
      VV_ERR_LOC   := '50';
      GN_MAX_INTERNAL_POS      :=
         TO_NUMBER(PK_APPLICATION.FN_GET_PARAMETER_VALUE(L_ENTITYID,
                                                         'MATCHING',
                                                         'POS_LEVEL_THRESHOLD',
                                                         '6'));
      -- identify the matching running in debug mode or not
      VV_DEBUG_MODE      :=
         PK_APPLICATION.FN_GET_PARAMETER_VALUE(L_ENTITYID,
                                               'MATCHING',
                                               'DEBUG',
                                               'N');

      VV_PREADV_SEARCH_ALWAYS      :=
         PK_APPLICATION.FN_GET_PARAMETER_VALUE(L_ENTITYID,
                                               'MATCHING',
                                               'PREADVICE_SEARCH_ALWAYS',
                                               'N');

      -- get the pre-advice predict strategy
      VN_PREADV_STRATEGY      :=
         TO_NUMBER(PK_APPLICATION.FN_GET_PARAMETER_VALUE(L_ENTITYID,
                                                         'MATCHING',
                                                         'PREADVICE_PREDICT_STRATEGY',
                                                         '1'));

      -- get the linked account hierarchical level
      GN_ACC_LINK_EXEMTION_LEVEL      :=
         TO_NUMBER(PK_APPLICATION.FN_GET_PARAMETER_VALUE(L_ENTITYID,
                                                         'MATCHING',
                                                         'ACC_LINKING_EXEMPTION_LEVEL',
                                                         '7'));

      -- get the reference enforcement applicable or not.
      VV_ENFORCE_REFERENCE      :=
         PK_APPLICATION.FN_GET_PARAMETER_VALUE(L_ENTITYID,
                                               'MATCHING',
                                               'ENFORCE_REFERENCE_SELECTION',
                                               'N');

      -- get the positions that are all search the pre-advices
      VV_PREADVICE_SEARCH_POSITIONS      :=
         PK_APPLICATION.FN_GET_PARAMETER_VALUE(L_ENTITYID,
                                               'MATCHING',
                                               'PREADVICE_SEARCH_POSITIONS',
                                               '6,4');

      VV_ERR_LOC   := '60';

      -- Get the number of days ahead to match on a currency basis
      -- Default to zero for EUR and 7 for other currencies (to maintain the current functionality)
      IF L_CURRENCYCODE = 'EUR'
      THEN
         VN_DAYS_TO_MATCH_AHEAD_DEFAULT   := 0;
      ELSE
         VN_DAYS_TO_MATCH_AHEAD_DEFAULT   := 7;
      END IF;

      VN_DAYS_TO_MATCH_AHEAD      :=
         TO_NUMBER(PK_APPLICATION.FN_GET_PARAMETER_VALUE(L_ENTITYID,
                                                         'MATCHING',
                                                         L_CURRENCYCODE || '_DAYS_AHEAD',
                                                         TO_CHAR(VN_DAYS_TO_MATCH_AHEAD_DEFAULT)));
      VV_ERR_LOC   := '60';

      -- cntr1 used to loop for the source movements selection for one position and one stage at time.
      -- positions determined by case statement inside the FOR LOOP.
      FOR CNTR1 IN 1 .. 11
      LOOP
         VN_TO_MATCH_STAGE      := 1;

         CASE CNTR1
            WHEN 1
            THEN
               VN_SRC_POS_LEVEL   := GN_MAX_INTERNAL_POS;
            WHEN 2
            THEN
               IF GN_MAX_INTERNAL_POS = 9
               THEN
                  VN_SRC_POS_LEVEL   := 8;
               ELSE
                  VN_SRC_POS_LEVEL   := 9;
               END IF;
            WHEN 3
            THEN
               IF GN_MAX_INTERNAL_POS = 8
               THEN
                  VN_SRC_POS_LEVEL   := 7;
               ELSE
                  VN_SRC_POS_LEVEL   := 8;
               END IF;
            WHEN 4
            THEN
               IF GN_MAX_INTERNAL_POS = 7
               THEN
                  VN_SRC_POS_LEVEL   := 6;
               ELSE
                  VN_SRC_POS_LEVEL   := 7;
               END IF;
            WHEN 5
            THEN
               IF GN_MAX_INTERNAL_POS = 6
               THEN
                  VN_SRC_POS_LEVEL   := 5;
               ELSE
                  VN_SRC_POS_LEVEL   := 6;
               END IF;
            WHEN 6
            THEN
               IF GN_MAX_INTERNAL_POS = 5
               THEN
                  VN_SRC_POS_LEVEL   := 4;
               ELSE
                  VN_SRC_POS_LEVEL   := 5;
               END IF;
            WHEN 7
            THEN
               IF GN_MAX_INTERNAL_POS = 4
               THEN
                  VN_SRC_POS_LEVEL   := 3;
               ELSE
                  VN_SRC_POS_LEVEL   := 4;
               END IF;
            WHEN 8
            THEN
               IF GN_PRE_ADVICE_POS = 1
               THEN
                  IF GN_MAX_INTERNAL_POS = 3
                  THEN
                     VN_SRC_POS_LEVEL   := 2;
                  ELSE
                     VN_SRC_POS_LEVEL   := 3;
                  END IF;
               ELSE
                  IF GN_MAX_INTERNAL_POS = 3
                  THEN
                     VN_SRC_POS_LEVEL   := 1;
                  ELSE
                     VN_SRC_POS_LEVEL   := 3;
                  END IF;
               END IF;
            WHEN 9
            THEN
               VN_SRC_POS_LEVEL    := GN_MAX_INTERNAL_POS;
               VN_TO_MATCH_STAGE   := 2;
            WHEN 10
            THEN
               VN_SRC_POS_LEVEL   := GN_PRE_ADVICE_POS;
            WHEN 11
            THEN
               -- Added for match the highest position levels that are all o/s movements and
               -- already crossed the normal to_match_stage (i.e greater than stage 1)
               -- and they may have reference agreement with current matched movements
               VN_SRC_POS_LEVEL    := GN_MAX_POS_OTHER_STAGE;
               VN_TO_MATCH_STAGE   := 2;
         END CASE;

         VV_SOURCE_POS_EXISTS   := 'N';

         -- check match quality exist for that source position
         BEGIN
            SELECT DISTINCT 'Y'
              INTO VV_SOURCE_POS_EXISTS
              FROM P_MATCH_QUALITY A
             WHERE HOST_ID = L_HOSTID
               AND ENTITY_ID = L_ENTITYID
               AND (CURRENCY_CODE = L_CURRENCYCODE
                 OR CURRENCY_CODE = '*')
               AND POSITION_LEVEL = VN_SRC_POS_LEVEL;
         EXCEPTION
            WHEN NO_DATA_FOUND
            THEN
               VV_SOURCE_POS_EXISTS   := 'N';
            WHEN TOO_MANY_ROWS
            THEN
               VV_SOURCE_POS_EXISTS   := 'Y';
         END;

         -- match quality does not exist then process source for next position
         -- otherwise proceed with current source position.
         IF VV_SOURCE_POS_EXISTS = 'Y'
         THEN
            -- open the source cursor
            OPEN CR_INS_SOURCE(VN_SRC_POS_LEVEL, VN_TO_MATCH_STAGE);

            LOOP
               BEGIN
                  -- fetch current record from source cursor
                  FETCH CR_INS_SOURCE INTO R_INS_SOURCE;

                  EXIT WHEN CR_INS_SOURCE%NOTFOUND;

                  -- initialize global variable
                  VN_TGT_POS1_QTY         := 0;
                  VN_TGT_POS2_QTY         := 0;
                  VN_TGT_POS3_QTY         := 0;
                  VN_TGT_POS4_QTY         := 0;
                  VN_TGT_POS5_QTY         := 0;
                  VN_TGT_POS6_QTY         := 0;
                  VN_TGT_POS7_QTY         := 0;
                  VN_TGT_POS8_QTY         := 0;
                  VN_TGT_POS9_QTY         := 0;
                  VV_SOURCE_EXIST         := 'N';
                  VN_RETN_MATCHID         := 0;
                  VV_RETN_STATUS          := 'X';
                  VV_RETN_QUALITY         := 'X';
                  VN_RETN_HI_POS          := 0;
                  VN_RETN_LOW_POS         := 0;
                  VN_STATUS_POSITION      := R_INS_SOURCE.POSITION_LEVEL;
                  VN_INNER_TGT_INTERNAL   := 0;
                  VN_INNER_TGT_EXTERNAL   := 0;
                  V_END_SOURCE            := 'N';
                  VV_BEN_ENRICHED         := '0';
                  VV_DEL_TARGET           := 'N';
                  VV_PREADVICE_EXPECTED   := 'N';
                  VV_ERR_LOC              := '150';

                  BEGIN
                     FLAG_C            := 0;
                     LINTQTYMATCHVAL   := -1;

                     -- check the matching process is enabled
                     -- when enabled continue processing
                     -- otherwise disable return the process
                     BEGIN
                        VV_ERR_LOC   := '160';

                        SELECT 'Y'
                          INTO V_JOB_STATUS
                          FROM S_SCHEDULER
                         WHERE HOST_ID = L_HOSTID
                           AND JOB_ID = 3
                           AND JOB_STATUS = 'E';
                     EXCEPTION
                        WHEN NO_DATA_FOUND
                        THEN
                           VV_ERR_LOC   := '170';

                           BEGIN
                              SELECT 'Y'
                                INTO V_JOB_STATUS
                                FROM S_SCHEDULER
                               WHERE HOST_ID = L_HOSTID
                                 AND JOB_ID = 3
                                 AND JOB_STATUS = 'D';

                              VV_ERR_LOC   := '180';
                              SP_REMOVE_P_B_TARGETS(R_INS_SOURCE.HOST_ID,
                                                    R_INS_SOURCE.ENTITY_ID,
                                                    R_INS_SOURCE.CURRENCY_CODE,
                                                    'Y',
                                                    0,
                                                    'D');
                              L_RESULT     := 0;
                              RETURN;
                           END;
                     END;

                     VV_ERR_LOC        := '190';

                     -- do soft lock by storing the source movement
                     -- into p_movement_lock table
                     BEGIN
                        SP_ADD_P_B_TARGETS(R_INS_SOURCE.MOVEMENT_ID,
                                           R_INS_SOURCE.HOST_ID,
                                           L_ENTITYID,
                                           L_CURRENCYCODE,
                                           NULL,
                                           NULL,
                                           R_INS_SOURCE.PREDICT_STATUS,
                                           R_INS_SOURCE.POSITION_LEVEL,
                                           R_INS_SOURCE.VALUE_DATE,
                                           R_INS_SOURCE.COUNTERPARTY_ID,
                                           R_INS_SOURCE.BENEFICIARY_ID,
                                           R_INS_SOURCE.CUSTODIAN_ID,
                                           R_INS_SOURCE.BOOKCODE,
                                           R_INS_SOURCE.SOURCE_AMOUNT,
                                           NULL,
                                           NULL,
                                           R_INS_SOURCE.ACCOUNT_ID,
                                           'Y', -- For p_movement_lock table
                                           'N', -- For p_b_target_movements table
                                           R_INS_SOURCE.MATCHING_PARTY,
                                           R_INS_SOURCE.INITIAL_PREDICT_STATUS);
                        VV_ERR_LOC   := '200';

                        -- increment to source movements to match stage and date
                        IF PV_SUPPLEMENT_FLAG_IN != 'Y'
                        THEN
                           VV_ERR_LOC   := '210';

                           UPDATE P_MOVEMENT
                              SET TO_MATCH_STAGE   = TO_MATCH_STAGE + 1,
                                  TO_MATCH_DATE    = GLOBAL_VAR.SYS_DATE + VA_TO_MATCH_STAGE(R_INS_SOURCE.TO_MATCH_STAGE)
                            WHERE HOST_ID = R_INS_SOURCE.HOST_ID
                              AND ENTITY_ID = R_INS_SOURCE.ENTITY_ID
                              AND CURRENCY_CODE = R_INS_SOURCE.CURRENCY_CODE
                              AND MOVEMENT_ID = R_INS_SOURCE.MOVEMENT_ID
                              AND MATCH_STATUS = 'L';

                           COMMIT;
                        END IF;

                        VV_ERR_LOC   := '220';
                        COMMIT;
                     EXCEPTION
                        WHEN DUP_VAL_ON_INDEX
                        THEN
                           VV_ERR_LOC     := '230';
                           V_END_SOURCE   := 'Y';
                     END;
                  END;

                  VV_ERR_LOC              := '240';

                  IF V_END_SOURCE = 'N'
                  THEN
                     IF R_INS_SOURCE.MATCH_STATUS = 'L'
                     THEN
                        BEGIN
                           -- BEGIN FOR CHECK CONDITION OF P_MATCH_QUALITY
                           -- Check the quality settings are exists for current currency otherwise
                           -- set the currency_code to all currency for quality selection
                           VV_ERR_LOC        := '250';
                           V_CURRENCY_CODE   := R_INS_SOURCE.CURRENCY_CODE;
                           VV_ERR_LOC        := '260';

                           IF V_CURRENCY_CODE != '  '
                           THEN -- TO CHECK MATCH QUALITY
                              VV_ERR_LOC   := '270';
                              L_MATCHID    := 0;

                              DECLARE
                                 R_INSERT_TARGET   CR_INS_TARGET_REF_PASS%ROWTYPE;
                                 VN_REF1_TGT1      P_MOVEMENT.REFERENCE1%TYPE;
                                 VTGTMOV1          P_MOVEMENT.MOVEMENT_ID%TYPE;
                                 VTGTMOV2          P_MOVEMENT.MOVEMENT_ID%TYPE;
                              BEGIN
                                 -- Check source movement references are valid references or not
                                 VV_ERR_LOC     := '280';

                                 -- target reference validation when source position = max internal position
                                 IF R_INS_SOURCE.POSITION_LEVEL = GN_MAX_INTERNAL_POS
                                 THEN
                                    VV_ERR_LOC   := '290';

                                    DECLARE
                                       VV_LOWER_POS_REF_FOUND    CHAR(1) := 'N';
                                       VV_HIGHER_POS_REF_FOUND   CHAR(1) := 'N';
                                       VN_MIN_POS                NUMBER := 0;
                                       VN_MAX_POS                NUMBER := 0;
                                    BEGIN
                                       VV_ERR_LOC   := '330';

                                       -- Check the existence of target reference
                                       SELECT MIN(M.POSITION_LEVEL), MAX(M.POSITION_LEVEL)
                                         INTO VN_MIN_POS, VN_MAX_POS
                                         FROM P_MOVEMENT M
                                        WHERE M.HOST_ID = R_INS_SOURCE.HOST_ID
                                          AND M.ENTITY_ID = R_INS_SOURCE.ENTITY_ID
                                          AND M.CURRENCY_CODE = R_INS_SOURCE.CURRENCY_CODE
                                          AND (M.VALUE_DATE = R_INS_SOURCE.VALUE_DATE
                                            OR (M.OPEN = 'Y'
                                            AND M.VALUE_DATE < R_INS_SOURCE.VALUE_DATE))
                                          AND M.MOVEMENT_ID != R_INS_SOURCE.MOVEMENT_ID
                                          AND M.POSITION_LEVEL != R_INS_SOURCE.POSITION_LEVEL
                                          AND M.MATCH_STATUS IN ('L', 'M', 'C')
                                          AND M.PREDICT_STATUS IN ('I', 'E')
                                          AND (M.MOVEMENT_ID IN (SELECT XREF_O.MOVEMENT_ID
                                                                   FROM P_REFERENCE_XREF XREF_I
                                                                        INNER JOIN P_REFERENCE_XREF XREF_O
                                                                           ON (XREF_O.CROSS_REFERENCE = XREF_I.CROSS_REFERENCE
                                                                           AND XREF_O.ENTITY_ID = XREF_I.ENTITY_ID
                                                                           AND XREF_O.CURRENCY_CODE = XREF_I.CURRENCY_CODE)
                                                                  WHERE XREF_I.MOVEMENT_ID = R_INS_SOURCE.MOVEMENT_ID
                                                                    AND XREF_I.CURRENCY_CODE = R_INS_SOURCE.CURRENCY_CODE
                                                                    AND XREF_O.MOVEMENT_ID != R_INS_SOURCE.MOVEMENT_ID));

                                       IF VN_MIN_POS < R_INS_SOURCE.POSITION_LEVEL
                                       OR VN_MAX_POS < R_INS_SOURCE.POSITION_LEVEL
                                       THEN
                                          VV_LOWER_POS_REF_FOUND   := 'Y';
                                       END IF;

                                       IF VN_MIN_POS > R_INS_SOURCE.POSITION_LEVEL
                                       OR VN_MAX_POS > R_INS_SOURCE.POSITION_LEVEL
                                       THEN
                                          VV_HIGHER_POS_REF_FOUND   := 'Y';
                                       END IF;

                                       VV_ERR_LOC   := '350';

                                       IF R_INS_SOURCE.TO_MATCH_STAGE = 1
                                       THEN
                                          IF R_INS_SOURCE.PREDICT_STATUS = 'E'
                                         AND R_INS_SOURCE.INITIAL_PREDICT_STATUS = 'I'
                                          THEN
                                             VV_PREADVICE_EXPECTED   := 'Y';
                                          END IF;

                                          IF VV_LOWER_POS_REF_FOUND = 'Y'
                                         AND VV_HIGHER_POS_REF_FOUND = 'Y'
                                          THEN
                                             VV_TARGET_POS_CHK        := 'B';
                                             L_FORCE_REFERENCE_ONLY   := 'Y';
                                          ELSIF VV_LOWER_POS_REF_FOUND = 'Y'
                                            AND VV_HIGHER_POS_REF_FOUND = 'N'
                                          THEN
                                             VV_TARGET_POS_CHK        := 'L';
                                             L_FORCE_REFERENCE_ONLY   := 'Y';
                                          ELSIF VV_LOWER_POS_REF_FOUND = 'N'
                                            AND VV_HIGHER_POS_REF_FOUND = 'Y'
                                          THEN
                                             L_FORCE_REFERENCE_ONLY   := 'Y';

                                             IF VV_PREADVICE_EXPECTED = 'Y'
                                             THEN
                                                VV_TARGET_POS_CHK   := 'N';
                                             ELSE
                                                VV_TARGET_POS_CHK   := 'H';
                                             END IF;
                                          ELSIF VV_LOWER_POS_REF_FOUND = 'N'
                                            AND VV_HIGHER_POS_REF_FOUND = 'N'
                                          THEN
                                             VV_ERR_LOC               := '351';

                                             IF FN_REF_CHK_OTHERS(R_INS_SOURCE.HOST_ID,
                                                                  R_INS_SOURCE.ENTITY_ID,
                                                                  R_INS_SOURCE.CURRENCY_CODE,
                                                                  R_INS_SOURCE.VALUE_DATE,
                                                                  R_INS_SOURCE.SIGN,
                                                                  R_INS_SOURCE.SOURCE_AMOUNT,
                                                                  L_TOLERANCE) = 0
                                             THEN
                                                VV_TARGET_POS_CHK   := 'N';
                                             ELSE
                                                VV_TARGET_POS_CHK   := 'B';
                                             END IF;

                                             L_FORCE_REFERENCE_ONLY   := 'N';
                                          END IF;
                                       ELSE
                                          VV_ERR_LOC   := '360';

                                          IF VV_LOWER_POS_REF_FOUND = 'Y'
                                          THEN
                                             VV_TARGET_POS_CHK        := 'L';
                                             L_FORCE_REFERENCE_ONLY   := 'Y';
                                          ELSIF VV_LOWER_POS_REF_FOUND = 'N'
                                          THEN
                                             VV_TARGET_POS_CHK        := 'B';
                                             L_FORCE_REFERENCE_ONLY   := 'N';
                                          END IF;
                                       END IF;

                                       VV_ERR_LOC   := '370';
                                    EXCEPTION
                                       WHEN NO_DATA_FOUND
                                       THEN
                                          NULL;
                                    END;
                                 -- target reference validation when source position is not max internal position
                                 ELSE
                                    IF R_INS_SOURCE.POSITION_LEVEL != GN_PRE_ADVICE_POS
                                    THEN
                                       VV_TARGET_POS_CHK   := 'L';
                                       VV_ERR_LOC          := '380';

                                       BEGIN
                                          SELECT 'Y'
                                            INTO L_FORCE_REFERENCE_ONLY
                                            FROM DUAL
                                           WHERE EXISTS
                                                    (SELECT NULL
                                                       FROM P_MOVEMENT M
                                                      WHERE M.HOST_ID = R_INS_SOURCE.HOST_ID
                                                        AND M.ENTITY_ID = R_INS_SOURCE.ENTITY_ID
                                                        AND M.CURRENCY_CODE = R_INS_SOURCE.CURRENCY_CODE
                                                        AND (M.VALUE_DATE = R_INS_SOURCE.VALUE_DATE
                                                          OR (M.OPEN = 'Y'
                                                          AND M.VALUE_DATE < R_INS_SOURCE.VALUE_DATE))
                                                        AND M.POSITION_LEVEL < R_INS_SOURCE.POSITION_LEVEL
                                                        AND M.POSITION_LEVEL <= GN_MAX_INTERNAL_POS
                                                        AND M.MOVEMENT_ID != R_INS_SOURCE.MOVEMENT_ID
                                                        AND M.MATCH_STATUS IN ('L', 'M', 'C')
                                                        AND M.PREDICT_STATUS IN ('I', 'E')
                                                        AND (M.MOVEMENT_ID IN (SELECT XREF_O.MOVEMENT_ID
                                                                                 FROM P_REFERENCE_XREF XREF_O
                                                                                WHERE XREF_O.HOST_ID = R_INS_SOURCE.HOST_ID
                                                                                  AND XREF_O.ENTITY_ID = R_INS_SOURCE.ENTITY_ID
                                                                                  AND XREF_O.MOVEMENT_ID != R_INS_SOURCE.MOVEMENT_ID
                                                                                  AND XREF_O.CURRENCY_CODE = R_INS_SOURCE.CURRENCY_CODE
                                                                                  AND XREF_O.CROSS_REFERENCE IN (SELECT XREF_I.CROSS_REFERENCE
                                                                                                                   FROM P_REFERENCE_XREF XREF_I
                                                                                                                  WHERE XREF_I.HOST_ID =
                                                                                                                           R_INS_SOURCE.HOST_ID
                                                                                                                    AND XREF_I.ENTITY_ID =
                                                                                                                           R_INS_SOURCE.ENTITY_ID
                                                                                                                    AND XREF_I.MOVEMENT_ID =
                                                                                                                           R_INS_SOURCE.MOVEMENT_ID
                                                                                                                    AND XREF_I.CURRENCY_CODE =
                                                                                                                           R_INS_SOURCE.CURRENCY_CODE))));

                                          VV_ERR_LOC   := '390';
                                       EXCEPTION
                                          WHEN NO_DATA_FOUND
                                          THEN
                                             L_FORCE_REFERENCE_ONLY   := 'N';
                                       END;

                                       VV_ERR_LOC          := '400';

                                       IF R_INS_SOURCE.TO_MATCH_STAGE = 1
                                      AND R_INS_SOURCE.POSITION_LEVEL = 4
                                      AND L_FORCE_REFERENCE_ONLY = 'N'
                                       THEN
                                          IF VV_PREADV_SEARCH_ALWAYS = 'Y'
                                          THEN
                                             VV_TARGET_POS_CHK   := 'N';
                                          ELSE
                                             VV_TARGET_POS_CHK   := 'B';
                                          END IF;
                                       END IF;
                                    ELSE
                                       VV_TARGET_POS_CHK   := 'P';
                                    END IF;
                                 END IF;

                                 VV_ERR_LOC     := '410';

                                 -- open target cursor
                                 IF VV_TARGET_POS_CHK = 'H'
                                 THEN
                                    OPEN CR_INSERT_TARGET_SUPP_PASS(R_INS_SOURCE.MOVEMENT_ID,
                                                                    R_INS_SOURCE.VALUE_DATE,
                                                                    R_INS_SOURCE.POSITION_LEVEL,
                                                                    R_INS_SOURCE.SOURCE_AMOUNT,
                                                                    R_INS_SOURCE.ACCOUNT_ID,
                                                                    R_INS_SOURCE.COUNTERPARTY_ID,
                                                                    R_INS_SOURCE.BENEFICIARY_ID,
                                                                    R_INS_SOURCE.CUSTODIAN_ID,
                                                                    R_INS_SOURCE.BOOKCODE,
                                                                    R_INS_SOURCE.OPEN,
                                                                    GN_MAX_INTERNAL_POS,
                                                                    R_INS_SOURCE.MATCHING_PARTY);
                                 ELSIF VV_TARGET_POS_CHK != 'N'
                                 THEN
                                    VV_ERR_LOC   := '280';

                                    IF VV_TARGET_POS_CHK != 'P'
                                    THEN
                                       IF L_FORCE_REFERENCE_ONLY = 'Y'
                                       THEN
                                          OPEN CR_INS_TARGET_REF_PASS(R_INS_SOURCE.MOVEMENT_ID,
                                                                      R_INS_SOURCE.VALUE_DATE,
                                                                      R_INS_SOURCE.POSITION_LEVEL,
                                                                      R_INS_SOURCE.SOURCE_AMOUNT,
                                                                      R_INS_SOURCE.ACCOUNT_ID,
                                                                      R_INS_SOURCE.COUNTERPARTY_ID,
                                                                      R_INS_SOURCE.BENEFICIARY_ID,
                                                                      R_INS_SOURCE.CUSTODIAN_ID,
                                                                      R_INS_SOURCE.BOOKCODE,
                                                                      R_INS_SOURCE.OPEN,
                                                                      VV_TARGET_POS_CHK,
                                                                      GN_MAX_INTERNAL_POS,
                                                                      R_INS_SOURCE.MATCHING_PARTY);
                                       ELSE
                                          OPEN CR_INS_TARGET_VD_AMT_PASS(R_INS_SOURCE.MOVEMENT_ID,
                                                                         R_INS_SOURCE.VALUE_DATE,
                                                                         R_INS_SOURCE.POSITION_LEVEL,
                                                                         R_INS_SOURCE.SOURCE_AMOUNT,
                                                                         R_INS_SOURCE.ACCOUNT_ID,
                                                                         R_INS_SOURCE.COUNTERPARTY_ID,
                                                                         R_INS_SOURCE.BENEFICIARY_ID,
                                                                         R_INS_SOURCE.CUSTODIAN_ID,
                                                                         R_INS_SOURCE.BOOKCODE,
                                                                         R_INS_SOURCE.OPEN,
                                                                         R_INS_SOURCE.SIGN,
                                                                         L_FORCE_REFERENCE_ONLY,
                                                                         GN_PRE_ADVICE_POS,
                                                                         GN_MAX_INTERNAL_POS,
                                                                         R_INS_SOURCE.MATCHING_PARTY);
                                       END IF;
                                    ELSE
                                       OPEN CR_INS_TARGET_PREADVICE(R_INS_SOURCE.MOVEMENT_ID,
                                                                    R_INS_SOURCE.VALUE_DATE,
                                                                    R_INS_SOURCE.POSITION_LEVEL,
                                                                    R_INS_SOURCE.SOURCE_AMOUNT,
                                                                    R_INS_SOURCE.ACCOUNT_ID,
                                                                    R_INS_SOURCE.COUNTERPARTY_ID,
                                                                    R_INS_SOURCE.BENEFICIARY_ID,
                                                                    R_INS_SOURCE.CUSTODIAN_ID,
                                                                    R_INS_SOURCE.BOOKCODE,
                                                                    R_INS_SOURCE.OPEN,
                                                                    R_INS_SOURCE.SIGN,
                                                                    GN_PRE_ADVICE_POS,
                                                                    GN_MAX_INTERNAL_POS,
                                                                    R_INS_SOURCE.MATCHING_PARTY);
                                    END IF;
                                 END IF;

                                 -- initialization of target references
                                 VV_ERR_LOC     := '430';
                                 VN_REF1_TGT1   := '~~~~~~~~~~~~~';

                                 -- fetch target movements
                                 IF VV_TARGET_POS_CHK != 'N'
                                 THEN
                                    LOOP
                                       IF VV_TARGET_POS_CHK = 'H'
                                       THEN
                                          FETCH CR_INSERT_TARGET_SUPP_PASS INTO R_INSERT_TARGET;

                                          EXIT WHEN CR_INSERT_TARGET_SUPP_PASS%NOTFOUND;
                                       ELSE
                                          IF VV_TARGET_POS_CHK != 'P'
                                          THEN
                                             IF L_FORCE_REFERENCE_ONLY = 'Y'
                                             THEN
                                                FETCH CR_INS_TARGET_REF_PASS INTO R_INSERT_TARGET;

                                                EXIT WHEN CR_INS_TARGET_REF_PASS%NOTFOUND;
                                             ELSE
                                                FETCH CR_INS_TARGET_VD_AMT_PASS INTO R_INSERT_TARGET;

                                                EXIT WHEN CR_INS_TARGET_VD_AMT_PASS%NOTFOUND;
                                             END IF;
                                          ELSE
                                             FETCH CR_INS_TARGET_PREADVICE INTO R_INSERT_TARGET;

                                             EXIT WHEN CR_INS_TARGET_PREADVICE%NOTFOUND;
                                          END IF;
                                       END IF;

                                       VV_ERR_LOC             := '470';
                                       L_TM_QUALITYFLAG       := R_INSERT_TARGET.L_TM_QUALITYFLAG;
                                       L_TM_QUALITYFLAG_MIN   := 0;
                                       V_END_TARGET           := 'Y';

                                       IF (L_TM_QUALITYFLAG > 0)
                                       THEN
                                          VV_ERR_LOC   := '480';

                                          DECLARE
                                             V_MOVE_ALREADY_EXIST   CHAR(1) := 'N';
                                             V_INSERT_FLAG          CHAR(1) := 'N';
                                          BEGIN
                                             VV_ERR_LOC   := '490';

                                             -- check for movement already exist in match processing table
                                             SELECT 'Y'
                                               INTO V_MOVE_ALREADY_EXIST
                                               FROM P_B_TARGET_MOVEMENTS
                                              WHERE HOST_ID = R_INSERT_TARGET.HOST_ID
                                                AND ENTITY_ID = R_INSERT_TARGET.ENTITY_ID
                                                AND CURRENCY_CODE = R_INSERT_TARGET.CURRENCY_CODE
                                                AND MOVEMENT_ID = R_INSERT_TARGET.MOVEMENT_ID
                                                AND VALID IN ('Y', 'F');
                                          EXCEPTION
                                             WHEN NO_DATA_FOUND
                                             THEN
                                                VV_ERR_LOC      := '500';
                                                V_INSERT_FLAG   := 'N';
                                                VV_ERR_LOC      := '520';

                                                IF R_INS_SOURCE.POSITION_LEVEL <= GN_MAX_INTERNAL_POS
                                               AND R_INSERT_TARGET.POSITION_LEVEL > GN_MAX_INTERNAL_POS
                                                THEN
                                                   V_INSERT_FLAG   := 'Y';
                                                ELSIF R_INS_SOURCE.POSITION_LEVEL <= GN_MAX_INTERNAL_POS
                                                  AND R_INSERT_TARGET.POSITION_LEVEL <= GN_MAX_INTERNAL_POS
                                                THEN
                                                   IF VV_TARGET_POS_CHK != 'P'
                                                   THEN
                                                      IF PK_MATCHING_PROCESS.FNISCROSSREFEXISTS(R_INS_SOURCE.HOST_ID,
                                                                                                R_INS_SOURCE.ENTITY_ID,
                                                                                                R_INS_SOURCE.CURRENCY_CODE,
                                                                                                R_INS_SOURCE.MOVEMENT_ID,
                                                                                                R_INSERT_TARGET.MOVEMENT_ID) = 'Y'
                                                      THEN
                                                         V_INSERT_FLAG   := 'Y';
                                                      ELSE
                                                         IF R_INSERT_TARGET.POSITION_LEVEL != GN_PRE_ADVICE_POS
                                                        AND VV_TARGET_POS_CHK = 'B'
                                                         THEN
                                                            DECLARE
                                                               VN_CNT   PLS_INTEGER := 0;
                                                            BEGIN
                                                               SELECT COUNT(1)
                                                                 INTO VN_CNT
                                                                 FROM P_B_TARGET_MOVEMENTS T
                                                                WHERE T.POS_LEVEL > R_INSERT_TARGET.POSITION_LEVEL
                                                                  AND T.QUALITYFLAG = R_INSERT_TARGET.L_TM_QUALITYFLAG;

                                                               IF VN_CNT > 0
                                                               THEN
                                                                  V_INSERT_FLAG   := 'Y';
                                                               END IF;
                                                            END;
                                                         END IF;
                                                      END IF;
                                                   ELSE
                                                      V_INSERT_FLAG   := 'Y';
                                                   END IF;
                                                ELSE
                                                   IF VV_TARGET_POS_CHK != 'P'
                                                   THEN
                                                      -- when target position level < position level threshold
                                                      IF R_INSERT_TARGET.POSITION_LEVEL <= GN_MAX_INTERNAL_POS
                                                      THEN
                                                         -- target reference assignment
                                                         IF VN_REF1_TGT1 = '~~~~~~~~~~~~~'
                                                         THEN
                                                            VN_REF1_TGT1    := '#$#$#$#$#$#$';
                                                            VTGTMOV1        := R_INSERT_TARGET.MOVEMENT_ID;
                                                            V_INSERT_FLAG   := 'Y';
                                                         ELSE
                                                            VTGTMOV2   := R_INSERT_TARGET.MOVEMENT_ID;

                                                            -- references validation between targets
                                                            IF PK_MATCHING_PROCESS.FNISCROSSREFEXISTS(R_INSERT_TARGET.HOST_ID,
                                                                                                      R_INSERT_TARGET.ENTITY_ID,
                                                                                                      R_INSERT_TARGET.CURRENCY_CODE,
                                                                                                      VTGTMOV1,
                                                                                                      VTGTMOV2) = 'Y'
                                                            THEN
                                                               V_INSERT_FLAG   := 'Y';
                                                            ELSE
                                                               V_INSERT_FLAG   := 'N';
                                                            END IF;
                                                         END IF;
                                                      ELSE
                                                         -- references validation between source and target
                                                         IF PK_MATCHING_PROCESS.FNISCROSSREFEXISTS(R_INS_SOURCE.HOST_ID,
                                                                                                   R_INS_SOURCE.ENTITY_ID,
                                                                                                   R_INS_SOURCE.CURRENCY_CODE,
                                                                                                   R_INS_SOURCE.MOVEMENT_ID,
                                                                                                   R_INSERT_TARGET.MOVEMENT_ID) = 'Y'
                                                         THEN
                                                            V_INSERT_FLAG   := 'Y';
                                                         END IF;
                                                      END IF;
                                                   ELSE
                                                      V_INSERT_FLAG   := 'Y';
                                                   END IF;
                                                END IF;

                                                IF V_INSERT_FLAG = 'Y'
                                                THEN
                                                   V_END_TARGET    := 'N';
                                                   VN_TGT_INSERT   := 'N';

                                                   -- for each movement only one position is allowed to insert
                                                   -- when target has same amount as source amount if not more
                                                   -- than one movement in the same position will be allowed.
                                                   -- if vn_tgt_pos<1-9>_qty variables i not set.
                                                   CASE R_INSERT_TARGET.POSITION_LEVEL
                                                      WHEN 1
                                                      THEN
                                                         IF VV_TARGET_POS_CHK != 'P'
                                                         THEN
                                                            IF VN_TGT_POS1_QTY = 0
                                                            THEN
                                                               VN_TGT_POS1_QTY   := R_INSERT_TARGET.L_TM_QUALITYFLAG;
                                                               VN_TGT_INSERT     := 'Y';
                                                            ELSIF VN_TGT_POS1_QTY = R_INSERT_TARGET.L_TM_QUALITYFLAG
                                                              AND R_INS_SOURCE.SOURCE_AMOUNT != R_INSERT_TARGET.TARGET_AMOUNT
                                                            THEN
                                                               VN_TGT_INSERT   := 'Y';
                                                            ELSIF VN_TGT_POS1_QTY < R_INSERT_TARGET.L_TM_QUALITYFLAG
                                                            THEN
                                                               VN_TGT_INSERT   := 'Y';
                                                            END IF;
                                                         END IF;
                                                      WHEN 2
                                                      THEN
                                                         IF VV_TARGET_POS_CHK != 'P'
                                                         THEN
                                                            IF VN_TGT_POS2_QTY = 0
                                                           AND VN_TGT_POS3_QTY = 0
                                                            THEN
                                                               VN_TGT_POS2_QTY   := R_INSERT_TARGET.L_TM_QUALITYFLAG;
                                                               VN_TGT_INSERT     := 'Y';
                                                            ELSIF VN_TGT_POS2_QTY = R_INSERT_TARGET.L_TM_QUALITYFLAG
                                                              AND R_INS_SOURCE.SOURCE_AMOUNT != R_INSERT_TARGET.TARGET_AMOUNT
                                                            THEN
                                                               IF R_INS_SOURCE.POSITION_LEVEL > R_INSERT_TARGET.POSITION_LEVEL
                                                               THEN
                                                                  VN_TGT_INSERT   := 'Y';
                                                               END IF;
                                                            ELSIF VN_TGT_POS2_QTY < R_INSERT_TARGET.L_TM_QUALITYFLAG
                                                            THEN
                                                               VN_TGT_INSERT   := 'Y';
                                                            END IF;
                                                         END IF;
                                                      WHEN 3
                                                      THEN
                                                         IF VV_TARGET_POS_CHK != 'P'
                                                         THEN
                                                            VN_RETN_VALUE   := 'Y';

                                                            IF VN_INNER_TGT_INTERNAL > 0
                                                            THEN
                                                               VN_RETN_VALUE      :=
                                                                  FN_CHECK_INNER_TGT_REFERENCE(R_INSERT_TARGET.HOST_ID,
                                                                                               R_INSERT_TARGET.ENTITY_ID,
                                                                                               VN_INNER_TGT_INTERNAL,
                                                                                               R_INSERT_TARGET.MOVEMENT_ID);
                                                            END IF;

                                                            IF VN_RETN_VALUE = 'N'
                                                            THEN
                                                               VN_TGT_INSERT   := 'N';
                                                            ELSE
                                                               IF VN_TGT_POS3_QTY = 0
                                                               THEN
                                                                  VN_TGT_POS3_QTY   := R_INSERT_TARGET.L_TM_QUALITYFLAG;
                                                                  VN_TGT_INSERT     := 'Y';
                                                               ELSIF VN_TGT_POS3_QTY = R_INSERT_TARGET.L_TM_QUALITYFLAG
                                                                 AND R_INS_SOURCE.SOURCE_AMOUNT != R_INSERT_TARGET.TARGET_AMOUNT
                                                               THEN
                                                                  IF R_INS_SOURCE.POSITION_LEVEL > R_INSERT_TARGET.POSITION_LEVEL
                                                                  THEN
                                                                     VN_TGT_INSERT   := 'Y';
                                                                  END IF;
                                                               ELSIF VN_TGT_POS3_QTY < R_INSERT_TARGET.L_TM_QUALITYFLAG
                                                               THEN
                                                                  VN_TGT_INSERT   := 'Y';
                                                               END IF;
                                                            END IF;
                                                         END IF;
                                                      WHEN 4
                                                      THEN
                                                         VN_RETN_VALUE   := 'Y';

                                                         IF VN_INNER_TGT_INTERNAL > 0
                                                         THEN
                                                            VN_RETN_VALUE      :=
                                                               FN_CHECK_INNER_TGT_REFERENCE(R_INSERT_TARGET.HOST_ID,
                                                                                            R_INSERT_TARGET.ENTITY_ID,
                                                                                            VN_INNER_TGT_INTERNAL,
                                                                                            R_INSERT_TARGET.MOVEMENT_ID);
                                                         END IF;

                                                         IF VN_RETN_VALUE = 'N'
                                                         THEN
                                                            VN_TGT_INSERT   := 'N';
                                                         ELSE
                                                            IF VN_TGT_POS4_QTY = 0
                                                            THEN
                                                               IF R_INS_SOURCE.POSITION_LEVEL < R_INSERT_TARGET.POSITION_LEVEL
                                                               THEN
                                                                  IF R_INS_SOURCE.SOURCE_AMOUNT BETWEEN R_INSERT_TARGET.TARGET_AMOUNT - L_TOLERANCE
                                                                                                    AND R_INSERT_TARGET.TARGET_AMOUNT + L_TOLERANCE
                                                                  THEN
                                                                     VN_TGT_POS4_QTY   := R_INSERT_TARGET.L_TM_QUALITYFLAG;
                                                                     VN_TGT_INSERT     := 'Y';
                                                                  END IF;
                                                               ELSE
                                                                  VN_TGT_POS4_QTY   := R_INSERT_TARGET.L_TM_QUALITYFLAG;
                                                                  VN_TGT_INSERT     := 'Y';
                                                               END IF;
                                                            ELSIF VN_TGT_POS4_QTY = R_INSERT_TARGET.L_TM_QUALITYFLAG
                                                              AND R_INS_SOURCE.SOURCE_AMOUNT != R_INSERT_TARGET.TARGET_AMOUNT
                                                            THEN
                                                               IF R_INS_SOURCE.POSITION_LEVEL > R_INSERT_TARGET.POSITION_LEVEL
                                                               THEN
                                                                  VN_TGT_INSERT   := 'Y';
                                                               END IF;
                                                            ELSIF VN_TGT_POS4_QTY < R_INSERT_TARGET.L_TM_QUALITYFLAG
                                                            THEN
                                                               VN_TGT_INSERT   := 'Y';
                                                            END IF;
                                                         END IF;
                                                      WHEN 5
                                                      THEN
                                                         IF VV_TARGET_POS_CHK != 'P'
                                                         THEN
                                                            VN_RETN_VALUE   := 'Y';

                                                            IF VN_INNER_TGT_INTERNAL > 0
                                                            THEN
                                                               VN_RETN_VALUE      :=
                                                                  FN_CHECK_INNER_TGT_REFERENCE(R_INSERT_TARGET.HOST_ID,
                                                                                               R_INSERT_TARGET.ENTITY_ID,
                                                                                               VN_INNER_TGT_INTERNAL,
                                                                                               R_INSERT_TARGET.MOVEMENT_ID);
                                                            END IF;

                                                            IF VN_RETN_VALUE = 'N'
                                                            THEN
                                                               VN_TGT_INSERT   := 'N';
                                                            ELSE
                                                               IF VN_TGT_POS5_QTY = 0
                                                               THEN
                                                                  IF R_INS_SOURCE.POSITION_LEVEL < R_INSERT_TARGET.POSITION_LEVEL
                                                                  THEN
                                                                     IF R_INS_SOURCE.SOURCE_AMOUNT BETWEEN   R_INSERT_TARGET.TARGET_AMOUNT
                                                                                                           - L_TOLERANCE
                                                                                                       AND   R_INSERT_TARGET.TARGET_AMOUNT
                                                                                                           + L_TOLERANCE
                                                                     THEN
                                                                        VN_TGT_POS5_QTY   := R_INSERT_TARGET.L_TM_QUALITYFLAG;
                                                                        VN_TGT_INSERT     := 'Y';
                                                                     END IF;
                                                                  ELSE
                                                                     VN_TGT_POS5_QTY   := R_INSERT_TARGET.L_TM_QUALITYFLAG;
                                                                     VN_TGT_INSERT     := 'Y';
                                                                  END IF;
                                                               ELSIF VN_TGT_POS5_QTY = R_INSERT_TARGET.L_TM_QUALITYFLAG
                                                                 AND R_INS_SOURCE.SOURCE_AMOUNT != R_INSERT_TARGET.TARGET_AMOUNT
                                                               THEN
                                                                  IF R_INS_SOURCE.POSITION_LEVEL > R_INSERT_TARGET.POSITION_LEVEL
                                                                  THEN
                                                                     VN_TGT_INSERT   := 'Y';
                                                                  END IF;
                                                               ELSIF VN_TGT_POS5_QTY < R_INSERT_TARGET.L_TM_QUALITYFLAG
                                                               THEN
                                                                  VN_TGT_INSERT   := 'Y';
                                                               END IF;
                                                            END IF;
                                                         END IF;
                                                      WHEN 6
                                                      THEN
                                                         VN_RETN_VALUE   := 'Y';

                                                         IF VN_INNER_TGT_INTERNAL > 0
                                                         THEN
                                                            VN_RETN_VALUE      :=
                                                               FN_CHECK_INNER_TGT_REFERENCE(R_INSERT_TARGET.HOST_ID,
                                                                                            R_INSERT_TARGET.ENTITY_ID,
                                                                                            VN_INNER_TGT_INTERNAL,
                                                                                            R_INSERT_TARGET.MOVEMENT_ID);
                                                         END IF;

                                                         IF VN_RETN_VALUE = 'N'
                                                         THEN
                                                            VN_TGT_INSERT   := 'N';
                                                         ELSE
                                                            IF VN_TGT_POS6_QTY = 0
                                                            THEN
                                                               IF R_INS_SOURCE.POSITION_LEVEL < R_INSERT_TARGET.POSITION_LEVEL
                                                               THEN
                                                                  IF R_INS_SOURCE.SOURCE_AMOUNT BETWEEN R_INSERT_TARGET.TARGET_AMOUNT - L_TOLERANCE
                                                                                                    AND R_INSERT_TARGET.TARGET_AMOUNT + L_TOLERANCE
                                                                  THEN
                                                                     VN_TGT_POS6_QTY   := R_INSERT_TARGET.L_TM_QUALITYFLAG;
                                                                     VN_TGT_INSERT     := 'Y';
                                                                  END IF;
                                                               ELSE
                                                                  VN_TGT_POS6_QTY   := R_INSERT_TARGET.L_TM_QUALITYFLAG;
                                                                  VN_TGT_INSERT     := 'Y';
                                                               END IF;
                                                            ELSIF VN_TGT_POS6_QTY = R_INSERT_TARGET.L_TM_QUALITYFLAG
                                                              AND R_INS_SOURCE.SOURCE_AMOUNT != R_INSERT_TARGET.TARGET_AMOUNT
                                                            THEN
                                                               IF R_INS_SOURCE.POSITION_LEVEL > R_INSERT_TARGET.POSITION_LEVEL
                                                               THEN
                                                                  VN_TGT_INSERT   := 'Y';
                                                               END IF;
                                                            ELSIF VN_TGT_POS6_QTY < R_INSERT_TARGET.L_TM_QUALITYFLAG
                                                            THEN
                                                               VN_TGT_INSERT   := 'Y';
                                                            END IF;
                                                         END IF;
                                                      WHEN 7
                                                      THEN
                                                         VN_RETN_VALUE   := 'Y';

                                                         IF VN_INNER_TGT_EXTERNAL > 0
                                                         THEN
                                                            VN_RETN_VALUE      :=
                                                               FN_CHECK_INNER_TGT_REFERENCE(R_INSERT_TARGET.HOST_ID,
                                                                                            R_INSERT_TARGET.ENTITY_ID,
                                                                                            VN_INNER_TGT_EXTERNAL,
                                                                                            R_INSERT_TARGET.MOVEMENT_ID);
                                                         END IF;

                                                         IF VN_RETN_VALUE = 'N'
                                                         THEN
                                                            VN_TGT_INSERT   := 'N';
                                                         ELSE
                                                            IF VN_TGT_POS7_QTY = 0
                                                            THEN
                                                               IF R_INSERT_TARGET.ACCOUNT_ID = R_INS_SOURCE.ACCOUNT_ID
                                                               THEN
                                                                  IF R_INS_SOURCE.POSITION_LEVEL < R_INSERT_TARGET.POSITION_LEVEL
                                                                  THEN
                                                                     IF R_INS_SOURCE.SOURCE_AMOUNT BETWEEN   R_INSERT_TARGET.TARGET_AMOUNT
                                                                                                           - L_TOLERANCE
                                                                                                       AND   R_INSERT_TARGET.TARGET_AMOUNT
                                                                                                           + L_TOLERANCE
                                                                     THEN
                                                                        VN_TGT_POS7_QTY   := R_INSERT_TARGET.L_TM_QUALITYFLAG;
                                                                        VN_TGT_INSERT     := 'Y';
                                                                     END IF;
                                                                  ELSE
                                                                     VN_TGT_POS7_QTY   := R_INSERT_TARGET.L_TM_QUALITYFLAG;
                                                                     VN_TGT_INSERT     := 'Y';
                                                                  END IF;
                                                               ELSE
                                                                  IF R_INS_SOURCE.POSITION_LEVEL <= GN_MAX_INTERNAL_POS
                                                                 AND FN_IS_SRC_LINK_TO_TGT_REC(R_INS_SOURCE.HOST_ID,
                                                                                               R_INS_SOURCE.ACCOUNT_ID,
                                                                                               R_INSERT_TARGET.ACCOUNT_ID) = 'Y'
                                                                  THEN
                                                                     VN_TGT_POS7_QTY   := R_INSERT_TARGET.L_TM_QUALITYFLAG;
                                                                     VN_TGT_INSERT     := 'Y';
                                                                  ELSE
                                                                     VV_DEL_TARGET   := 'Y';
                                                                  END IF;
                                                               END IF;
                                                            ELSIF VN_TGT_POS7_QTY = R_INSERT_TARGET.L_TM_QUALITYFLAG
                                                              AND R_INS_SOURCE.SOURCE_AMOUNT != R_INSERT_TARGET.TARGET_AMOUNT
                                                            THEN
                                                               IF R_INS_SOURCE.POSITION_LEVEL > R_INSERT_TARGET.POSITION_LEVEL
                                                               THEN
                                                                  VN_TGT_INSERT   := 'Y';
                                                               END IF;
                                                            ELSIF VN_TGT_POS7_QTY < R_INSERT_TARGET.L_TM_QUALITYFLAG
                                                            THEN
                                                               VN_TGT_INSERT   := 'Y';
                                                            END IF;
                                                         END IF;
                                                      WHEN 8
                                                      THEN
                                                         VN_RETN_VALUE   := 'Y';

                                                         IF VN_INNER_TGT_EXTERNAL > 0
                                                         THEN
                                                            VN_RETN_VALUE      :=
                                                               FN_CHECK_INNER_TGT_REFERENCE(R_INSERT_TARGET.HOST_ID,
                                                                                            R_INSERT_TARGET.ENTITY_ID,
                                                                                            VN_INNER_TGT_EXTERNAL,
                                                                                            R_INSERT_TARGET.MOVEMENT_ID);
                                                         END IF;

                                                         IF VN_RETN_VALUE = 'N'
                                                         THEN
                                                            VN_TGT_INSERT   := 'N';
                                                         ELSE
                                                            IF VN_TGT_POS8_QTY = 0
                                                            THEN
                                                               IF R_INSERT_TARGET.ACCOUNT_ID = R_INS_SOURCE.ACCOUNT_ID
                                                               THEN
                                                                  IF R_INS_SOURCE.POSITION_LEVEL < R_INSERT_TARGET.POSITION_LEVEL
                                                                  THEN
                                                                     IF R_INS_SOURCE.SOURCE_AMOUNT BETWEEN   R_INSERT_TARGET.TARGET_AMOUNT
                                                                                                           - L_TOLERANCE
                                                                                                       AND   R_INSERT_TARGET.TARGET_AMOUNT
                                                                                                           + L_TOLERANCE
                                                                     THEN
                                                                        VN_TGT_POS8_QTY   := R_INSERT_TARGET.L_TM_QUALITYFLAG;
                                                                        VN_TGT_INSERT     := 'Y';
                                                                     END IF;
                                                                  ELSE
                                                                     VN_TGT_POS8_QTY   := R_INSERT_TARGET.L_TM_QUALITYFLAG;
                                                                     VN_TGT_INSERT     := 'Y';
                                                                  END IF;
                                                               ELSE
                                                                  IF R_INS_SOURCE.POSITION_LEVEL <= GN_MAX_INTERNAL_POS
                                                                 AND FN_IS_SRC_LINK_TO_TGT_REC(R_INS_SOURCE.HOST_ID,
                                                                                               R_INS_SOURCE.ACCOUNT_ID,
                                                                                               R_INSERT_TARGET.ACCOUNT_ID) = 'Y'
                                                                  THEN
                                                                     VN_TGT_POS8_QTY   := R_INSERT_TARGET.L_TM_QUALITYFLAG;
                                                                     VN_TGT_INSERT     := 'Y';
                                                                  ELSE
                                                                     VV_DEL_TARGET   := 'Y';
                                                                  END IF;
                                                               END IF;
                                                            ELSIF VN_TGT_POS8_QTY = R_INSERT_TARGET.L_TM_QUALITYFLAG
                                                              AND R_INS_SOURCE.SOURCE_AMOUNT != R_INSERT_TARGET.TARGET_AMOUNT
                                                            THEN
                                                               VN_TGT_INSERT   := 'Y';
                                                            ELSIF VN_TGT_POS8_QTY < R_INSERT_TARGET.L_TM_QUALITYFLAG
                                                            THEN
                                                               VN_TGT_INSERT   := 'Y';
                                                            END IF;
                                                         END IF;
                                                      WHEN 9
                                                      THEN
                                                         VN_RETN_VALUE   := 'Y';

                                                         IF VN_INNER_TGT_EXTERNAL > 0
                                                         THEN
                                                            VN_RETN_VALUE      :=
                                                               FN_CHECK_INNER_TGT_REFERENCE(R_INSERT_TARGET.HOST_ID,
                                                                                            R_INSERT_TARGET.ENTITY_ID,
                                                                                            VN_INNER_TGT_EXTERNAL,
                                                                                            R_INSERT_TARGET.MOVEMENT_ID);
                                                         END IF;

                                                         IF VN_RETN_VALUE = 'N'
                                                         THEN
                                                            VN_TGT_INSERT   := 'N';
                                                         ELSE
                                                            IF VN_TGT_POS9_QTY = 0
                                                            THEN
                                                               IF R_INSERT_TARGET.ACCOUNT_ID = R_INS_SOURCE.ACCOUNT_ID
                                                               THEN
                                                                  IF R_INS_SOURCE.POSITION_LEVEL < R_INSERT_TARGET.POSITION_LEVEL
                                                                  THEN
                                                                     IF R_INS_SOURCE.SOURCE_AMOUNT BETWEEN   R_INSERT_TARGET.TARGET_AMOUNT
                                                                                                           - L_TOLERANCE
                                                                                                       AND   R_INSERT_TARGET.TARGET_AMOUNT
                                                                                                           + L_TOLERANCE
                                                                     THEN
                                                                        VN_TGT_POS9_QTY   := R_INSERT_TARGET.L_TM_QUALITYFLAG;
                                                                        VN_TGT_INSERT     := 'Y';
                                                                     END IF;
                                                                  ELSE
                                                                     VN_TGT_POS9_QTY   := R_INSERT_TARGET.L_TM_QUALITYFLAG;
                                                                     VN_TGT_INSERT     := 'Y';
                                                                  END IF;
                                                               ELSE
                                                                  IF R_INS_SOURCE.POSITION_LEVEL <= GN_MAX_INTERNAL_POS
                                                                 AND FN_IS_SRC_LINK_TO_TGT_REC(R_INS_SOURCE.HOST_ID,
                                                                                               R_INS_SOURCE.ACCOUNT_ID,
                                                                                               R_INSERT_TARGET.ACCOUNT_ID) = 'Y'
                                                                  THEN
                                                                     VN_TGT_POS9_QTY   := R_INSERT_TARGET.L_TM_QUALITYFLAG;
                                                                     VN_TGT_INSERT     := 'Y';
                                                                  ELSE
                                                                     VV_DEL_TARGET   := 'Y';
                                                                  END IF;
                                                               END IF;
                                                            ELSIF VN_TGT_POS9_QTY = R_INSERT_TARGET.L_TM_QUALITYFLAG
                                                              AND R_INS_SOURCE.SOURCE_AMOUNT != R_INSERT_TARGET.TARGET_AMOUNT
                                                            THEN
                                                               VN_TGT_INSERT   := 'Y';
                                                            ELSIF VN_TGT_POS9_QTY < R_INSERT_TARGET.L_TM_QUALITYFLAG
                                                            THEN
                                                               VN_TGT_INSERT   := 'Y';
                                                            END IF;
                                                         END IF;
                                                      ELSE
                                                         NULL;
                                                   END CASE;

                                                   IF VV_DEL_TARGET != 'Y'
                                                   THEN
                                                      IF VN_TGT_INSERT = 'Y'
                                                      THEN
                                                         BEGIN
                                                            VV_ERR_LOC   := '530';

                                                            -- assume target value date is source value date
                                                            -- when target OPEN flag is 'Y' otherwise use target value date
                                                            IF R_INSERT_TARGET.OPEN = 'N'
                                                            THEN
                                                               V_VALUE_DATE   := R_INSERT_TARGET.VALUE_DATE;
                                                            ELSE
                                                               V_VALUE_DATE   := R_INS_SOURCE.VALUE_DATE;
                                                            END IF;

                                                            -- insert the target movement into match processing table
                                                            SP_ADD_P_B_TARGETS(R_INSERT_TARGET.MOVEMENT_ID,
                                                                               R_INSERT_TARGET.HOST_ID,
                                                                               R_INSERT_TARGET.ENTITY_ID,
                                                                               R_INSERT_TARGET.CURRENCY_CODE,
                                                                               R_INSERT_TARGET.MATCH_ID,
                                                                               R_INSERT_TARGET.MATCH_STATUS,
                                                                               R_INSERT_TARGET.PREDICT_STATUS,
                                                                               R_INSERT_TARGET.POSITION_LEVEL,
                                                                               V_VALUE_DATE,
                                                                               R_INSERT_TARGET.COUNTERPARTY_ID,
                                                                               R_INSERT_TARGET.BENEFICIARY_ID,
                                                                               R_INSERT_TARGET.CUSTODIAN_ID,
                                                                               R_INSERT_TARGET.BOOKCODE,
                                                                               R_INSERT_TARGET.TARGET_AMOUNT,
                                                                               R_INSERT_TARGET.VALID,
                                                                               R_INSERT_TARGET.L_TM_QUALITYFLAG,
                                                                               R_INSERT_TARGET.ACCOUNT_ID,
                                                                               'Y',
                                                                               'Y',
                                                                               R_INSERT_TARGET.MATCHING_PARTY,
                                                                               R_INSERT_TARGET.INITIAL_PREDICT_STATUS);
                                                         EXCEPTION
                                                            WHEN DUP_VAL_ON_INDEX
                                                            THEN
                                                               VV_ERR_LOC     := '540';
                                                               V_END_TARGET   := 'Y';
                                                         END;
                                                      END IF;
                                                   END IF;
                                                END IF;

                                                VV_ERR_LOC      := '550';

                                                -- indirect target processing
                                                -- when current target has match id
                                                IF VV_DEL_TARGET != 'Y'
                                                THEN
                                                   IF VN_TGT_INSERT = 'Y'
                                                   THEN
                                                      IF (R_INSERT_TARGET.VALID <> 'F'
                                                      AND V_END_TARGET = 'N')
                                                      THEN
                                                         VV_ERR_LOC   := '560';

                                                         IF ((R_INSERT_TARGET.MATCH_STATUS = 'M'
                                                           OR R_INSERT_TARGET.MATCH_STATUS = 'C')
                                                         AND (VV_TARGET_POS_CHK != 'H'
                                                          AND VV_TARGET_POS_CHK != 'N'))
                                                         THEN
                                                            VV_ERR_LOC          := '630';
                                                            L_INT_QTY_P_MATCH   := LINTQTYMATCHVAL;

                                                            -- indirect target selection
                                                            DECLARE
                                                               VV_DEL_INNER_TARGET     CHAR(1);
                                                               VN_MATCH_LOW_POSITION   NUMBER(1);

                                                               CURSOR CR_INNER_INSERT_TARGET(
                                                                  SOURCE_MOVEMENT_ID        NUMBER,
                                                                  SOURCE_VALUE_DATE         DATE,
                                                                  SOURCE_HIGHESTPOSLEVEL    NUMBER,
                                                                  SOURCE_AMOUNT             NUMBER,
                                                                  SOURCE_ACCOUNT_ID         VARCHAR2,
                                                                  SOURCE_COUNTERPARTY_ID    VARCHAR2,
                                                                  SOURCE_BENEFICIARY_ID     VARCHAR2,
                                                                  SOURCE_CUSTODIAN_ID       VARCHAR2,
                                                                  SOURCE_BOOKCODE           VARCHAR2,
                                                                  SOURCE_OPEN               VARCHAR2,
                                                                  CN_POS_THRESHOLD          NUMBER,
                                                                  SOURCE_MATCHINGPARTY      P_MOVEMENT.MATCHING_PARTY%TYPE)
                                                               IS
                                                                  SELECT MOVEMENT_ID,
                                                                         HOST_ID,
                                                                         ENTITY_ID,
                                                                         CURRENCY_CODE,
                                                                         MATCH_ID,
                                                                         MATCH_STATUS,
                                                                         PREDICT_STATUS,
                                                                         POSITION_LEVEL,
                                                                         VALUE_DATE,
                                                                         PK_MATCHING_PROCESS.FN_UPDATE_P_B_MATQUAL_POSLEVEL(
                                                                            A.HOST_ID,
                                                                            A.ENTITY_ID,
                                                                            V_CURRENCY_CODE,
                                                                            DECODE(SOURCE_OPEN,
                                                                                   'N', SOURCE_VALUE_DATE,
                                                                                   'Y', GV_GLOBAL_VAR_SYS_DATE),
                                                                            DECODE(A.OPEN,  'N', A.VALUE_DATE,  'Y', GV_GLOBAL_VAR_SYS_DATE),
                                                                            SOURCE_AMOUNT,
                                                                            DECODE(A.SIGN,  'D', -A.AMOUNT,  'C', A.AMOUNT),
                                                                            L_TOLERANCE,
                                                                            SOURCE_ACCOUNT_ID,
                                                                            A.ACCOUNT_ID,
                                                                            SOURCE_COUNTERPARTY_ID,
                                                                            A.COUNTERPARTY_ID,
                                                                            SOURCE_BENEFICIARY_ID,
                                                                            A.BENEFICIARY_ID,
                                                                            SOURCE_CUSTODIAN_ID,
                                                                            A.CUSTODIAN_ID,
                                                                            SOURCE_BOOKCODE,
                                                                            A.BOOKCODE,
                                                                            SOURCE_HIGHESTPOSLEVEL,
                                                                            SOURCE_MOVEMENT_ID,
                                                                            A.MOVEMENT_ID,
                                                                            A.OPEN,
                                                                            SOURCE_MATCHINGPARTY,
                                                                            A.MATCHING_PARTY,
                                                                            A.POSITION_LEVEL,
                                                                            CN_POS_THRESHOLD)
                                                                            QTY,
                                                                         DECODE(SIGN,  'D', -AMOUNT,  'C', AMOUNT) AMOUNT,
                                                                         COUNTERPARTY_ID,
                                                                         BENEFICIARY_ID,
                                                                         CUSTODIAN_ID,
                                                                         BOOKCODE,
                                                                         ACCOUNT_ID,
                                                                         'F' VALID,
                                                                         MATCHING_PARTY,
                                                                         A.OPEN,
                                                                         INITIAL_PREDICT_STATUS
                                                                    FROM P_MOVEMENT A
                                                                   WHERE HOST_ID = R_INS_SOURCE.HOST_ID
                                                                     AND ENTITY_ID = R_INS_SOURCE.ENTITY_ID
                                                                     AND CURRENCY_CODE = R_INS_SOURCE.CURRENCY_CODE
                                                                     AND A.MOVEMENT_ID <> R_INSERT_TARGET.MOVEMENT_ID
                                                                     AND MATCH_STATUS IN ('M', 'C')
                                                                     AND MATCH_ID = R_INSERT_TARGET.MATCH_ID
                                                                     AND NOT EXISTS
                                                                            (SELECT NULL
                                                                               FROM P_MOVEMENT_LOCK C
                                                                              WHERE C.HOST_ID = A.HOST_ID
                                                                                AND C.MOVEMENT_ID = A.MOVEMENT_ID
                                                                                AND C.ENTITY_ID = A.ENTITY_ID
                                                                                AND C.CURRENCY_CODE = A.CURRENCY_CODE)
                                                                     AND NOT EXISTS
                                                                            (SELECT NULL
                                                                               FROM P_B_TARGET_MOVEMENTS D
                                                                              WHERE D.HOST_ID = A.HOST_ID
                                                                                AND D.ENTITY_ID = A.ENTITY_ID
                                                                                AND D.CURRENCY_CODE = A.CURRENCY_CODE
                                                                                AND D.MOVEMENT_ID = A.MOVEMENT_ID);
                                                            BEGIN
                                                               VV_ERR_LOC   := '640';

                                                               FOR R_INNER_INSERT_TARGET IN CR_INNER_INSERT_TARGET(R_INS_SOURCE.MOVEMENT_ID,
                                                                                                                   R_INS_SOURCE.VALUE_DATE,
                                                                                                                   R_INS_SOURCE.POSITION_LEVEL,
                                                                                                                   R_INS_SOURCE.SOURCE_AMOUNT,
                                                                                                                   R_INS_SOURCE.ACCOUNT_ID,
                                                                                                                   R_INS_SOURCE.COUNTERPARTY_ID,
                                                                                                                   R_INS_SOURCE.BENEFICIARY_ID,
                                                                                                                   R_INS_SOURCE.CUSTODIAN_ID,
                                                                                                                   R_INS_SOURCE.BOOKCODE,
                                                                                                                   R_INS_SOURCE.OPEN,
                                                                                                                   GN_MAX_INTERNAL_POS,
                                                                                                                   R_INS_SOURCE.MATCHING_PARTY)
                                                               LOOP
                                                                  -- apply the target selection business logic for indirect target
                                                                  -- references between internal movements should be same
                                                                  -- references between external movements should be same
                                                                  BEGIN
                                                                     VV_DEL_INNER_TARGET   := 'N';
                                                                     VV_ERR_LOC            := '650';

                                                                     IF VV_TARGET_POS_CHK != 'P'
                                                                     THEN
                                                                        IF R_INS_SOURCE.POSITION_LEVEL > GN_MAX_INTERNAL_POS
                                                                       AND R_INNER_INSERT_TARGET.POSITION_LEVEL > GN_MAX_INTERNAL_POS
                                                                        THEN
                                                                           IF R_INS_SOURCE.ACCOUNT_ID != R_INNER_INSERT_TARGET.ACCOUNT_ID
                                                                          AND R_INS_SOURCE.POSITION_LEVEL != R_INNER_INSERT_TARGET.POSITION_LEVEL
                                                                           THEN
                                                                              VV_DEL_TARGET   := 'Y';
                                                                           END IF;

                                                                           IF PK_MATCHING_PROCESS.FNISCROSSREFEXISTS(
                                                                                 R_INS_SOURCE.HOST_ID,
                                                                                 R_INS_SOURCE.ENTITY_ID,
                                                                                 R_INS_SOURCE.CURRENCY_CODE,
                                                                                 R_INS_SOURCE.MOVEMENT_ID,
                                                                                 R_INNER_INSERT_TARGET.MOVEMENT_ID) = 'Y'
                                                                          AND R_INS_SOURCE.POSITION_LEVEL != R_INNER_INSERT_TARGET.POSITION_LEVEL
                                                                           THEN
                                                                              NULL;
                                                                           ELSIF R_INS_SOURCE.POSITION_LEVEL = R_INNER_INSERT_TARGET.POSITION_LEVEL
                                                                           THEN
                                                                              IF PK_MATCHING_PROCESS.FNISCROSSREFEXISTS(
                                                                                    R_INS_SOURCE.HOST_ID,
                                                                                    R_INS_SOURCE.ENTITY_ID,
                                                                                    R_INS_SOURCE.CURRENCY_CODE,
                                                                                    R_INS_SOURCE.MOVEMENT_ID,
                                                                                    R_INNER_INSERT_TARGET.MOVEMENT_ID) = 'Y'
                                                                              THEN
                                                                                 VV_DEL_TARGET   := 'Y';
                                                                              ELSE
                                                                                 IF PK_MATCHING_PROCESS.FNISCROSSREFEXISTS(
                                                                                       R_INS_SOURCE.HOST_ID,
                                                                                       R_INS_SOURCE.ENTITY_ID,
                                                                                       R_INS_SOURCE.CURRENCY_CODE,
                                                                                       R_INS_SOURCE.MOVEMENT_ID,
                                                                                       R_INSERT_TARGET.MOVEMENT_ID) = 'Y'
                                                                                 THEN
                                                                                    NULL;
                                                                                 ELSE
                                                                                    VV_DEL_TARGET   := 'Y';
                                                                                 END IF;
                                                                              -- vv_del_inner_target := 'Y';
                                                                              END IF;
                                                                           ELSE
                                                                              VV_DEL_TARGET   := 'Y';
                                                                           END IF;
                                                                        END IF;

                                                                        IF R_INNER_INSERT_TARGET.POSITION_LEVEL >= GN_MAX_INTERNAL_POS
                                                                       AND R_INS_SOURCE.POSITION_LEVEL = GN_MAX_INTERNAL_POS
                                                                       AND (R_INS_SOURCE.SOURCE_AMOUNT NOT BETWEEN (  R_INNER_INSERT_TARGET.AMOUNT
                                                                                                                    - L_TOLERANCE)
                                                                                                               AND (  R_INNER_INSERT_TARGET.AMOUNT
                                                                                                                    + L_TOLERANCE))
                                                                        THEN
                                                                           VV_DEL_INNER_TARGET   := 'Y';
                                                                        END IF;

                                                                        IF R_INNER_INSERT_TARGET.POSITION_LEVEL = R_INS_SOURCE.POSITION_LEVEL
                                                                       AND R_INSERT_TARGET.MATCH_STATUS = 'M'
                                                                       AND R_INSERT_TARGET.L_TM_QUALITYFLAG < 3
                                                                        THEN
                                                                           VV_DEL_TARGET   := 'Y';
                                                                        END IF;

                                                                        IF (R_INNER_INSERT_TARGET.POSITION_LEVEL != R_INS_SOURCE.POSITION_LEVEL)
                                                                       AND (R_INSERT_TARGET.POSITION_LEVEL < GN_MAX_INTERNAL_POS)
                                                                        THEN
                                                                           IF (R_INS_SOURCE.SOURCE_AMOUNT != R_INNER_INSERT_TARGET.AMOUNT)
                                                                          AND (R_INS_SOURCE.POSITION_LEVEL != GN_MAX_INTERNAL_POS)
                                                                           THEN
                                                                              VV_DEL_TARGET   := 'Y';
                                                                           END IF;
                                                                        END IF;

                                                                        IF (R_INNER_INSERT_TARGET.POSITION_LEVEL <= GN_MAX_INTERNAL_POS
                                                                        AND R_INS_SOURCE.POSITION_LEVEL <= GN_MAX_INTERNAL_POS)
                                                                       AND (R_INS_SOURCE.POSITION_LEVEL != GN_PRE_ADVICE_POS
                                                                        AND R_INNER_INSERT_TARGET.POSITION_LEVEL != GN_PRE_ADVICE_POS)
                                                                        THEN
                                                                           -- when source source reference exist indirect target reference
                                                                           -- all the indirect target otherwise set delete flag for removement
                                                                           -- current matched movements from match processing table.
                                                                           IF PK_MATCHING_PROCESS.FNISCROSSREFEXISTS(
                                                                                 R_INS_SOURCE.HOST_ID,
                                                                                 R_INS_SOURCE.ENTITY_ID,
                                                                                 R_INS_SOURCE.CURRENCY_CODE,
                                                                                 R_INS_SOURCE.MOVEMENT_ID,
                                                                                 R_INNER_INSERT_TARGET.MOVEMENT_ID) = 'Y'
                                                                           THEN
                                                                              -- Ticket 104709
                                                                              VV_DEL_TARGET   := 'N';
                                                                           ELSE
                                                                              DECLARE
                                                                                 VN_CNT   PLS_INTEGER := 0;
                                                                              BEGIN
                                                                                 SELECT COUNT(1)
                                                                                   INTO VN_CNT
                                                                                   FROM P_B_TARGET_MOVEMENTS T
                                                                                  WHERE T.POS_LEVEL != R_INNER_INSERT_TARGET.POSITION_LEVEL
                                                                                    AND T.QUALITYFLAG = R_INNER_INSERT_TARGET.QTY
                                                                                    AND R_INSERT_TARGET.MATCH_STATUS != 'C';

                                                                                 IF VN_CNT = 0
                                                                                 THEN
                                                                                    VV_DEL_TARGET   := 'Y';
                                                                                 END IF;
                                                                              END;
                                                                           END IF;
                                                                        END IF;
                                                                     ELSE
                                                                        SELECT LOWEST_POSITION_LEVEL
                                                                          INTO VN_MATCH_LOW_POSITION
                                                                          FROM P_MATCH
                                                                         WHERE HOST_ID = R_INS_SOURCE.HOST_ID
                                                                           AND ENTITY_ID = R_INS_SOURCE.ENTITY_ID
                                                                           AND MATCH_ID = R_INNER_INSERT_TARGET.MATCH_ID;

                                                                        IF VN_MATCH_LOW_POSITION < GN_MAX_INTERNAL_POS
                                                                       AND VN_MATCH_LOW_POSITION != 4
                                                                        THEN
                                                                           VV_DEL_TARGET   := 'Y';
                                                                        END IF;
                                                                     END IF;

                                                                     IF VV_DEL_TARGET != 'Y'
                                                                     THEN
                                                                        IF VV_DEL_INNER_TARGET = 'N'
                                                                        THEN
                                                                           VN_INNER_TGT_EXTERNAL   := 0;
                                                                           VN_INNER_TGT_INTERNAL   := 0;

                                                                           IF R_INNER_INSERT_TARGET.POSITION_LEVEL > GN_MAX_INTERNAL_POS
                                                                           THEN
                                                                              VN_INNER_TGT_EXTERNAL   := R_INNER_INSERT_TARGET.POSITION_LEVEL;
                                                                           ELSE
                                                                              VN_INNER_TGT_INTERNAL   := R_INNER_INSERT_TARGET.POSITION_LEVEL;
                                                                           END IF;

                                                                           -- set target position quality variable
                                                                           CASE R_INNER_INSERT_TARGET.POSITION_LEVEL
                                                                              WHEN 1
                                                                              THEN
                                                                                 VN_TGT_POS1_QTY   := R_INNER_INSERT_TARGET.QTY;
                                                                              WHEN 2
                                                                              THEN
                                                                                 VN_TGT_POS2_QTY   := R_INNER_INSERT_TARGET.QTY;
                                                                              WHEN 3
                                                                              THEN
                                                                                 VN_TGT_POS3_QTY   := R_INNER_INSERT_TARGET.QTY;
                                                                              WHEN 4
                                                                              THEN
                                                                                 VN_TGT_POS4_QTY   := R_INNER_INSERT_TARGET.QTY;
                                                                              WHEN 5
                                                                              THEN
                                                                                 VN_TGT_POS5_QTY   := R_INNER_INSERT_TARGET.QTY;
                                                                              WHEN 6
                                                                              THEN
                                                                                 VN_TGT_POS6_QTY   := R_INNER_INSERT_TARGET.QTY;
                                                                              WHEN 7
                                                                              THEN
                                                                                 VN_TGT_POS7_QTY   := R_INNER_INSERT_TARGET.QTY;
                                                                              WHEN 8
                                                                              THEN
                                                                                 VN_TGT_POS8_QTY   := R_INNER_INSERT_TARGET.QTY;
                                                                              WHEN 9
                                                                              THEN
                                                                                 VN_TGT_POS9_QTY   := R_INNER_INSERT_TARGET.QTY;
                                                                           END CASE;

                                                                           -- Assign indirect target value date is source value date
                                                                           -- when indirect target OPEN flag is 'Y' otherwise
                                                                           -- use indirect target value date
                                                                           IF R_INNER_INSERT_TARGET.OPEN = 'N'
                                                                           THEN
                                                                              V_VALUE_DATE   := R_INNER_INSERT_TARGET.VALUE_DATE;
                                                                           ELSE
                                                                              V_VALUE_DATE   := R_INS_SOURCE.VALUE_DATE;
                                                                           END IF;

                                                                           -- inserting indirect target
                                                                           SP_ADD_P_B_TARGETS(R_INNER_INSERT_TARGET.MOVEMENT_ID,
                                                                                              R_INNER_INSERT_TARGET.HOST_ID,
                                                                                              R_INNER_INSERT_TARGET.ENTITY_ID,
                                                                                              R_INNER_INSERT_TARGET.CURRENCY_CODE,
                                                                                              R_INNER_INSERT_TARGET.MATCH_ID,
                                                                                              R_INNER_INSERT_TARGET.MATCH_STATUS,
                                                                                              R_INNER_INSERT_TARGET.PREDICT_STATUS,
                                                                                              R_INNER_INSERT_TARGET.POSITION_LEVEL,
                                                                                              V_VALUE_DATE,
                                                                                              R_INNER_INSERT_TARGET.COUNTERPARTY_ID,
                                                                                              R_INNER_INSERT_TARGET.BENEFICIARY_ID,
                                                                                              R_INNER_INSERT_TARGET.CUSTODIAN_ID,
                                                                                              R_INNER_INSERT_TARGET.BOOKCODE,
                                                                                              R_INNER_INSERT_TARGET.AMOUNT,
                                                                                              R_INNER_INSERT_TARGET.VALID,
                                                                                              R_INNER_INSERT_TARGET.QTY,
                                                                                              R_INNER_INSERT_TARGET.ACCOUNT_ID,
                                                                                              'Y',
                                                                                              'Y',
                                                                                              R_INNER_INSERT_TARGET.MATCHING_PARTY,
                                                                                              R_INNER_INSERT_TARGET.INITIAL_PREDICT_STATUS);
                                                                        END IF;
                                                                     END IF;
                                                                  EXCEPTION
                                                                     WHEN DUP_VAL_ON_INDEX
                                                                     THEN
                                                                        VV_ERR_LOC   := '660';
                                                                  END;

                                                                  -- delete the target from match processing table
                                                                  -- when indirect target does not passed the business rule
                                                                  -- and also reset the quality variables for the target positions.
                                                                  IF VV_DEL_TARGET = 'Y'
                                                                  THEN
                                                                     DECLARE
                                                                        CURSOR CR_DEL_MATCHED_TARGETS
                                                                        IS
                                                                           SELECT MOVEMENT_ID, POS_LEVEL
                                                                             FROM P_B_TARGET_MOVEMENTS
                                                                            WHERE MATCH_ID = R_INNER_INSERT_TARGET.MATCH_ID;
                                                                     BEGIN
                                                                        FOR R_DEL_MATCHED_TARGETS IN CR_DEL_MATCHED_TARGETS
                                                                        LOOP
                                                                           -- delete movements whose match id that does not satisfy references rule

                                                                           DELETE FROM P_B_TARGET_MOVEMENTS
                                                                                 WHERE MOVEMENT_ID = R_DEL_MATCHED_TARGETS.MOVEMENT_ID
                                                                             RETURNING HOST_ID, ENTITY_ID, MOVEMENT_ID,
                                                                                       CURRENCY_CODE
                                                                                  BULK COLLECT INTO VDELSUPPORTTAB;

                                                                           SPREMOVESUPPORTTABLESREC(VDELSUPPORTTAB);

                                                                           COMMIT;

                                                                           -- vv_del_target := 'N';
                                                                           -- reset deleted target position quality variables
                                                                           CASE R_DEL_MATCHED_TARGETS.POS_LEVEL
                                                                              WHEN 1
                                                                              THEN
                                                                                 VN_TGT_POS1_QTY   := 0;
                                                                              WHEN 2
                                                                              THEN
                                                                                 VN_TGT_POS2_QTY   := 0;
                                                                              WHEN 3
                                                                              THEN
                                                                                 VN_TGT_POS3_QTY   := 0;
                                                                              WHEN 4
                                                                              THEN
                                                                                 VN_TGT_POS4_QTY   := 0;
                                                                              WHEN 5
                                                                              THEN
                                                                                 VN_TGT_POS5_QTY   := 0;
                                                                              WHEN 6
                                                                              THEN
                                                                                 VN_TGT_POS6_QTY   := 0;
                                                                              WHEN 7
                                                                              THEN
                                                                                 VN_TGT_POS7_QTY   := 0;
                                                                              WHEN 8
                                                                              THEN
                                                                                 VN_TGT_POS8_QTY   := 0;
                                                                              WHEN 9
                                                                              THEN
                                                                                 VN_TGT_POS9_QTY   := 0;
                                                                           END CASE;
                                                                        END LOOP;
                                                                     END;

                                                                     VN_REF1_TGT1    := '~~~~~~~~~~~~~';
                                                                     VV_DEL_TARGET   := 'N';
                                                                     EXIT;
                                                                  END IF;
                                                               END LOOP;
                                                            END;
                                                         END IF;

                                                         VV_ERR_LOC   := '670';
                                                      END IF;
                                                   END IF;
                                                END IF;
                                          END;
                                       END IF;

                                       COMMIT;
                                    END LOOP;
                                 END IF;
                              END;

                              -- close target cursor
                              IF VV_TARGET_POS_CHK != 'N'
                              THEN
                                 VV_ERR_LOC   := '690';

                                 IF VV_TARGET_POS_CHK = 'H'
                                 THEN
                                    CLOSE CR_INSERT_TARGET_SUPP_PASS;
                                 ELSE
                                    IF VV_TARGET_POS_CHK = 'P'
                                    THEN
                                       CLOSE CR_INS_TARGET_PREADVICE;
                                    ELSE
                                       IF L_FORCE_REFERENCE_ONLY = 'Y'
                                       THEN
                                          CLOSE CR_INS_TARGET_REF_PASS;
                                       ELSE
                                          CLOSE CR_INS_TARGET_VD_AMT_PASS;
                                       END IF;
                                    END IF;
                                 END IF;

                                 IF VV_DEL_TARGET != 'Y'
                                 THEN
                                    VV_ERR_LOC   := '700';

                                    DECLARE
                                       MATCH_QUALITY   NUMBER(10);
                                    BEGIN
                                       L_MATCHID         := 0;
                                       VV_ERR_LOC        := '710';

                                       -- get previous match id from match processing table
                                       -- and assign that match id in match id variable
                                       DECLARE
                                          CURSOR CR_PREV_MATCH_IN
                                          IS
                                             SELECT DISTINCT MATCH_ID
                                               FROM P_B_TARGET_MOVEMENTS
                                              WHERE MATCH_ID IS NOT NULL;
                                       BEGIN
                                          VV_ERR_LOC   := '720';

                                          FOR R_PREV_MATCH_IN IN CR_PREV_MATCH_IN
                                          LOOP
                                             L_MATCHID   := R_PREV_MATCH_IN.MATCH_ID;
                                          END LOOP;

                                          VV_ERR_LOC   := '730';
                                       EXCEPTION
                                          WHEN NO_DATA_FOUND
                                          THEN
                                             NULL;
                                       END;

                                       VV_ERR_LOC        := '740';

                                       -- select the match quality and count from match processing table
                                       -- for direct target movements
                                       -- when count > 0 then insert the source movement into
                                       -- match processing table.
                                       SELECT COUNT(*), MAX(QUALITYFLAG)
                                         INTO L_COUNT, MATCH_QUALITY
                                         FROM P_B_TARGET_MOVEMENTS
                                        WHERE VALID IN ('Y', 'T');

                                       VV_ERR_LOC        := '750';
                                       VV_SOURCE_EXIST   := 'N';

                                       IF (L_COUNT > 0)
                                       THEN
                                          VV_ERR_LOC   := '760';
                                          SP_ADD_P_B_TARGETS(R_INS_SOURCE.MOVEMENT_ID,
                                                             R_INS_SOURCE.HOST_ID,
                                                             L_ENTITYID,
                                                             L_CURRENCYCODE,
                                                             NULL,
                                                             'L',
                                                             R_INS_SOURCE.PREDICT_STATUS,
                                                             R_INS_SOURCE.POSITION_LEVEL,
                                                             R_INS_SOURCE.VALUE_DATE,
                                                             R_INS_SOURCE.COUNTERPARTY_ID,
                                                             R_INS_SOURCE.BENEFICIARY_ID,
                                                             R_INS_SOURCE.CUSTODIAN_ID,
                                                             R_INS_SOURCE.BOOKCODE,
                                                             R_INS_SOURCE.SOURCE_AMOUNT,
                                                             'Y',
                                                             MATCH_QUALITY,
                                                             R_INS_SOURCE.ACCOUNT_ID,
                                                             'N',
                                                             'Y',
                                                             R_INS_SOURCE.MATCHING_PARTY,
                                                             R_INS_SOURCE.INITIAL_PREDICT_STATUS);
                                          VV_ERR_LOC   := '770';

                                          -- START : Mantis 771
                                          -- When source position > position level threshold
                                          -- check the source references with target references
                                          IF R_INS_SOURCE.POSITION_LEVEL > GN_MAX_INTERNAL_POS
                                          THEN
                                             DECLARE
                                                VN_INT_REF_FOUND   CHAR(1);

                                                CURSOR CR_EXT_REF_CHK
                                                IS
                                                   SELECT MATCH_ID, MOVEMENT_ID
                                                     FROM P_B_TARGET_MOVEMENTS
                                                    WHERE POS_LEVEL > GN_MAX_INTERNAL_POS
                                                      AND MATCH_ID IS NOT NULL;

                                                CURSOR CR_INT_REF_CHK
                                                IS
                                                   SELECT MATCH_ID, MOVEMENT_ID
                                                     FROM P_B_TARGET_MOVEMENTS
                                                    WHERE POS_LEVEL <= GN_MAX_INTERNAL_POS
                                                      AND MATCH_ID IS NOT NULL;
                                             BEGIN
                                                VN_INT_REF_FOUND   := 'N';

                                                -- check the external targets references
                                                -- when matched proceed further
                                                -- else check with internal targets references
                                                -- when matched remove the existing external targets
                                                -- when internal references not matched then
                                                -- delete the movements of external targets.
                                                FOR R_EXT_REF_CHK IN CR_EXT_REF_CHK
                                                LOOP
                                                   IF PK_MATCHING_PROCESS.FNISCROSSREFEXISTS(R_INS_SOURCE.HOST_ID,
                                                                                             R_INS_SOURCE.ENTITY_ID,
                                                                                             R_INS_SOURCE.CURRENCY_CODE,
                                                                                             R_INS_SOURCE.MOVEMENT_ID,
                                                                                             R_EXT_REF_CHK.MOVEMENT_ID) = 'Y'
                                                   THEN
                                                      NULL;
                                                   ELSE
                                                      FOR R_INT_REF_CHK IN CR_INT_REF_CHK
                                                      LOOP
                                                         IF PK_MATCHING_PROCESS.FNISCROSSREFEXISTS(R_INS_SOURCE.HOST_ID,
                                                                                                   R_INS_SOURCE.ENTITY_ID,
                                                                                                   R_INS_SOURCE.CURRENCY_CODE,
                                                                                                   R_INS_SOURCE.MOVEMENT_ID,
                                                                                                   R_INT_REF_CHK.MOVEMENT_ID) = 'Y'
                                                         THEN
                                                            VN_INT_REF_FOUND   := 'Y';

                                                            DELETE FROM P_B_TARGET_MOVEMENTS
                                                                  WHERE MATCH_ID = R_EXT_REF_CHK.MATCH_ID
                                                                    AND POS_LEVEL > GN_MAX_INTERNAL_POS
                                                              RETURNING HOST_ID, ENTITY_ID, MOVEMENT_ID,
                                                                        CURRENCY_CODE
                                                                   BULK COLLECT INTO VDELSUPPORTTAB;

                                                            SPREMOVESUPPORTTABLESREC(VDELSUPPORTTAB);

                                                            COMMIT;
                                                            EXIT;
                                                         END IF;
                                                      -- Exit for loop (r_int_ref_chk)
                                                      END LOOP;

                                                      IF VN_INT_REF_FOUND = 'N'
                                                      THEN
                                                         DELETE FROM P_B_TARGET_MOVEMENTS
                                                               WHERE MATCH_ID = R_EXT_REF_CHK.MATCH_ID
                                                           RETURNING HOST_ID, ENTITY_ID, MOVEMENT_ID,
                                                                     CURRENCY_CODE
                                                                BULK COLLECT INTO VDELSUPPORTTAB;

                                                         SPREMOVESUPPORTTABLESREC(VDELSUPPORTTAB);

                                                         COMMIT;
                                                      END IF;
                                                   END IF;
                                                END LOOP;
                                             END;
                                          END IF;

                                          -- END  : Mantis 771

                                          -- START: MI 695 - Enrich beneficiary of P_B_TARGETMOVEMENTS (for enriching
                                          -- the beneficiary_id even if movements are offered)
                                          -- Enrich the beneficiary of the internal movement when beneficiary has null value
                                          -- with not null beneficiary when all the internal references are same.
                                          DECLARE
                                             VN_BENEFICIARY_NULL_CNT   NUMBER;
                                             VN_BENEFICIARY_CNT        NUMBER;
                                             VN_POS_LEVEL_CNT          NUMBER;
                                          BEGIN
                                             VV_ERR_LOC   := '780';

                                             -- get the beneficiary id note exist count for internal position level
                                             SELECT COUNT(1)
                                               INTO VN_BENEFICIARY_NULL_CNT
                                               FROM P_B_TARGET_MOVEMENTS
                                              WHERE POS_LEVEL <= GN_MAX_INTERNAL_POS
                                                AND BENEFICIARY_ID IS NULL;

                                             VV_ERR_LOC   := '790';

                                             IF VN_BENEFICIARY_NULL_CNT > 0
                                             THEN
                                                VV_ERR_LOC   := '800';

                                                -- get the beneficiary id exist count for internal position level
                                                SELECT COUNT(DISTINCT BENEFICIARY_ID)
                                                  INTO VN_BENEFICIARY_CNT
                                                  FROM P_B_TARGET_MOVEMENTS
                                                 WHERE POS_LEVEL < GN_MAX_INTERNAL_POS
                                                   AND BENEFICIARY_ID IS NOT NULL;

                                                VV_ERR_LOC   := '810';

                                                IF VN_BENEFICIARY_CNT = 1
                                                THEN
                                                   VV_ERR_LOC   := '820';

                                                   BEGIN
                                                      -- get the count of each external position levels
                                                      SELECT COUNT(DISTINCT POS_LEVEL)
                                                        INTO VN_POS_LEVEL_CNT
                                                        FROM P_B_TARGET_MOVEMENTS
                                                       WHERE POS_LEVEL <= GN_MAX_INTERNAL_POS;

                                                      VV_ERR_LOC   := '830';
                                                   EXCEPTION
                                                      WHEN NO_DATA_FOUND
                                                      THEN
                                                         NULL;
                                                   END;

                                                   VV_ERR_LOC   := '840';

                                                   -- enrich beneficiary id whose beneficiary is null
                                                   -- for internal movements.
                                                   IF VN_POS_LEVEL_CNT > 1
                                                   THEN
                                                      VV_ERR_LOC   := '850';
                                                      VV_BEN_ENRICHED      :=
                                                         FN_ENRICH_BENEFICIARY(R_INS_SOURCE.HOST_ID,
                                                                               R_INS_SOURCE.ENTITY_ID,
                                                                               R_INS_SOURCE.CURRENCY_CODE,
                                                                               GN_MAX_INTERNAL_POS,
                                                                               GN_MAX_INTERNAL_POS);
                                                   END IF;
                                                END IF;
                                             END IF;

                                             VV_ERR_LOC   := '860';
                                          EXCEPTION
                                             WHEN NO_DATA_FOUND
                                             THEN
                                                NULL;
                                          END;

                                          VV_ERR_LOC   := '870';

                                          -- Reading the match quality
                                          -- based on the highest position quality settings
                                          -- when currency source is not a highest position
                                          -- match processing table.
                                          DECLARE
                                             VN_MAX_POS_LVL           NUMBER(1);
                                             VN_MIN_POS_LVL           NUMBER(1);
                                             VN_MOVEMENT_ID_IN        NUMBER;
                                             VN_INNER_MATCH_SRC_POS   NUMBER := 0;
                                             VN_PROCESS_POSITION      NUMBER(1);
                                             VN_MAX_POS_MVMT_COUNT    PLS_INTEGER;

                                             -- get the position level, count, max and min quality
                                             -- for positions having count > 1
                                             CURSOR CR_DEL_MIN_QUALITY_MVMTS
                                             IS
                                                  SELECT POS_LEVEL, COUNT(1) CNT, MIN(QUALITYFLAG) MIN_QUALITY_FLAG,
                                                         MAX(QUALITYFLAG) MAX_QUALITY_FLAG
                                                    FROM P_B_TARGET_MOVEMENTS
                                                GROUP BY POS_LEVEL
                                                  HAVING COUNT(1) > 1;
                                          BEGIN
                                             VV_ERR_LOC   := '880';

                                             -- get the max and min position levels
                                             SELECT MAX(POS_LEVEL), MIN(POS_LEVEL)
                                               INTO VN_MAX_POS_LVL, VN_MIN_POS_LVL
                                               FROM P_B_TARGET_MOVEMENTS;

                                             VV_ERR_LOC   := '890';

                                             -- get the movements count in max position level
                                             -- delete when count > 1 for lowest quality
                                             SELECT COUNT(1)
                                               INTO VN_MAX_POS_MVMT_COUNT
                                               FROM P_B_TARGET_MOVEMENTS
                                              WHERE POS_LEVEL = VN_MAX_POS_LVL;

                                             IF VN_MAX_POS_MVMT_COUNT > 1
                                             THEN
                                                DELETE FROM P_B_TARGET_MOVEMENTS
                                                      WHERE POS_LEVEL = VN_MAX_POS_LVL
                                                        AND QUALITYFLAG != 5;
                                             END IF;

                                             -- call inner matching when source position level
                                             -- higher than position level threshold
                                             IF R_INS_SOURCE.POSITION_LEVEL >= GN_MAX_INTERNAL_POS
                                             THEN
                                                VV_ERR_LOC   := '900';

                                                IF VN_MAX_POS_LVL = R_INS_SOURCE.POSITION_LEVEL
                                                THEN
                                                   VN_MOVEMENT_ID_IN   := R_INS_SOURCE.MOVEMENT_ID;
                                                ELSE
                                                   -- select the amount, match status and quality
                                                   -- for all the movement > source position and
                                                   -- match id is not null.
                                                   DECLARE
                                                      CURSOR CR_INNER_MATCH_SRC_POS
                                                      IS
                                                         SELECT DISTINCT AMOUNT AS MAX_POS_AMOUNT, MATCH_STATUS AS MAX_POS_STATUS,
                                                                         QUALITYFLAG AS MAX_POS_QLTY
                                                           FROM P_B_TARGET_MOVEMENTS
                                                          WHERE MATCH_ID IS NOT NULL
                                                            AND POS_LEVEL > R_INS_SOURCE.POSITION_LEVEL;
                                                   BEGIN
                                                      FOR INREC IN CR_INNER_MATCH_SRC_POS
                                                      LOOP
                                                         -- when max position status is confirm and max position amount
                                                         -- not same as source amount and max position quality not A or B
                                                         -- then [inner matching source position = source position]

                                                         IF INREC.MAX_POS_STATUS = 'C'
                                                         THEN
                                                            IF INREC.MAX_POS_AMOUNT != R_INS_SOURCE.SOURCE_AMOUNT
                                                           AND (INREC.MAX_POS_QLTY != 5
                                                             OR INREC.MAX_POS_QLTY != 4)
                                                            THEN
                                                               VN_INNER_MATCH_SRC_POS   := R_INS_SOURCE.POSITION_LEVEL;
                                                               EXIT;
                                                            END IF;
                                                         END IF;

                                                         -- when match position status is offered and max position amount
                                                         -- not same as source amount then [inner matching source position = source position]
                                                         IF INREC.MAX_POS_STATUS = 'M'
                                                         THEN
                                                            IF INREC.MAX_POS_AMOUNT != R_INS_SOURCE.SOURCE_AMOUNT
                                                            THEN
                                                               VN_INNER_MATCH_SRC_POS   := R_INS_SOURCE.POSITION_LEVEL;
                                                               EXIT;
                                                            END IF;
                                                         END IF;
                                                      END LOOP;
                                                   END;

                                                   VN_MOVEMENT_ID_IN   := 0;
                                                END IF;

                                                IF VN_INNER_MATCH_SRC_POS != 0
                                                THEN
                                                   VN_PROCESS_POSITION   := VN_INNER_MATCH_SRC_POS;
                                                ELSE
                                                   VN_PROCESS_POSITION   := VN_MAX_POS_LVL;
                                                END IF;

                                                VV_ERR_LOC   := '910';
                                                -- inner matching for setting the match quality base on the highest position
                                                -- in the match processing table
                                                SP_INNER_MATCHING(R_INS_SOURCE.HOST_ID,
                                                                  R_INS_SOURCE.ENTITY_ID,
                                                                  R_INS_SOURCE.CURRENCY_CODE,
                                                                  VN_PROCESS_POSITION,
                                                                  GN_MAX_INTERNAL_POS,
                                                                  R_INS_SOURCE.POSITION_LEVEL,
                                                                  L_TOLERANCE,
                                                                  VN_MOVEMENT_ID_IN,
                                                                  VN_MIN_POS_LVL);
                                             END IF;

                                             VV_ERR_LOC   := '920';

                                             -- Preadvice position check
                                             DECLARE
                                                VN_PRE_ADV_POSITION   NUMBER;
                                                VN_PRE_ADV_QUALITY    NUMBER;
                                                VN_PRE_ADV_MATCHID    NUMBER;

                                                -- cursor will get the quality and match id of the
                                                -- preadvice search positions that does not have
                                                -- any preadvices in that current match id in the
                                                -- match processing table
                                                CURSOR CR_MATCHED_INTERNAL_MVMTS
                                                IS
                                                   SELECT QUALITYFLAG, MATCH_ID
                                                     FROM P_B_TARGET_MOVEMENTS
                                                    WHERE MATCH_ID IS NOT NULL
                                                      AND ((POS_LEVEL = GN_MAX_INTERNAL_POS)
                                                        OR (POS_LEVEL = 4))
                                                      AND MATCH_ID != (SELECT MATCH_ID
                                                                         FROM P_B_TARGET_MOVEMENTS
                                                                        WHERE POS_LEVEL = VN_PRE_ADV_POSITION);
                                             BEGIN
                                                VV_ERR_LOC   := '755-a';

                                                -- when source position > position level threshold
                                                -- do the below validations
                                                IF R_INS_SOURCE.POSITION_LEVEL > GN_MAX_INTERNAL_POS
                                                THEN
                                                   VV_ERR_LOC   := '755-b';

                                                   BEGIN
                                                      -- select the position level and match quality
                                                      -- of the preadvice position and match id is not empty
                                                      SELECT POS_LEVEL, QUALITYFLAG
                                                        INTO VN_PRE_ADV_POSITION, VN_PRE_ADV_QUALITY
                                                        FROM P_B_TARGET_MOVEMENTS
                                                       WHERE MATCH_ID IS NOT NULL
                                                         AND POS_LEVEL = GN_PRE_ADVICE_POS;

                                                      VV_ERR_LOC   := '755-bb';
                                                   EXCEPTION
                                                      WHEN NO_DATA_FOUND
                                                      THEN
                                                         NULL;
                                                      WHEN TOO_MANY_ROWS
                                                      THEN
                                                         -- when more than one preadvice position
                                                         -- delete one of them

                                                         DELETE FROM P_B_TARGET_MOVEMENTS
                                                               WHERE MATCH_ID NOT IN (SELECT MIN(MATCH_ID)
                                                                                        FROM P_B_TARGET_MOVEMENTS
                                                                                       WHERE POS_LEVEL = GN_PRE_ADVICE_POS)
                                                                 AND MATCH_ID IS NOT NULL
                                                                 AND POS_LEVEL = GN_PRE_ADVICE_POS
                                                           RETURNING HOST_ID, ENTITY_ID, MOVEMENT_ID,
                                                                     CURRENCY_CODE
                                                                BULK COLLECT INTO VDELSUPPORTTAB;

                                                         SPREMOVESUPPORTTABLESREC(VDELSUPPORTTAB);

                                                         COMMIT;

                                                         -- select the position level and match quality
                                                         -- of the preadvice position and match id is not empty
                                                         SELECT POS_LEVEL, QUALITYFLAG
                                                           INTO VN_PRE_ADV_POSITION, VN_PRE_ADV_QUALITY
                                                           FROM P_B_TARGET_MOVEMENTS
                                                          WHERE MATCH_ID IS NOT NULL
                                                            AND POS_LEVEL = GN_PRE_ADVICE_POS;
                                                   END;

                                                   VV_ERR_LOC   := '755-c';

                                                   -- delete the other internal movements
                                                   -- whose quality flag is not in 5 or 4
                                                   -- and position lesser than position level threshold
                                                   -- and it does not have any match id
                                                   IF VN_PRE_ADV_POSITION = GN_PRE_ADVICE_POS
                                                   THEN
                                                      VV_ERR_LOC   := '755-d';

                                                      DELETE FROM P_B_TARGET_MOVEMENTS
                                                            WHERE POS_LEVEL < GN_MAX_INTERNAL_POS
                                                              AND MATCH_ID IS NULL
                                                              AND QUALITYFLAG NOT IN (5, 4)
                                                        RETURNING HOST_ID, ENTITY_ID, MOVEMENT_ID,
                                                                  CURRENCY_CODE
                                                             BULK COLLECT INTO VDELSUPPORTTAB;

                                                      VV_ERR_LOC   := '755-da';
                                                      SPREMOVESUPPORTTABLESREC(VDELSUPPORTTAB);

                                                      COMMIT;
                                                   END IF;

                                                   VV_ERR_LOC   := '755-e';

                                                   -- select match id and quality for the position
                                                   -- is position level threshold and match id is match id of
                                                   -- preadvice position
                                                   SELECT QUALITYFLAG, MATCH_ID
                                                     INTO VN_PRE_ADV_QUALITY, VN_PRE_ADV_MATCHID
                                                     FROM P_B_TARGET_MOVEMENTS
                                                    WHERE MATCH_ID IS NOT NULL
                                                      AND POS_LEVEL = GN_MAX_INTERNAL_POS
                                                      AND MATCH_ID = (SELECT MATCH_ID
                                                                        FROM P_B_TARGET_MOVEMENTS
                                                                       WHERE POS_LEVEL = VN_PRE_ADV_POSITION);

                                                   VV_ERR_LOC   := '755-f';

                                                   -- select the preadvice position
                                                   SELECT MIN(POS_LEVEL)
                                                     INTO VN_MIN_MATCHED_PRE_ADV_POS
                                                     FROM P_B_TARGET_MOVEMENTS
                                                    WHERE MATCH_ID = VN_PRE_ADV_MATCHID;

                                                   VV_ERR_LOC   := '755-g';

                                                   -- delete the preadvice position when
                                                   -- quality flag of other matched internal movement > preadvice movement quality
                                                   -- otherwise delete the other matched internal movements
                                                   FOR R_MATCHED_INTERNAL_MVMTS IN CR_MATCHED_INTERNAL_MVMTS
                                                   LOOP
                                                      VV_ERR_LOC   := '755-h';

                                                      IF R_MATCHED_INTERNAL_MVMTS.QUALITYFLAG > VN_PRE_ADV_QUALITY
                                                      THEN
                                                         VV_ERR_LOC   := '755-i';

                                                         DELETE FROM P_B_TARGET_MOVEMENTS
                                                               WHERE MATCH_ID = VN_PRE_ADV_MATCHID
                                                           RETURNING HOST_ID, ENTITY_ID, MOVEMENT_ID,
                                                                     CURRENCY_CODE
                                                                BULK COLLECT INTO VDELSUPPORTTAB;

                                                         SPREMOVESUPPORTTABLESREC(VDELSUPPORTTAB);
                                                      ELSE
                                                         VV_ERR_LOC   := '755-j';

                                                         DELETE FROM P_B_TARGET_MOVEMENTS
                                                               WHERE MATCH_ID = R_MATCHED_INTERNAL_MVMTS.MATCH_ID
                                                           RETURNING HOST_ID, ENTITY_ID, MOVEMENT_ID,
                                                                     CURRENCY_CODE
                                                                BULK COLLECT INTO VDELSUPPORTTAB;

                                                         SPREMOVESUPPORTTABLESREC(VDELSUPPORTTAB);
                                                      END IF;

                                                      VV_ERR_LOC   := '755-k';
                                                      COMMIT;
                                                   END LOOP;
                                                END IF;
                                             EXCEPTION
                                                WHEN NO_DATA_FOUND
                                                THEN
                                                   NULL;
                                             END;

                                             VV_ERR_LOC   := '1050';

                                             -- Deleting the lowest quality in each positions
                                             -- leaving highest match quality for each position
                                             FOR R_DEL_MIN_QUALITY_MVMTS IN CR_DEL_MIN_QUALITY_MVMTS
                                             LOOP
                                                VV_ERR_LOC   := '1060';

                                                IF ((R_DEL_MIN_QUALITY_MVMTS.MIN_QUALITY_FLAG != R_DEL_MIN_QUALITY_MVMTS.MAX_QUALITY_FLAG)
                                                AND R_DEL_MIN_QUALITY_MVMTS.CNT >= 2)
                                                THEN
                                                   VV_ERR_LOC   := '1070';

                                                   IF R_DEL_MIN_QUALITY_MVMTS.POS_LEVEL <= GN_MAX_INTERNAL_POS
                                                   THEN
                                                      VV_ERR_LOC   := '1080';

                                                      IF VV_BEN_ENRICHED != '0'
                                                      THEN
                                                         VV_ERR_LOC   := '1090';

                                                         DELETE FROM P_B_TARGET_MOVEMENTS
                                                               WHERE ((POS_LEVEL = R_DEL_MIN_QUALITY_MVMTS.POS_LEVEL
                                                                   AND POS_LEVEL = GN_MAX_INTERNAL_POS
                                                                   AND NVL(BENEFICIARY_ID, '@*@*@*@*@*') != VV_BEN_ENRICHED)
                                                                   OR (POS_LEVEL = R_DEL_MIN_QUALITY_MVMTS.POS_LEVEL
                                                                   AND POS_LEVEL != GN_MAX_INTERNAL_POS
                                                                   AND QUALITYFLAG != R_DEL_MIN_QUALITY_MVMTS.MAX_QUALITY_FLAG))
                                                           RETURNING HOST_ID, ENTITY_ID, MOVEMENT_ID,
                                                                     CURRENCY_CODE
                                                                BULK COLLECT INTO VDELSUPPORTTAB;

                                                         SPREMOVESUPPORTTABLESREC(VDELSUPPORTTAB);
                                                      ELSE
                                                         VV_ERR_LOC   := '1100';

                                                         DELETE FROM P_B_TARGET_MOVEMENTS
                                                               WHERE POS_LEVEL = R_DEL_MIN_QUALITY_MVMTS.POS_LEVEL
                                                                 AND QUALITYFLAG != R_DEL_MIN_QUALITY_MVMTS.MAX_QUALITY_FLAG
                                                           RETURNING HOST_ID, ENTITY_ID, MOVEMENT_ID,
                                                                     CURRENCY_CODE
                                                                BULK COLLECT INTO VDELSUPPORTTAB;

                                                         SPREMOVESUPPORTTABLESREC(VDELSUPPORTTAB);

                                                         COMMIT;
                                                      END IF;

                                                      VV_ERR_LOC   := '1110';
                                                   ELSE
                                                      VV_ERR_LOC   := '1120';

                                                      DELETE FROM P_B_TARGET_MOVEMENTS
                                                            WHERE POS_LEVEL = R_DEL_MIN_QUALITY_MVMTS.POS_LEVEL
                                                              AND QUALITYFLAG = R_DEL_MIN_QUALITY_MVMTS.MIN_QUALITY_FLAG
                                                        RETURNING HOST_ID, ENTITY_ID, MOVEMENT_ID,
                                                                  CURRENCY_CODE
                                                             BULK COLLECT INTO VDELSUPPORTTAB;

                                                      SPREMOVESUPPORTTABLESREC(VDELSUPPORTTAB);
                                                   END IF;

                                                   VV_ERR_LOC   := '1130';
                                                ELSIF ((R_DEL_MIN_QUALITY_MVMTS.MIN_QUALITY_FLAG = R_DEL_MIN_QUALITY_MVMTS.MAX_QUALITY_FLAG)
                                                   AND R_DEL_MIN_QUALITY_MVMTS.POS_LEVEL > GN_MAX_INTERNAL_POS)
                                                THEN
                                                   VV_ERR_LOC   := '1140';

                                                   DELETE FROM P_B_TARGET_MOVEMENTS
                                                         WHERE POS_LEVEL = R_DEL_MIN_QUALITY_MVMTS.POS_LEVEL
                                                           AND ((BENEFICIARY_ID != R_INS_SOURCE.BENEFICIARY_ID)
                                                             OR (BENEFICIARY_ID =
                                                                    DECODE(R_INS_SOURCE.POSITION_LEVEL,
                                                                           GN_MAX_INTERNAL_POS, BENEFICIARY_ID,
                                                                           R_INS_SOURCE.BENEFICIARY_ID)
                                                             AND VALID = 'F'))
                                                     RETURNING HOST_ID, ENTITY_ID, MOVEMENT_ID,
                                                               CURRENCY_CODE
                                                          BULK COLLECT INTO VDELSUPPORTTAB;

                                                   SPREMOVESUPPORTTABLESREC(VDELSUPPORTTAB);
                                                ELSIF ((R_DEL_MIN_QUALITY_MVMTS.MIN_QUALITY_FLAG = R_DEL_MIN_QUALITY_MVMTS.MAX_QUALITY_FLAG)
                                                   AND (R_DEL_MIN_QUALITY_MVMTS.POS_LEVEL != VN_MIN_POS_LVL))
                                                THEN
                                                   VV_ERR_LOC   := '1150';

                                                   DELETE FROM P_B_TARGET_MOVEMENTS
                                                         WHERE POS_LEVEL = R_DEL_MIN_QUALITY_MVMTS.POS_LEVEL
                                                           AND MATCH_STATUS = 'L'
                                                     RETURNING HOST_ID, ENTITY_ID, MOVEMENT_ID,
                                                               CURRENCY_CODE
                                                          BULK COLLECT INTO VDELSUPPORTTAB;

                                                   SPREMOVESUPPORTTABLESREC(VDELSUPPORTTAB);
                                                END IF;
                                             END LOOP;

                                             VV_ERR_LOC   := '1160';
                                          EXCEPTION
                                             WHEN NO_DATA_FOUND
                                             THEN
                                                NULL;
                                          END;

                                          COMMIT;
                                          VV_ERR_LOC   := '1170';

                                          -- when match processing table has more than one existing match
                                          -- keep the lowest match id and remove the remaining matches from them
                                          DECLARE
                                             VN_MATCH_ID_CNT   NUMBER;
                                             VN_MATCH_ID       NUMBER;
                                          BEGIN
                                             VV_ERR_LOC   := '1190';

                                             SELECT COUNT(DISTINCT MATCH_ID) INTO VN_MATCH_ID_CNT FROM P_B_TARGET_MOVEMENTS;

                                             VV_ERR_LOC   := '1200';

                                             IF VN_MATCH_ID_CNT > 1
                                             THEN
                                                VV_ERR_LOC   := '1210';

                                                DELETE FROM P_B_TARGET_MOVEMENTS
                                                      WHERE MATCH_ID IS NOT NULL
                                                        AND MATCH_ID != (SELECT MIN(MATCH_ID) FROM P_B_TARGET_MOVEMENTS)
                                                  RETURNING HOST_ID, ENTITY_ID, MOVEMENT_ID,
                                                            CURRENCY_CODE
                                                       BULK COLLECT INTO VDELSUPPORTTAB;

                                                SPREMOVESUPPORTTABLESREC(VDELSUPPORTTAB);

                                                COMMIT;
                                                VV_ERR_LOC   := '1220';

                                                SELECT MATCH_ID
                                                  INTO VN_MATCH_ID
                                                  FROM P_B_TARGET_MOVEMENTS
                                                 WHERE MATCH_ID IS NOT NULL
                                                   AND ROWNUM = 1;

                                                VV_ERR_LOC   := '1230';
                                                L_MATCHID    := VN_MATCH_ID;
                                             END IF;
                                          EXCEPTION
                                             WHEN NO_DATA_FOUND
                                             THEN
                                                NULL;
                                          END;

                                          VV_ERR_LOC   := '1240';

                                          -- When any of the target movement has same
                                          -- source position then check the quality of the
                                          -- source and target then delete the source
                                          -- when it has lower match quality
                                          DECLARE
                                             VN_TGT_MATCH_ID    NUMBER;
                                             VN_MAX_POS_LEVEL   NUMBER;
                                             VN_SRC_QUALITY     NUMBER;
                                             VN_TGT_QUALITY     NUMBER;
                                          BEGIN
                                             SELECT MAX(POS_LEVEL) INTO VN_MAX_POS_LEVEL FROM P_B_TARGET_MOVEMENTS;

                                             IF VN_MAX_POS_LEVEL > GN_MAX_INTERNAL_POS
                                             THEN
                                                IF R_INS_SOURCE.POSITION_LEVEL = VN_MAX_POS_LEVEL
                                                THEN
                                                   SELECT NVL(MATCH_ID, 0)
                                                     INTO VN_TGT_MATCH_ID
                                                     FROM P_B_TARGET_MOVEMENTS
                                                    WHERE POS_LEVEL = R_INS_SOURCE.POSITION_LEVEL
                                                      AND MOVEMENT_ID != R_INS_SOURCE.MOVEMENT_ID
                                                      AND ROWNUM = 1;

                                                   IF VN_TGT_MATCH_ID > 0
                                                   THEN
                                                      SELECT QUALITYFLAG
                                                        INTO VN_SRC_QUALITY
                                                        FROM P_B_TARGET_MOVEMENTS
                                                       WHERE MOVEMENT_ID = R_INS_SOURCE.MOVEMENT_ID;

                                                      SELECT QUALITYFLAG
                                                        INTO VN_TGT_QUALITY
                                                        FROM P_B_TARGET_MOVEMENTS
                                                       WHERE POS_LEVEL = R_INS_SOURCE.POSITION_LEVEL
                                                         AND MOVEMENT_ID != R_INS_SOURCE.MOVEMENT_ID;

                                                      IF VN_TGT_QUALITY >= VN_SRC_QUALITY
                                                      THEN
                                                         DELETE FROM P_B_TARGET_MOVEMENTS
                                                               WHERE MOVEMENT_ID = R_INS_SOURCE.MOVEMENT_ID;

                                                         COMMIT;
                                                      END IF;
                                                   END IF;
                                                END IF;
                                             END IF;
                                          EXCEPTION
                                             WHEN NO_DATA_FOUND
                                             THEN
                                                NULL;
                                          END;

                                          -- Check for all the previously matched movements exists in current match
                                          DECLARE
                                             VN_MATCHID_CNT   NUMBER;
                                          BEGIN
                                             VV_ERR_LOC   := '1250';

                                             DELETE FROM P_B_TARGET_MOVEMENTS
                                                   WHERE QUALITYFLAG = 0
                                               RETURNING HOST_ID, ENTITY_ID, MOVEMENT_ID,
                                                         CURRENCY_CODE
                                                    BULK COLLECT INTO VDELSUPPORTTAB;

                                             SPREMOVESUPPORTTABLESREC(VDELSUPPORTTAB);

                                             COMMIT;
                                             VV_ERR_LOC   := '1260';

                                             SELECT COUNT(*)
                                               INTO VN_MATCHID_CNT
                                               FROM P_MOVEMENT
                                              WHERE HOST_ID = R_INS_SOURCE.HOST_ID
                                                AND ENTITY_ID = R_INS_SOURCE.ENTITY_ID
                                                AND CURRENCY_CODE = R_INS_SOURCE.CURRENCY_CODE
                                                AND MATCH_ID = L_MATCHID
                                                AND NOT EXISTS
                                                       (SELECT MOVEMENT_ID
                                                          FROM P_B_TARGET_MOVEMENTS
                                                         WHERE MATCH_ID = L_MATCHID
                                                           AND MOVEMENT_ID = P_MOVEMENT.MOVEMENT_ID);

                                             VV_ERR_LOC   := '1270';

                                             IF VN_MATCHID_CNT > 0
                                             THEN
                                                L_MATCHID_PREV   := L_MATCHID;
                                                L_MATCHID        := 0;
                                             END IF;
                                          EXCEPTION
                                             WHEN NO_DATA_FOUND
                                             THEN
                                                L_MATCHID_PREV   := L_MATCHID;
                                                L_MATCHID        := 0;
                                          END;

                                          VV_ERR_LOC   := '1280';

                                          -- Check whether the current source is exists in the quality selection process.
                                          DECLARE
                                             VN_MVMTS_CNT   NUMBER;
                                          BEGIN
                                             SELECT 'Y'
                                               INTO VV_SOURCE_EXIST
                                               FROM P_B_TARGET_MOVEMENTS
                                              WHERE MOVEMENT_ID = R_INS_SOURCE.MOVEMENT_ID;

                                             SELECT COUNT(*) INTO VN_MVMTS_CNT FROM P_B_TARGET_MOVEMENTS;

                                             IF VN_MVMTS_CNT = 1
                                             THEN
                                                VV_SOURCE_EXIST   := 'N';
                                             END IF;
                                          EXCEPTION
                                             WHEN NO_DATA_FOUND
                                             THEN
                                                VV_SOURCE_EXIST   := 'N';
                                          END;

                                          VV_ERR_LOC   := '1290';

                                          -- when source movement exist in the match processing table
                                          -- proceed further otherwise look for next source movement
                                          IF VV_SOURCE_EXIST = 'Y'
                                          THEN
                                             VV_ERR_LOC   := '1300';

                                             DECLARE
                                                VN_MAX_POSITION      NUMBER;
                                                VN_MIN_QUALITY       NUMBER;
                                                VV_MIN_QUALITY_VAR   CHAR(1);
                                             BEGIN
                                                VV_ERR_LOC           := '1310';

                                                -- Get the lowest matchquality and max position level
                                                SELECT MAX(POS_LEVEL), MIN(QUALITYFLAG)
                                                  INTO VN_MAX_POSITION, VN_MIN_QUALITY
                                                  FROM P_B_TARGET_MOVEMENTS;

                                                VV_ERR_LOC           := '1320';
                                                -- get the over al matchquality
                                                VV_MIN_QUALITY_VAR   := PK_MATCHING_PROCESS.FN_GET_L_INT_QTY_VAR(VN_MIN_QUALITY);
                                                VV_ERR_LOC           := '1322';

                                                BEGIN
                                                   IF V_REC_ACTION(VN_MAX_POSITION)(VV_MIN_QUALITY_VAR) != 'N'
                                                   THEN
                                                      VV_ERR_LOC           := '1324';
                                                      VN_STATUS_POSITION   := VN_MAX_POSITION;
                                                   ELSE
                                                      VV_ERR_LOC           := '1326';
                                                      VN_STATUS_POSITION   := R_INS_SOURCE.POSITION_LEVEL;
                                                   END IF;
                                                EXCEPTION
                                                   WHEN NO_DATA_FOUND
                                                   THEN
                                                      VV_ERR_LOC           := '1328';
                                                      VN_STATUS_POSITION   := R_INS_SOURCE.POSITION_LEVEL;
                                                END;

                                                VV_ERR_LOC           := '1330';
                                                -- get the amount total flag for the highest position
                                                V_AMT_TOTAL          := FN_MATCHING_AMOUNT_TOTAL_FLAG(VV_MIN_QUALITY_VAR, VN_STATUS_POSITION);
                                                VV_ERR_LOC           := '1340';

                                                -- when amount total match quality enabled for the highest position
                                                -- validated the amount total with highest position with summation of
                                                -- each other positions.
                                                -- Also delete movements that are not passed amount total validations
                                                IF V_AMT_TOTAL = 'Y'
                                                THEN
                                                   VV_ERR_LOC   := '1350';
                                                   SP_MATCHING_AMOUNT_TOTAL(VN_STATUS_POSITION, L_TOLERANCE);
                                                   VV_ERR_LOC   := '1360';

                                                   FOR R_AMOUNTTOTAL IN (SELECT HOST_ID, ENTITY_ID, CURRENCY_CODE,
                                                                                MOVEMENT_ID
                                                                           FROM P_B_TARGET_MOVEMENTS
                                                                          WHERE VALID != 'T')
                                                   LOOP
                                                      VV_ERR_LOC   := '1370';
                                                      SP_REMOVE_P_B_TARGETS(R_AMOUNTTOTAL.HOST_ID,
                                                                            R_AMOUNTTOTAL.ENTITY_ID,
                                                                            R_AMOUNTTOTAL.CURRENCY_CODE,
                                                                            'N', -- Delete specific supplied movement
                                                                            R_AMOUNTTOTAL.MOVEMENT_ID);
                                                   END LOOP;

                                                   COMMIT;
                                                   VV_ERR_LOC   := '1380';
                                                END IF;

                                                VV_ERR_LOC           := '1390';

                                                -- check for existence of source movement in match processing table
                                                SELECT 'Y'
                                                  INTO VV_SOURCE_EXIST
                                                  FROM P_B_TARGET_MOVEMENTS
                                                 WHERE MOVEMENT_ID = R_INS_SOURCE.MOVEMENT_ID;
                                             EXCEPTION
                                                WHEN NO_DATA_FOUND
                                                THEN
                                                   VV_SOURCE_EXIST   := 'N';
                                             END;

                                             VV_ERR_LOC   := '1400';

                                             -- when source movement exist in match processing table
                                             -- the match the movements in match processing table.
                                             IF VV_SOURCE_EXIST = 'Y'
                                             THEN
                                                DECLARE
                                                   VN_MAX_MOVEMENT_ID      NUMBER := 0;
                                                   VN_MAX_POS_LEVEL        NUMBER := 0;
                                                   VN_MATCH_PROCESS_CURR   VARCHAR2(3);
                                                   VN_MATCH_ID_CNT         NUMBER;
                                                BEGIN
                                                   VV_ERR_LOC   := '1410';

                                                   SELECT COUNT(MATCH_ID)
                                                     INTO VN_MATCH_ID_CNT
                                                     FROM P_B_TARGET_MOVEMENTS
                                                    WHERE MATCH_ID IS NOT NULL;

                                                   VV_ERR_LOC   := '1420';

                                                   IF VN_MATCH_ID_CNT = 0
                                                   THEN
                                                      L_MATCHID_PREV   := L_MATCHID;
                                                      L_MATCHID        := 0;
                                                   END IF;

                                                   SELECT MOVEMENT_ID, POS_LEVEL
                                                     INTO VN_MAX_MOVEMENT_ID, VN_MAX_POS_LEVEL
                                                     FROM P_B_TARGET_MOVEMENTS
                                                    WHERE POS_LEVEL = (SELECT MAX(POS_LEVEL) FROM P_B_TARGET_MOVEMENTS);

                                                   VV_ERR_LOC   := '1430';

                                                   IF VN_MAX_MOVEMENT_ID != 0
                                                   THEN
                                                      VV_ERR_LOC              := '1440';
                                                      -- The function gets the processing currency for match_quality
                                                      -- and match_action selection from the PL/SQL tables
                                                      VN_MATCH_PROCESS_CURR   := R_INS_SOURCE.CURRENCY_CODE;
                                                      VV_ERR_LOC              := '1450';
                                                      -- Call SP_MATCH_UPDATE_ACTIONS
                                                      -- The procedure will create a new match or append to the existing
                                                      -- match depends on the l_matchid variable.
                                                      SP_MATCH_UPDATE_ACTIONS(R_INS_SOURCE.HOST_ID,
                                                                              R_INS_SOURCE.ENTITY_ID,
                                                                              R_INS_SOURCE.CURRENCY_CODE,
                                                                              L_MATCHID,
                                                                              VN_MAX_MOVEMENT_ID,
                                                                              VN_RETN_MATCHID,
                                                                              VV_RETN_STATUS,
                                                                              VV_RETN_QUALITY,
                                                                              VN_RETN_HI_POS,
                                                                              VN_RETN_LOW_POS,
                                                                              VN_STATUS_POSITION,
                                                                              R_INS_SOURCE.POSITION_LEVEL);
                                                      VV_ERR_LOC              := '1460';

                                                      -- Call SP_MATCH_DELETE_ACTIONS
                                                      -- The procedure will reset the match_id and match_status of the
                                                      -- match that is going to be replaced by the current match
                                                      IF L_MATCHID_PREV != 0
                                                      THEN
                                                         SP_MATCH_DELETE_ACTIONS(R_INS_SOURCE.HOST_ID,
                                                                                 R_INS_SOURCE.ENTITY_ID,
                                                                                 R_INS_SOURCE.CURRENCY_CODE,
                                                                                 L_MATCHID_PREV);
                                                      END IF;

                                                      VV_ERR_LOC              := '1470';
                                                   END IF;
                                                EXCEPTION
                                                   WHEN NO_DATA_FOUND
                                                   THEN
                                                      NULL;
                                                END;
                                             END IF;
                                          END IF;
                                       END IF;
                                    END;
                                 END IF;
                              END IF;
                           END IF;
                        END;
                     END IF; --V_SOURCE = 'N'
                  END IF;

                  VV_ERR_LOC              := '1480';

                  -- The following PL/SQL block used for pre-advice matching
                  -- rules for preadvice selection is
                  -- if current source movement is outstanding movement
                  -- it must be a either 6 or 4 otherwise when current source is
                  -- not a outstanding movement then lowest position in that
                  -- match should be 6 or 4.
                  DECLARE --<<PRE_ADVICE>>
                     VV_MATCH_PREADVICE    CHAR(1);
                     VN_RETN_PRE_LOW_POS   NUMBER(1);
                     VN_RETN_PRE_HI_POS    NUMBER(1);
                  -- Variable used to identify pre-advice to be processed or not
                  BEGIN --<<PRE_ADVICE>>
                     VV_ERR_LOC           := '1490';
                     VV_MATCH_PREADVICE   := 'N';

                     -- if source movement 4 or 6 does not match on reference for the first time.
                     IF VN_RETN_MATCHID = 0
                    AND (REGEXP_INSTR(VV_PREADVICE_SEARCH_POSITIONS, R_INS_SOURCE.POSITION_LEVEL) > 0)
                    AND R_INS_SOURCE.TO_MATCH_STAGE = 1
                     THEN
                        VV_ERR_LOC   := '1500';

                        IF VV_PREADV_SEARCH_ALWAYS = 'N'
                       AND VV_PREADVICE_EXPECTED = 'Y'
                        THEN
                           VV_MATCH_PREADVICE   := 'Y';
                        ELSIF VV_PREADV_SEARCH_ALWAYS = 'Y'
                        THEN
                           VV_MATCH_PREADVICE   := 'Y';
                        END IF;
                     ELSIF VN_RETN_MATCHID != 0
                     -- if source movement get matched
                     THEN
                        VV_ERR_LOC   := '1510';

                        IF REGEXP_INSTR(VV_PREADVICE_SEARCH_POSITIONS, VN_RETN_LOW_POS) > 0
                        THEN
                           VV_ERR_LOC   := '1520';

                           IF (VN_MIN_MATCHED_PRE_ADV_POS != 4
                            OR VN_RETN_HI_POS != GN_MAX_INTERNAL_POS)
                           THEN
                              VV_ERR_LOC           := '1530';
                              VV_MATCH_PREADVICE   := 'Y';
                           END IF;
                        END IF;
                     END IF;

                     VV_ERR_LOC           := '1540';

                     IF VV_MATCH_PREADVICE = 'Y'
                     THEN
                        VV_ERR_LOC   := '1550';
                        -- The procedure perform the matching of preadvice position to the
                        -- current confirmed 'A' quality match or current source movement
                        -- if source movement position is 4 or 6
                        SP_PREADVICE_MATCHING(R_INS_SOURCE.HOST_ID,
                                              R_INS_SOURCE.ENTITY_ID,
                                              R_INS_SOURCE.CURRENCY_CODE,
                                              R_INS_SOURCE.VALUE_DATE,
                                              R_INS_SOURCE.ACCOUNT_ID,
                                              R_INS_SOURCE.POSITION_LEVEL,
                                              R_INS_SOURCE.MOVEMENT_ID,
                                              R_INS_SOURCE.SIGN,
                                              ABS(R_INS_SOURCE.SOURCE_AMOUNT),
                                              VN_RETN_MATCHID,
                                              GN_PRE_ADVICE_POS,
                                              VN_RETN_PRE_LOW_POS,
                                              VN_RETN_PRE_HI_POS,
                                              VV_IS_OPEN);

                        IF VN_RETN_PRE_HI_POS != 0
                        THEN
                           VN_RETN_HI_POS   := VN_RETN_PRE_HI_POS;
                        END IF;

                        IF VN_RETN_PRE_LOW_POS != 0
                        THEN
                           VN_RETN_LOW_POS   := VN_RETN_PRE_LOW_POS;
                        END IF;
                     END IF;
                  EXCEPTION
                     WHEN NO_DATA_FOUND
                     THEN
                        NULL;
                  END;

                  -- update the predict status of the match
                  -- when match contains the pre-advice position
                  -- and based on the preadvice predict strategy
                  -- in p_misc_params table.

                  IF VN_PREADV_STRATEGY = 3
                 AND VN_RETN_LOW_POS = GN_PRE_ADVICE_POS
                  THEN
                     IF VN_RETN_HI_POS != 0
                     THEN
                        IF VV_IS_OPEN = 'Y'
                        THEN
                           UPDATE P_MOVEMENT
                              SET PREDICT_STATUS = 'I', UPDATE_USER = 'SYSTEM', UPDATE_DATE = GLOBAL_VAR.SYS_DATE
                            WHERE HOST_ID = R_INS_SOURCE.HOST_ID
                              AND ENTITY_ID = R_INS_SOURCE.ENTITY_ID
                              AND CURRENCY_CODE = R_INS_SOURCE.CURRENCY_CODE
                              AND MATCH_ID = VN_RETN_MATCHID
                              AND POSITION_LEVEL = GN_PRE_ADVICE_POS;

                           UPDATE P_MOVEMENT
                              SET PREDICT_STATUS = 'E', UPDATE_USER = 'SYSTEM', UPDATE_DATE = GLOBAL_VAR.SYS_DATE
                            WHERE HOST_ID = R_INS_SOURCE.HOST_ID
                              AND ENTITY_ID = R_INS_SOURCE.ENTITY_ID
                              AND CURRENCY_CODE = R_INS_SOURCE.CURRENCY_CODE
                              AND MATCH_ID = VN_RETN_MATCHID
                              AND POSITION_LEVEL != GN_PRE_ADVICE_POS;
                        ELSE
                           UPDATE P_MOVEMENT
                              SET PREDICT_STATUS = 'I', UPDATE_USER = 'SYSTEM', UPDATE_DATE = GLOBAL_VAR.SYS_DATE
                            WHERE HOST_ID = R_INS_SOURCE.HOST_ID
                              AND ENTITY_ID = R_INS_SOURCE.ENTITY_ID
                              AND CURRENCY_CODE = R_INS_SOURCE.CURRENCY_CODE
                              AND VALUE_DATE = R_INS_SOURCE.VALUE_DATE
                              AND MATCH_ID = VN_RETN_MATCHID
                              AND POSITION_LEVEL = GN_PRE_ADVICE_POS;

                           UPDATE P_MOVEMENT
                              SET PREDICT_STATUS = 'E', UPDATE_USER = 'SYSTEM', UPDATE_DATE = GLOBAL_VAR.SYS_DATE
                            WHERE HOST_ID = R_INS_SOURCE.HOST_ID
                              AND ENTITY_ID = R_INS_SOURCE.ENTITY_ID
                              AND CURRENCY_CODE = R_INS_SOURCE.CURRENCY_CODE
                              AND VALUE_DATE = R_INS_SOURCE.VALUE_DATE
                              AND MATCH_ID = VN_RETN_MATCHID
                              AND POSITION_LEVEL != GN_PRE_ADVICE_POS;
                        END IF;

                        COMMIT;
                     END IF;
                  ELSIF VN_PREADV_STRATEGY = 2
                    AND VN_RETN_LOW_POS = GN_PRE_ADVICE_POS
                  THEN
                     IF VN_RETN_HI_POS != 0
                     THEN
                        IF VV_IS_OPEN = 'Y'
                        THEN
                           UPDATE P_MOVEMENT
                              SET PREDICT_STATUS = 'I', UPDATE_USER = 'SYSTEM', UPDATE_DATE = GLOBAL_VAR.SYS_DATE
                            WHERE HOST_ID = R_INS_SOURCE.HOST_ID
                              AND ENTITY_ID = R_INS_SOURCE.ENTITY_ID
                              AND CURRENCY_CODE = R_INS_SOURCE.CURRENCY_CODE
                              AND MATCH_ID = VN_RETN_MATCHID
                              AND POSITION_LEVEL = VN_RETN_HI_POS;

                           UPDATE P_MOVEMENT
                              SET PREDICT_STATUS = 'E', UPDATE_USER = 'SYSTEM', UPDATE_DATE = GLOBAL_VAR.SYS_DATE
                            WHERE HOST_ID = R_INS_SOURCE.HOST_ID
                              AND ENTITY_ID = R_INS_SOURCE.ENTITY_ID
                              AND CURRENCY_CODE = R_INS_SOURCE.CURRENCY_CODE
                              AND MATCH_ID = VN_RETN_MATCHID
                              AND POSITION_LEVEL != VN_RETN_HI_POS;
                        ELSE
                           UPDATE P_MOVEMENT
                              SET PREDICT_STATUS = 'I', UPDATE_USER = 'SYSTEM', UPDATE_DATE = GLOBAL_VAR.SYS_DATE
                            WHERE HOST_ID = R_INS_SOURCE.HOST_ID
                              AND ENTITY_ID = R_INS_SOURCE.ENTITY_ID
                              AND CURRENCY_CODE = R_INS_SOURCE.CURRENCY_CODE
                              AND VALUE_DATE = R_INS_SOURCE.VALUE_DATE
                              AND MATCH_ID = VN_RETN_MATCHID
                              AND POSITION_LEVEL = VN_RETN_HI_POS;

                           UPDATE P_MOVEMENT
                              SET PREDICT_STATUS = 'E', UPDATE_USER = 'SYSTEM', UPDATE_DATE = GLOBAL_VAR.SYS_DATE
                            WHERE HOST_ID = R_INS_SOURCE.HOST_ID
                              AND ENTITY_ID = R_INS_SOURCE.ENTITY_ID
                              AND CURRENCY_CODE = R_INS_SOURCE.CURRENCY_CODE
                              AND VALUE_DATE = R_INS_SOURCE.VALUE_DATE
                              AND MATCH_ID = VN_RETN_MATCHID
                              AND POSITION_LEVEL != VN_RETN_HI_POS;
                        END IF;

                        COMMIT;
                     END IF;
                  END IF;
               --<<PRE_ADVICE>>
               EXCEPTION
                  WHEN OTHERS
                  THEN
                     -- close target cursors if it is open
                     IF VV_TARGET_POS_CHK = 'H'
                     THEN
                        IF CR_INSERT_TARGET_SUPP_PASS%ISOPEN = TRUE
                        THEN
                           CLOSE CR_INSERT_TARGET_SUPP_PASS;
                        END IF;
                     ELSE
                        IF L_FORCE_REFERENCE_ONLY = 'Y'
                        THEN
                           IF CR_INS_TARGET_REF_PASS%ISOPEN = TRUE
                           THEN
                              CLOSE CR_INS_TARGET_REF_PASS;
                           END IF;
                        ELSE
                           IF CR_INS_TARGET_VD_AMT_PASS%ISOPEN = TRUE
                           THEN
                              CLOSE CR_INS_TARGET_VD_AMT_PASS;
                           END IF;
                        END IF;
                     END IF;

                     PK_DEBUG.SP_DEBUG_MATCHING_PROCESS('E');
                     -- 'E' Exception
                     VERRORCODE   := SQLCODE;
                     VERRORDESC   := SQLERRM;
                     SP_ERROR_LOG(L_HOSTID,
                                  VUSERID,
                                  VIPADDRESS,
                                  VSOURCE || ' Error for ' || L_ENTITYID || '/' || L_CURRENCYCODE || ' at Location: ' || VV_ERR_LOC,
                                  VERRORCODE,
                                  VERRORDESC);
               END;

               VV_ERR_LOC   := '1560';
               -- Clearing the locked and processing movements
               SP_REMOVE_P_B_TARGETS(R_INS_SOURCE.HOST_ID,
                                     R_INS_SOURCE.ENTITY_ID,
                                     R_INS_SOURCE.CURRENCY_CODE,
                                     'Y',
                                     0,
                                     'S' -- Source cursor end delete
                                        );
               VV_ERR_LOC   := '1570';
            END LOOP; -- FOR INSERT INTO P_SRC_MOVEMENT

            -- close the source cursor
            IF CR_INS_SOURCE%ISOPEN = TRUE
            THEN
               CLOSE CR_INS_SOURCE;
            END IF;
         END IF; -- End of (IF vv_source_pos_exists = 'Y')

         -- Get the maximum position level from o/s movements that have all already crossed the normal to_match_stage
         -- (i.e. greater than stage 1) and have reference agreement with current matched movements.
         -- NOTE: This query has been optimized by breaking down into logical WITH blocks,
         -- which seems to aid Oracle in choosing a more appropriate execution path.
         WITH MATREF
              AS (SELECT DISTINCT X1.CROSS_REFERENCE
                    FROM P_MOVEMENT M1 INNER JOIN P_REFERENCE_XREF X1 ON (M1.MOVEMENT_ID = X1.MOVEMENT_ID)
                   WHERE M1.MATCH_ID = VN_RETN_MATCHID)
         SELECT MAX(M.POSITION_LEVEL)
           INTO GN_MAX_POS_OTHER_STAGE
           FROM P_REFERENCE_XREF X INNER JOIN P_MOVEMENT M ON (X.MOVEMENT_ID = M.MOVEMENT_ID)
          WHERE X.CROSS_REFERENCE IN (SELECT * FROM MATREF)
            AND M.MATCH_STATUS = 'L'
            AND M.TO_MATCH_STAGE > 1
            AND M.POSITION_LEVEL > 6;
      END LOOP; -- FOR_LOOP_1
   EXCEPTION
      WHEN OTHERS
      THEN
         IF CR_INS_SOURCE%ISOPEN = TRUE
         THEN
            CLOSE CR_INS_SOURCE;
         END IF;

         VERRORCODE   := SQLCODE;
         VERRORDESC   := SQLERRM;
         SP_REMOVE_P_B_TARGETS(R_INS_SOURCE.HOST_ID,
                               R_INS_SOURCE.ENTITY_ID,
                               R_INS_SOURCE.CURRENCY_CODE,
                               'Y',
                               0,
                               'X' -- Exception delete
                                  );

         SP_ERROR_LOG(L_HOSTID,
                      VUSERID,
                      VIPADDRESS,
                      VSOURCE || ' Error for ' || L_ENTITYID || '/' || L_CURRENCYCODE || ' at Location: ' || VV_ERR_LOC,
                      VERRORCODE,
                      VERRORDESC);
         L_RESULT     := 1;
   END SP_MATCHING_PROCEDURE;
END PK_MATCHING_PROCESS;
/